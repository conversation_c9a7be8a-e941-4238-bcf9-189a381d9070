# 自动驾驶开发加速系统 - Logstash管道配置

input {
  # 从Filebeat接收日志
  beats {
    port => 5044
    host => "0.0.0.0"
  }

  # 从系统管理服务接收日志
  tcp {
    port => 5000
    codec => json_lines
    tags => ["system-management"]
  }

  # 从开发工具服务接收日志
  tcp {
    port => 5001
    codec => json_lines
    tags => ["development-tools"]
  }

  # 从仿真集成服务接收日志
  tcp {
    port => 5002
    codec => json_lines
    tags => ["simulation-integration"]
  }

  # 从地图编辑服务接收日志
  tcp {
    port => 5003
    codec => json_lines
    tags => ["map-editor"]
  }

  # 从部署运维服务接收日志
  tcp {
    port => 5004
    codec => json_lines
    tags => ["deployment-ops"]
  }

  # 从Nginx接收访问日志
  file {
    path => "/var/log/nginx/access.log"
    start_position => "beginning"
    tags => ["nginx", "access"]
  }

  # 从Nginx接收错误日志
  file {
    path => "/var/log/nginx/error.log"
    start_position => "beginning"
    tags => ["nginx", "error"]
  }

  # 从Docker容器接收日志
  docker {
    host => "unix:///var/run/docker.sock"
    tags => ["docker"]
  }

  # 从Kubernetes接收日志
  http {
    port => 8080
    codec => json
    tags => ["kubernetes"]
  }
}

filter {
  # 处理系统管理服务日志
  if "system-management" in [tags] {
    grok {
      match => { 
        "message" => "%{TIMESTAMP_ISO8601:timestamp} \[%{LOGLEVEL:level}\] %{DATA:logger} - %{GREEDYDATA:message}"
      }
    }
    
    date {
      match => [ "timestamp", "ISO8601" ]
    }
    
    mutate {
      add_field => { "service" => "system-management" }
      add_field => { "service_type" => "backend" }
      add_field => { "language" => "go" }
    }
  }

  # 处理开发工具服务日志
  if "development-tools" in [tags] {
    json {
      source => "message"
    }
    
    date {
      match => [ "timestamp", "ISO8601" ]
    }
    
    mutate {
      add_field => { "service" => "development-tools" }
      add_field => { "service_type" => "backend" }
      add_field => { "language" => "python" }
    }
    
    # 提取项目构建相关信息
    if [event_type] == "build" {
      mutate {
        add_field => { "event_category" => "build" }
      }
      
      if [build_status] == "failed" {
        mutate {
          add_field => { "alert_level" => "warning" }
        }
      }
    }
  }

  # 处理仿真集成服务日志
  if "simulation-integration" in [tags] {
    json {
      source => "message"
    }
    
    date {
      match => [ "timestamp", "ISO8601" ]
    }
    
    mutate {
      add_field => { "service" => "simulation-integration" }
      add_field => { "service_type" => "backend" }
      add_field => { "language" => "python" }
    }
    
    # 提取仿真会话相关信息
    if [event_type] == "simulation" {
      mutate {
        add_field => { "event_category" => "simulation" }
      }
      
      if [session_status] == "error" {
        mutate {
          add_field => { "alert_level" => "error" }
        }
      }
    }
  }

  # 处理地图编辑服务日志
  if "map-editor" in [tags] {
    grok {
      match => { 
        "message" => "%{TIMESTAMP_ISO8601:timestamp} %{LOGLEVEL:level} %{DATA:module} - %{GREEDYDATA:message}"
      }
    }
    
    date {
      match => [ "timestamp", "ISO8601" ]
    }
    
    mutate {
      add_field => { "service" => "map-editor" }
      add_field => { "service_type" => "backend" }
      add_field => { "language" => "rust" }
    }
    
    # 提取地图编辑冲突信息
    if "conflict" in [message] {
      mutate {
        add_field => { "event_category" => "conflict" }
        add_field => { "alert_level" => "warning" }
      }
    }
  }

  # 处理部署运维服务日志
  if "deployment-ops" in [tags] {
    json {
      source => "message"
    }
    
    date {
      match => [ "timestamp", "ISO8601" ]
    }
    
    mutate {
      add_field => { "service" => "deployment-ops" }
      add_field => { "service_type" => "backend" }
      add_field => { "language" => "nodejs" }
    }
    
    # 提取部署相关信息
    if [event_type] == "deployment" {
      mutate {
        add_field => { "event_category" => "deployment" }
      }
      
      if [deployment_status] == "failed" {
        mutate {
          add_field => { "alert_level" => "error" }
        }
      }
    }
  }

  # 处理Nginx访问日志
  if "nginx" in [tags] and "access" in [tags] {
    grok {
      match => { 
        "message" => "%{NGINXACCESS}"
      }
    }
    
    date {
      match => [ "timestamp", "dd/MMM/yyyy:HH:mm:ss Z" ]
    }
    
    mutate {
      add_field => { "service" => "nginx" }
      add_field => { "service_type" => "proxy" }
      convert => { "response" => "integer" }
      convert => { "bytes" => "integer" }
    }
    
    # 标记错误响应
    if [response] >= 400 {
      mutate {
        add_field => { "alert_level" => "warning" }
      }
    }
    
    if [response] >= 500 {
      mutate {
        add_field => { "alert_level" => "error" }
      }
    }
  }

  # 处理Nginx错误日志
  if "nginx" in [tags] and "error" in [tags] {
    grok {
      match => { 
        "message" => "%{DATESTAMP:timestamp} \[%{DATA:level}\] %{NUMBER:pid}#%{NUMBER:tid}: %{GREEDYDATA:message}"
      }
    }
    
    date {
      match => [ "timestamp", "yyyy/MM/dd HH:mm:ss" ]
    }
    
    mutate {
      add_field => { "service" => "nginx" }
      add_field => { "service_type" => "proxy" }
      add_field => { "alert_level" => "error" }
    }
  }

  # 处理Docker容器日志
  if "docker" in [tags] {
    mutate {
      add_field => { "service_type" => "container" }
    }
    
    # 提取容器名称和服务信息
    if [docker][name] {
      mutate {
        add_field => { "container_name" => "%{[docker][name]}" }
      }
    }
  }

  # 处理Kubernetes日志
  if "kubernetes" in [tags] {
    json {
      source => "message"
    }
    
    mutate {
      add_field => { "service_type" => "kubernetes" }
    }
    
    # 提取Pod和Namespace信息
    if [kubernetes][pod_name] {
      mutate {
        add_field => { "pod_name" => "%{[kubernetes][pod_name]}" }
        add_field => { "namespace" => "%{[kubernetes][namespace]}" }
      }
    }
  }

  # 通用字段处理
  mutate {
    add_field => { "[@metadata][index_prefix]" => "autonomous-driving" }
    add_field => { "environment" => "${ENVIRONMENT:production}" }
    add_field => { "cluster" => "${CLUSTER_NAME:main}" }
  }

  # 移除不需要的字段
  mutate {
    remove_field => [ "host", "agent", "ecs", "input", "log" ]
  }

  # 地理位置信息（如果有IP地址）
  if [clientip] {
    geoip {
      source => "clientip"
      target => "geoip"
    }
  }

  # 用户代理解析
  if [agent] {
    useragent {
      source => "agent"
      target => "user_agent"
    }
  }
}

output {
  # 输出到Elasticsearch
  elasticsearch {
    hosts => ["elasticsearch-master-1:9200", "elasticsearch-master-2:9200", "elasticsearch-master-3:9200"]
    user => "elastic"
    password => "${ELASTIC_PASSWORD}"
    ssl => true
    ssl_certificate_verification => false
    
    # 动态索引名称
    index => "%{[@metadata][index_prefix]}-%{service}-%{+YYYY.MM.dd}"
    
    # 索引模板
    template_name => "autonomous-driving-template"
    template => "/usr/share/logstash/templates/autonomous-driving-template.json"
    template_overwrite => true
    
    # 文档类型
    document_type => "_doc"
    
    # 批量设置
    flush_size => 1000
    idle_flush_time => 10
  }

  # 错误日志输出到专门的索引
  if [alert_level] == "error" {
    elasticsearch {
      hosts => ["elasticsearch-master-1:9200", "elasticsearch-master-2:9200", "elasticsearch-master-3:9200"]
      user => "elastic"
      password => "${ELASTIC_PASSWORD}"
      ssl => true
      ssl_certificate_verification => false
      index => "autonomous-driving-errors-%{+YYYY.MM.dd}"
    }
  }

  # 输出到文件（备份）
  file {
    path => "/var/log/logstash/autonomous-driving-%{service}-%{+YYYY-MM-dd}.log"
    codec => json_lines
  }

  # 调试输出（开发环境）
  if "${ENVIRONMENT}" == "development" {
    stdout {
      codec => rubydebug
    }
  }
}
