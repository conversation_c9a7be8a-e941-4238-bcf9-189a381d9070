# 自动驾驶开发加速系统 - Filebeat配置

# 全局配置
name: "autonomous-driving-filebeat"
tags: ["autonomous-driving", "production"]

# 输入配置
filebeat.inputs:
  # 系统管理服务日志
  - type: log
    enabled: true
    paths:
      - /var/log/autonomous-driving/system-management/*.log
    fields:
      service: system-management
      service_type: backend
      language: go
    fields_under_root: true
    multiline.pattern: '^\d{4}-\d{2}-\d{2}'
    multiline.negate: true
    multiline.match: after
    exclude_lines: ['^DEBUG']
    
  # 开发工具服务日志
  - type: log
    enabled: true
    paths:
      - /var/log/autonomous-driving/development-tools/*.log
    fields:
      service: development-tools
      service_type: backend
      language: python
    fields_under_root: true
    json.keys_under_root: true
    json.add_error_key: true
    
  # 仿真集成服务日志
  - type: log
    enabled: true
    paths:
      - /var/log/autonomous-driving/simulation-integration/*.log
    fields:
      service: simulation-integration
      service_type: backend
      language: python
    fields_under_root: true
    json.keys_under_root: true
    json.add_error_key: true
    
  # 地图编辑服务日志
  - type: log
    enabled: true
    paths:
      - /var/log/autonomous-driving/map-editor/*.log
    fields:
      service: map-editor
      service_type: backend
      language: rust
    fields_under_root: true
    multiline.pattern: '^\d{4}-\d{2}-\d{2}'
    multiline.negate: true
    multiline.match: after
    
  # 部署运维服务日志
  - type: log
    enabled: true
    paths:
      - /var/log/autonomous-driving/deployment-ops/*.log
    fields:
      service: deployment-ops
      service_type: backend
      language: nodejs
    fields_under_root: true
    json.keys_under_root: true
    json.add_error_key: true
    
  # Nginx访问日志
  - type: log
    enabled: true
    paths:
      - /var/log/nginx/access.log
    fields:
      service: nginx
      service_type: proxy
      log_type: access
    fields_under_root: true
    
  # Nginx错误日志
  - type: log
    enabled: true
    paths:
      - /var/log/nginx/error.log
    fields:
      service: nginx
      service_type: proxy
      log_type: error
    fields_under_root: true
    multiline.pattern: '^\d{4}/\d{2}/\d{2}'
    multiline.negate: true
    multiline.match: after
    
  # 系统日志
  - type: log
    enabled: true
    paths:
      - /var/log/syslog
      - /var/log/messages
    fields:
      service: system
      service_type: system
      log_type: syslog
    fields_under_root: true
    exclude_lines: ['^DEBUG', '^INFO.*systemd']
    
  # Docker容器日志
  - type: container
    enabled: true
    paths:
      - '/var/lib/docker/containers/*/*.log'
    stream: all
    processors:
      - add_docker_metadata:
          host: "unix:///var/run/docker.sock"
          match_fields: ["container.id"]
          match_pids: ["process.pid", "process.ppid"]
          match_source: true
          match_source_index: 4
          match_short_id: false
          cleanup_timeout: 60
          skip_older: 168h
    fields:
      service_type: container
    fields_under_root: true

# 模块配置
filebeat.modules:
  # Nginx模块
  - module: nginx
    access:
      enabled: true
      var.paths: ["/var/log/nginx/access.log"]
    error:
      enabled: true
      var.paths: ["/var/log/nginx/error.log"]
      
  # 系统模块
  - module: system
    syslog:
      enabled: true
      var.paths: ["/var/log/syslog"]
    auth:
      enabled: true
      var.paths: ["/var/log/auth.log"]
      
  # PostgreSQL模块
  - module: postgresql
    log:
      enabled: true
      var.paths: ["/var/log/postgresql/*.log"]
      
  # Redis模块
  - module: redis
    log:
      enabled: true
      var.paths: ["/var/log/redis/*.log"]

# 处理器配置
processors:
  # 添加主机信息
  - add_host_metadata:
      when.not.contains.tags: forwarded
      
  # 添加Docker元数据
  - add_docker_metadata:
      host: "unix:///var/run/docker.sock"
      
  # 添加Kubernetes元数据
  - add_kubernetes_metadata:
      host: ${NODE_NAME}
      matchers:
        - logs_path:
            logs_path: "/var/log/containers/"
            
  # 删除不需要的字段
  - drop_fields:
      fields: ["agent", "ecs", "host.architecture", "host.os.family"]
      
  # 重命名字段
  - rename:
      fields:
        - from: "message"
          to: "log_message"
      ignore_missing: true
      
  # 添加环境标签
  - add_tags:
      tags: [production, autonomous-driving]
      
  # 条件处理
  - if:
      contains:
        log_message: "ERROR"
    then:
      - add_tags:
          tags: [error]
    else:
      - if:
          contains:
            log_message: "WARN"
        then:
          - add_tags:
              tags: [warning]

# 输出配置
output.logstash:
  hosts: ["logstash-1:5044", "logstash-2:5044", "logstash-3:5044"]
  loadbalance: true
  worker: 2
  compression_level: 3
  escape_html: false
  
  # SSL配置
  ssl.enabled: true
  ssl.certificate_authorities: ["/etc/filebeat/certs/ca.crt"]
  ssl.certificate: "/etc/filebeat/certs/filebeat.crt"
  ssl.key: "/etc/filebeat/certs/filebeat.key"
  ssl.verification_mode: certificate

# 备用输出（当Logstash不可用时）
#output.elasticsearch:
#  hosts: ["elasticsearch-master-1:9200", "elasticsearch-master-2:9200", "elasticsearch-master-3:9200"]
#  username: "filebeat_writer"
#  password: "${FILEBEAT_PASSWORD}"
#  ssl.enabled: true
#  ssl.certificate_authorities: ["/etc/filebeat/certs/ca.crt"]
#  index: "filebeat-autonomous-driving-%{+yyyy.MM.dd}"

# 日志配置
logging.level: info
logging.to_files: true
logging.files:
  path: /var/log/filebeat
  name: filebeat
  keepfiles: 7
  permissions: 0644
  rotateeverybytes: 10485760

# 监控配置
monitoring.enabled: true
monitoring.elasticsearch:
  hosts: ["elasticsearch-master-1:9200", "elasticsearch-master-2:9200", "elasticsearch-master-3:9200"]
  username: "filebeat_monitoring"
  password: "${FILEBEAT_MONITORING_PASSWORD}"
  ssl.enabled: true
  ssl.certificate_authorities: ["/etc/filebeat/certs/ca.crt"]

# 性能配置
queue.mem:
  events: 4096
  flush.min_events: 512
  flush.timeout: 1s

# HTTP端点配置
http.enabled: true
http.host: "0.0.0.0"
http.port: 5066

# 设置配置
setup.template.enabled: true
setup.template.name: "filebeat-autonomous-driving"
setup.template.pattern: "filebeat-autonomous-driving-*"
setup.template.settings:
  index.number_of_shards: 3
  index.number_of_replicas: 1
  index.refresh_interval: "30s"

# ILM配置
setup.ilm.enabled: true
setup.ilm.rollover_alias: "filebeat-autonomous-driving"
setup.ilm.pattern: "{now/d}-000001"
setup.ilm.policy: "filebeat-autonomous-driving-policy"

# Kibana配置
setup.kibana:
  host: "kibana:5601"
  protocol: "https"
  username: "filebeat_setup"
  password: "${FILEBEAT_SETUP_PASSWORD}"
  ssl.enabled: true
  ssl.certificate_authorities: ["/etc/filebeat/certs/ca.crt"]

# 仪表板配置
setup.dashboards.enabled: true
setup.dashboards.directory: "/usr/share/filebeat/kibana"
setup.dashboards.index: "filebeat-autonomous-driving-*"

# 管道配置
setup.pipelines.enabled: true
