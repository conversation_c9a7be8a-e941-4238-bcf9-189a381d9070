# 自动驾驶开发加速系统 - Elasticsearch配置
# 集群配置
cluster.name: autonomous-driving-platform
node.name: elasticsearch-master-1
node.roles: [ master, data, ingest ]

# 网络配置
network.host: 0.0.0.0
http.port: 9200
transport.port: 9300

# 发现配置
discovery.seed_hosts: 
  - elasticsearch-master-1:9300
  - elasticsearch-master-2:9300
  - elasticsearch-master-3:9300

cluster.initial_master_nodes:
  - elasticsearch-master-1
  - elasticsearch-master-2
  - elasticsearch-master-3

# 数据路径配置
path.data: /usr/share/elasticsearch/data
path.logs: /usr/share/elasticsearch/logs

# 内存配置
bootstrap.memory_lock: true

# 安全配置
xpack.security.enabled: true
xpack.security.transport.ssl.enabled: true
xpack.security.transport.ssl.verification_mode: certificate
xpack.security.transport.ssl.client_authentication: required
xpack.security.transport.ssl.keystore.path: elastic-certificates.p12
xpack.security.transport.ssl.truststore.path: elastic-certificates.p12

xpack.security.http.ssl.enabled: true
xpack.security.http.ssl.keystore.path: elastic-certificates.p12

# 监控配置
xpack.monitoring.enabled: true
xpack.monitoring.collection.enabled: true

# 索引生命周期管理
xpack.ilm.enabled: true

# 索引模板配置
index.number_of_shards: 3
index.number_of_replicas: 1
index.refresh_interval: 30s

# 日志级别
logger.root: INFO
logger.org.elasticsearch.transport: WARN
logger.org.elasticsearch.discovery: WARN

# 性能优化配置
indices.memory.index_buffer_size: 20%
indices.memory.min_index_buffer_size: 96mb

# 线程池配置
thread_pool.write.queue_size: 1000
thread_pool.search.queue_size: 1000

# 慢查询日志配置
index.search.slowlog.threshold.query.warn: 10s
index.search.slowlog.threshold.query.info: 5s
index.search.slowlog.threshold.query.debug: 2s
index.search.slowlog.threshold.query.trace: 500ms

index.search.slowlog.threshold.fetch.warn: 1s
index.search.slowlog.threshold.fetch.info: 800ms
index.search.slowlog.threshold.fetch.debug: 500ms
index.search.slowlog.threshold.fetch.trace: 200ms

index.indexing.slowlog.threshold.index.warn: 10s
index.indexing.slowlog.threshold.index.info: 5s
index.indexing.slowlog.threshold.index.debug: 2s
index.indexing.slowlog.threshold.index.trace: 500ms

# 集群设置
cluster.routing.allocation.disk.threshold.enabled: true
cluster.routing.allocation.disk.watermark.low: 85%
cluster.routing.allocation.disk.watermark.high: 90%
cluster.routing.allocation.disk.watermark.flood_stage: 95%

# 索引设置
action.auto_create_index: true
action.destructive_requires_name: true
