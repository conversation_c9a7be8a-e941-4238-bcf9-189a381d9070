# 自动驾驶开发加速系统 - Kibana配置

# 服务器配置
server.port: 5601
server.host: "0.0.0.0"
server.name: "autonomous-driving-kibana"
server.basePath: ""
server.rewriteBasePath: false

# Elasticsearch配置
elasticsearch.hosts: 
  - "https://elasticsearch-master-1:9200"
  - "https://elasticsearch-master-2:9200"
  - "https://elasticsearch-master-3:9200"

elasticsearch.username: "kibana_system"
elasticsearch.password: "${KIBANA_PASSWORD}"

# SSL配置
elasticsearch.ssl.certificateAuthorities: ["/usr/share/kibana/config/certs/ca.crt"]
elasticsearch.ssl.verificationMode: certificate

server.ssl.enabled: true
server.ssl.certificate: "/usr/share/kibana/config/certs/kibana.crt"
server.ssl.key: "/usr/share/kibana/config/certs/kibana.key"

# 安全配置
xpack.security.enabled: true
xpack.security.encryptionKey: "autonomous-driving-platform-kibana-encryption-key-32-chars"
xpack.security.session.idleTimeout: "1h"
xpack.security.session.lifespan: "30d"

# 监控配置
xpack.monitoring.enabled: true
xpack.monitoring.kibana.collection.enabled: true
xpack.monitoring.ui.container.elasticsearch.enabled: true

# 告警配置
xpack.alerting.enabled: true
xpack.actions.enabled: true

# 报告配置
xpack.reporting.enabled: true
xpack.reporting.kibanaServer.hostname: "kibana"
xpack.reporting.kibanaServer.port: 5601
xpack.reporting.kibanaServer.protocol: "https"

# 日志配置
logging.appenders:
  file:
    type: file
    fileName: /var/log/kibana/kibana.log
    layout:
      type: json
  console:
    type: console
    layout:
      type: pattern
      pattern: "[%date][%level][%logger] %message"

logging.loggers:
  - name: root
    appenders: [file, console]
    level: info
  - name: elasticsearch
    level: warn
  - name: elasticsearch.query
    level: warn

# 性能配置
elasticsearch.requestTimeout: 30000
elasticsearch.shardTimeout: 30000
elasticsearch.pingTimeout: 3000

# 索引模式配置
kibana.index: ".kibana"
kibana.defaultAppId: "dashboard"

# 国际化配置
i18n.locale: "zh-CN"

# 地图配置
map.includeElasticMapsService: true
map.tilemap.url: "https://tiles.elastic.co/v2/default/{z}/{x}/{y}.png?elastic_tile_service_tos=agree&my_app_name=kibana"

# 数据视图配置
data.search.aggs.shardDelay.enabled: true

# 保存对象配置
savedObjects.maxImportPayloadBytes: 26214400

# 插件配置
plugins.scanDirs: ["/usr/share/kibana/plugins"]

# 开发配置
dev.basePathProxyTarget: "https://localhost:5601"

# 自定义配置
newsfeed.enabled: false
telemetry.enabled: false
telemetry.optIn: false

# 高级设置
advanced_settings:
  # 默认索引模式
  defaultIndex: "autonomous-driving-*"
  
  # 时间字段
  timepicker:defaultRefreshInterval:
    display: "30 seconds"
    pause: false
    value: 30000
  
  # 发现页面设置
  discover:sampleSize: 500
  discover:sort:defaultOrder: "desc"
  
  # 可视化设置
  visualization:tileMap:maxPrecision: 7
  visualization:colorMapping: {}
  
  # 仪表板设置
  dashboard:defaultDarkMode: false
  
  # 搜索设置
  search:timeout: 600000
  courier:maxConcurrentShardRequests: 5
  
  # 历史记录设置
  history:limit: 10

# 空间配置（多租户）
xpack.spaces.enabled: true
xpack.spaces.maxSpaces: 1000

# Canvas配置
xpack.canvas.enabled: true

# 机器学习配置
xpack.ml.enabled: true

# 图形配置
xpack.graph.enabled: true

# 升级助手配置
xpack.upgrade_assistant.enabled: true

# 索引生命周期管理
xpack.ilm.enabled: true

# 快照和恢复
xpack.snapshot_restore.enabled: true

# 跨集群复制
xpack.ccr.enabled: true

# 索引管理
xpack.index_management.enabled: true

# 远程集群
xpack.remote_clusters.enabled: true

# 转换
xpack.transform.enabled: true

# 数据增强器
xpack.data_enhanced.search.enabled: true

# 企业搜索
xpack.enterprise_search.enabled: false

# Fleet配置
xpack.fleet.enabled: true
xpack.fleet.agents.enabled: true

# 安全解决方案
xpack.securitySolution.enabled: true

# 可观测性
xpack.observability.enabled: true

# APM配置
xpack.apm.enabled: true
xpack.apm.ui.enabled: true

# Uptime监控
xpack.uptime.enabled: true

# 基础设施监控
xpack.infra.enabled: true

# 日志监控
xpack.logs.enabled: true

# 指标监控
xpack.metrics.enabled: true
