# 自动驾驶开发加速系统 - 数据库服务Docker Compose配置
# 包含PostgreSQL主从复制、Redis集群、备份服务等

version: '3.8'

services:
  # PostgreSQL主数据库
  postgres-master:
    image: postgres:15-alpine
    container_name: autodriving-postgres-master
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-autodriving}
      POSTGRES_USER: ${POSTGRES_USER:-autodriving}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-autodriving123}
      POSTGRES_REPLICATION_USER: ${POSTGRES_REPLICATION_USER:-replicator}
      POSTGRES_REPLICATION_PASSWORD: ${POSTGRES_REPLICATION_PASSWORD:-replicator123}
      PGDATA: /var/lib/postgresql/data/pgdata
    ports:
      - "5432:5432"
    volumes:
      # 数据持久化
      - postgres-master-data:/var/lib/postgresql/data
      
      # 初始化脚本
      - ./database/schema:/docker-entrypoint-initdb.d:ro
      
      # PostgreSQL配置
      - ./database/postgres/postgresql.conf:/etc/postgresql/postgresql.conf:ro
      - ./database/postgres/pg_hba.conf:/etc/postgresql/pg_hba.conf:ro
      
      # 日志目录
      - ./logs/postgres:/var/log/postgresql
    command: >
      postgres
      -c config_file=/etc/postgresql/postgresql.conf
      -c hba_file=/etc/postgresql/pg_hba.conf
      -c log_destination=stderr,csvlog
      -c logging_collector=on
      -c log_directory=/var/log/postgresql
      -c log_filename=postgresql-%Y-%m-%d.log
      -c log_rotation_age=1d
      -c log_rotation_size=100MB
    networks:
      - autodriving-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-autodriving} -d ${POSTGRES_DB:-autodriving}"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    labels:
      - "prometheus.io/scrape=true"
      - "prometheus.io/port=9187"

  # PostgreSQL从数据库（只读副本）
  postgres-slave:
    image: postgres:15-alpine
    container_name: autodriving-postgres-slave
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-autodriving}
      POSTGRES_USER: ${POSTGRES_USER:-autodriving}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-autodriving123}
      POSTGRES_MASTER_HOST: postgres-master
      POSTGRES_MASTER_PORT: 5432
      POSTGRES_REPLICATION_USER: ${POSTGRES_REPLICATION_USER:-replicator}
      POSTGRES_REPLICATION_PASSWORD: ${POSTGRES_REPLICATION_PASSWORD:-replicator123}
      PGDATA: /var/lib/postgresql/data/pgdata
    ports:
      - "5433:5432"
    volumes:
      - postgres-slave-data:/var/lib/postgresql/data
      - ./database/postgres/recovery.conf:/var/lib/postgresql/data/recovery.conf:ro
      - ./logs/postgres-slave:/var/log/postgresql
    depends_on:
      - postgres-master
    networks:
      - autodriving-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-autodriving} -d ${POSTGRES_DB:-autodriving}"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s

  # Redis主节点
  redis-master:
    image: redis:7-alpine
    container_name: autodriving-redis-master
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis-master-data:/data
      - ./database/redis/redis.conf:/etc/redis/redis.conf:ro
      - ./logs/redis:/var/log/redis
    command: redis-server /etc/redis/redis.conf
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD:-redis123}
    networks:
      - autodriving-network
    healthcheck:
      test: ["CMD", "redis-cli", "--no-auth-warning", "-a", "${REDIS_PASSWORD:-redis123}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    sysctls:
      - net.core.somaxconn=65535
    ulimits:
      memlock: -1

  # Redis从节点
  redis-slave:
    image: redis:7-alpine
    container_name: autodriving-redis-slave
    restart: unless-stopped
    ports:
      - "6380:6379"
    volumes:
      - redis-slave-data:/data
      - ./database/redis/redis-slave.conf:/etc/redis/redis.conf:ro
      - ./logs/redis-slave:/var/log/redis
    command: redis-server /etc/redis/redis.conf
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD:-redis123}
      - REDIS_MASTER_HOST=redis-master
      - REDIS_MASTER_PORT=6379
    depends_on:
      - redis-master
    networks:
      - autodriving-network
    healthcheck:
      test: ["CMD", "redis-cli", "--no-auth-warning", "-a", "${REDIS_PASSWORD:-redis123}", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Sentinel（高可用）
  redis-sentinel-1:
    image: redis:7-alpine
    container_name: autodriving-redis-sentinel-1
    restart: unless-stopped
    ports:
      - "26379:26379"
    volumes:
      - ./database/redis/sentinel.conf:/etc/redis/sentinel.conf:ro
    command: redis-sentinel /etc/redis/sentinel.conf
    depends_on:
      - redis-master
      - redis-slave
    networks:
      - autodriving-network
    healthcheck:
      test: ["CMD", "redis-cli", "-p", "26379", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis-sentinel-2:
    image: redis:7-alpine
    container_name: autodriving-redis-sentinel-2
    restart: unless-stopped
    ports:
      - "26380:26379"
    volumes:
      - ./database/redis/sentinel.conf:/etc/redis/sentinel.conf:ro
    command: redis-sentinel /etc/redis/sentinel.conf
    depends_on:
      - redis-master
      - redis-slave
    networks:
      - autodriving-network

  redis-sentinel-3:
    image: redis:7-alpine
    container_name: autodriving-redis-sentinel-3
    restart: unless-stopped
    ports:
      - "26381:26379"
    volumes:
      - ./database/redis/sentinel.conf:/etc/redis/sentinel.conf:ro
    command: redis-sentinel /etc/redis/sentinel.conf
    depends_on:
      - redis-master
      - redis-slave
    networks:
      - autodriving-network

  # PostgreSQL监控（postgres_exporter）
  postgres-exporter:
    image: prometheuscommunity/postgres-exporter:latest
    container_name: autodriving-postgres-exporter
    restart: unless-stopped
    ports:
      - "9187:9187"
    environment:
      DATA_SOURCE_NAME: "postgresql://${POSTGRES_USER:-autodriving}:${POSTGRES_PASSWORD:-autodriving123}@postgres-master:5432/${POSTGRES_DB:-autodriving}?sslmode=disable"
    depends_on:
      - postgres-master
    networks:
      - autodriving-network
    labels:
      - "prometheus.io/scrape=true"
      - "prometheus.io/port=9187"

  # Redis监控（redis_exporter）
  redis-exporter:
    image: oliver006/redis_exporter:latest
    container_name: autodriving-redis-exporter
    restart: unless-stopped
    ports:
      - "9121:9121"
    environment:
      REDIS_ADDR: "redis://redis-master:6379"
      REDIS_PASSWORD: ${REDIS_PASSWORD:-redis123}
    depends_on:
      - redis-master
    networks:
      - autodriving-network
    labels:
      - "prometheus.io/scrape=true"
      - "prometheus.io/port=9121"

  # 数据库备份服务
  db-backup:
    image: postgres:15-alpine
    container_name: autodriving-db-backup
    restart: unless-stopped
    volumes:
      - ./backups:/backups
      - ./scripts/backup.sh:/backup.sh:ro
    environment:
      POSTGRES_HOST: postgres-master
      POSTGRES_DB: ${POSTGRES_DB:-autodriving}
      POSTGRES_USER: ${POSTGRES_USER:-autodriving}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-autodriving123}
      BACKUP_SCHEDULE: "0 2 * * *"  # 每天凌晨2点备份
      BACKUP_RETENTION_DAYS: 30
    command: >
      sh -c "
        apk add --no-cache dcron &&
        echo '${BACKUP_SCHEDULE:-0 2 * * *} /backup.sh' | crontab - &&
        crond -f -l 2
      "
    depends_on:
      - postgres-master
    networks:
      - autodriving-network

  # Redis备份服务
  redis-backup:
    image: redis:7-alpine
    container_name: autodriving-redis-backup
    restart: unless-stopped
    volumes:
      - ./backups:/backups
      - ./scripts/redis-backup.sh:/redis-backup.sh:ro
    environment:
      REDIS_HOST: redis-master
      REDIS_PORT: 6379
      REDIS_PASSWORD: ${REDIS_PASSWORD:-redis123}
      BACKUP_SCHEDULE: "0 3 * * *"  # 每天凌晨3点备份
      BACKUP_RETENTION_DAYS: 30
    command: >
      sh -c "
        apk add --no-cache dcron &&
        echo '${BACKUP_SCHEDULE:-0 3 * * *} /redis-backup.sh' | crontab - &&
        crond -f -l 2
      "
    depends_on:
      - redis-master
    networks:
      - autodriving-network

  # pgAdmin（数据库管理界面）
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: autodriving-pgadmin
    restart: unless-stopped
    ports:
      - "5050:80"
    environment:
      PGADMIN_DEFAULT_EMAIL: ${PGADMIN_EMAIL:-<EMAIL>}
      PGADMIN_DEFAULT_PASSWORD: ${PGADMIN_PASSWORD:-admin123}
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    volumes:
      - pgadmin-data:/var/lib/pgadmin
      - ./database/pgadmin/servers.json:/pgadmin4/servers.json:ro
    depends_on:
      - postgres-master
    networks:
      - autodriving-network

  # Redis Commander（Redis管理界面）
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: autodriving-redis-commander
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      REDIS_HOSTS: "master:redis-master:6379:0:${REDIS_PASSWORD:-redis123},slave:redis-slave:6379:0:${REDIS_PASSWORD:-redis123}"
      HTTP_USER: ${REDIS_COMMANDER_USER:-admin}
      HTTP_PASSWORD: ${REDIS_COMMANDER_PASSWORD:-admin123}
    depends_on:
      - redis-master
      - redis-slave
    networks:
      - autodriving-network

# 网络配置
networks:
  autodriving-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷配置
volumes:
  postgres-master-data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/postgres-master

  postgres-slave-data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/postgres-slave

  redis-master-data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/redis-master

  redis-slave-data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/redis-slave

  pgadmin-data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/pgadmin

# 环境变量配置示例
# 创建 .env 文件来设置以下变量：
# POSTGRES_DB=autodriving
# POSTGRES_USER=autodriving
# POSTGRES_PASSWORD=your-strong-postgres-password
# POSTGRES_REPLICATION_USER=replicator
# POSTGRES_REPLICATION_PASSWORD=your-replication-password
# REDIS_PASSWORD=your-strong-redis-password
# PGADMIN_EMAIL=<EMAIL>
# PGADMIN_PASSWORD=your-pgadmin-password
# REDIS_COMMANDER_USER=admin
# REDIS_COMMANDER_PASSWORD=your-redis-commander-password
