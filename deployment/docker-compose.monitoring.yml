# 自动驾驶开发加速系统 - 监控和日志系统Docker Compose配置
# 包含Prometheus、<PERSON><PERSON>、<PERSON><PERSON><PERSON>ack、<PERSON><PERSON><PERSON>等监控组件

version: '3.8'

services:
  # Prometheus监控服务
  prometheus:
    image: prom/prometheus:v2.47.0
    container_name: autodriving-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ./monitoring/prometheus/rules:/etc/prometheus/rules:ro
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    networks:
      - autodriving-network
    labels:
      - "prometheus.io/scrape=false"

  # Grafana可视化服务
  grafana:
    image: grafana/grafana:10.1.0
    container_name: autodriving-grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_USER=${GRAFANA_ADMIN_USER:-admin}
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD:-admin123}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards:ro
    depends_on:
      - prometheus
    networks:
      - autodriving-network
    labels:
      - "prometheus.io/scrape=true"
      - "prometheus.io/port=3000"

  # AlertManager告警管理
  alertmanager:
    image: prom/alertmanager:v0.26.0
    container_name: autodriving-alertmanager
    restart: unless-stopped
    ports:
      - "9093:9093"
    volumes:
      - ./monitoring/alertmanager/alertmanager.yml:/etc/alertmanager/alertmanager.yml:ro
      - alertmanager-data:/alertmanager
    command:
      - '--config.file=/etc/alertmanager/alertmanager.yml'
      - '--storage.path=/alertmanager'
      - '--web.external-url=http://localhost:9093'
    networks:
      - autodriving-network

  # Node Exporter系统指标收集
  node-exporter:
    image: prom/node-exporter:v1.6.1
    container_name: autodriving-node-exporter
    restart: unless-stopped
    ports:
      - "9100:9100"
    volumes:
      - /proc:/host/proc:ro
      - /sys:/host/sys:ro
      - /:/rootfs:ro
    command:
      - '--path.procfs=/host/proc'
      - '--path.rootfs=/rootfs'
      - '--path.sysfs=/host/sys'
      - '--collector.filesystem.mount-points-exclude=^/(sys|proc|dev|host|etc)($$|/)'
    networks:
      - autodriving-network
    labels:
      - "prometheus.io/scrape=true"
      - "prometheus.io/port=9100"

  # cAdvisor容器监控
  cadvisor:
    image: gcr.io/cadvisor/cadvisor:v0.47.2
    container_name: autodriving-cadvisor
    restart: unless-stopped
    ports:
      - "8080:8080"
    volumes:
      - /:/rootfs:ro
      - /var/run:/var/run:ro
      - /sys:/sys:ro
      - /var/lib/docker/:/var/lib/docker:ro
      - /dev/disk/:/dev/disk:ro
    privileged: true
    devices:
      - /dev/kmsg
    networks:
      - autodriving-network
    labels:
      - "prometheus.io/scrape=true"
      - "prometheus.io/port=8080"

  # Elasticsearch日志存储
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.9.0
    container_name: autodriving-elasticsearch
    restart: unless-stopped
    ports:
      - "9200:9200"
      - "9300:9300"
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms1g -Xmx1g"
      - bootstrap.memory_lock=true
    ulimits:
      memlock:
        soft: -1
        hard: -1
    volumes:
      - elasticsearch-data:/usr/share/elasticsearch/data
    networks:
      - autodriving-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Kibana日志可视化
  kibana:
    image: docker.elastic.co/kibana/kibana:8.9.0
    container_name: autodriving-kibana
    restart: unless-stopped
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - XPACK_SECURITY_ENABLED=false
    depends_on:
      - elasticsearch
    networks:
      - autodriving-network
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:5601/api/status || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Logstash日志处理
  logstash:
    image: docker.elastic.co/logstash/logstash:8.9.0
    container_name: autodriving-logstash
    restart: unless-stopped
    ports:
      - "5044:5044"
      - "5000:5000/tcp"
      - "5000:5000/udp"
      - "9600:9600"
    environment:
      - "LS_JAVA_OPTS=-Xmx1g -Xms1g"
    volumes:
      - ./monitoring/logstash/pipeline:/usr/share/logstash/pipeline:ro
      - ./monitoring/logstash/config/logstash.yml:/usr/share/logstash/config/logstash.yml:ro
    depends_on:
      - elasticsearch
    networks:
      - autodriving-network

  # Filebeat日志收集
  filebeat:
    image: docker.elastic.co/beats/filebeat:8.9.0
    container_name: autodriving-filebeat
    restart: unless-stopped
    user: root
    volumes:
      - ./monitoring/filebeat/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./logs:/var/log/app:ro
    environment:
      - ELASTICSEARCH_HOST=elasticsearch:9200
      - KIBANA_HOST=kibana:5601
    depends_on:
      - elasticsearch
      - logstash
    networks:
      - autodriving-network

  # Jaeger链路追踪
  jaeger:
    image: jaegertracing/all-in-one:1.48
    container_name: autodriving-jaeger
    restart: unless-stopped
    ports:
      - "16686:16686"
      - "14268:14268"
      - "14250:14250"
      - "6831:6831/udp"
      - "6832:6832/udp"
    environment:
      - COLLECTOR_OTLP_ENABLED=true
      - SPAN_STORAGE_TYPE=elasticsearch
      - ES_SERVER_URLS=http://elasticsearch:9200
      - ES_TAGS_AS_FIELDS_ALL=true
    depends_on:
      - elasticsearch
    networks:
      - autodriving-network

  # Loki日志聚合
  loki:
    image: grafana/loki:2.9.0
    container_name: autodriving-loki
    restart: unless-stopped
    ports:
      - "3100:3100"
    volumes:
      - ./monitoring/loki/loki.yml:/etc/loki/local-config.yaml:ro
      - loki-data:/loki
    command: -config.file=/etc/loki/local-config.yaml
    networks:
      - autodriving-network

  # Promtail日志收集
  promtail:
    image: grafana/promtail:2.9.0
    container_name: autodriving-promtail
    restart: unless-stopped
    volumes:
      - ./monitoring/promtail/promtail.yml:/etc/promtail/config.yml:ro
      - /var/log:/var/log:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./logs:/var/log/app:ro
    command: -config.file=/etc/promtail/config.yml
    depends_on:
      - loki
    networks:
      - autodriving-network

  # Redis监控
  redis-exporter:
    image: oliver006/redis_exporter:v1.53.0
    container_name: autodriving-redis-exporter
    restart: unless-stopped
    ports:
      - "9121:9121"
    environment:
      - REDIS_ADDR=redis://redis-master:6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
    networks:
      - autodriving-network
    labels:
      - "prometheus.io/scrape=true"
      - "prometheus.io/port=9121"

  # PostgreSQL监控
  postgres-exporter:
    image: prometheuscommunity/postgres-exporter:v0.13.2
    container_name: autodriving-postgres-exporter
    restart: unless-stopped
    ports:
      - "9187:9187"
    environment:
      - DATA_SOURCE_NAME=postgresql://${POSTGRES_USER:-autodriving}:${POSTGRES_PASSWORD:-}@postgres-master:5432/${POSTGRES_DB:-autodriving}?sslmode=disable
    networks:
      - autodriving-network
    labels:
      - "prometheus.io/scrape=true"
      - "prometheus.io/port=9187"

  # Blackbox Exporter外部监控
  blackbox-exporter:
    image: prom/blackbox-exporter:v0.24.0
    container_name: autodriving-blackbox-exporter
    restart: unless-stopped
    ports:
      - "9115:9115"
    volumes:
      - ./monitoring/blackbox/blackbox.yml:/etc/blackbox_exporter/config.yml:ro
    networks:
      - autodriving-network
    labels:
      - "prometheus.io/scrape=true"
      - "prometheus.io/port=9115"

# 网络配置
networks:
  autodriving-network:
    external: true

# 数据卷配置
volumes:
  prometheus-data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/prometheus

  grafana-data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/grafana

  alertmanager-data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/alertmanager

  elasticsearch-data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/elasticsearch

  loki-data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/loki

# 环境变量配置示例
# 创建 .env 文件来设置以下变量：
# GRAFANA_ADMIN_USER=admin
# GRAFANA_ADMIN_PASSWORD=your-grafana-password
# POSTGRES_USER=autodriving
# POSTGRES_PASSWORD=your-postgres-password
# POSTGRES_DB=autodriving
# REDIS_PASSWORD=your-redis-password
