# 自动驾驶开发加速系统 - Helm Values配置

# 全局配置
global:
  # 镜像仓库配置
  imageRegistry: "harbor.autonomous-driving.com"
  imagePullSecrets:
    - name: harbor-registry-secret
  
  # 存储类配置
  storageClass: "fast-ssd"
  
  # 环境配置
  environment: "production"
  
  # 域名配置
  domain: "autonomous-driving-platform.com"
  
  # 安全配置
  security:
    enabled: true
    podSecurityPolicy: true
    networkPolicy: true

# 命名空间配置
namespace:
  create: true
  name: "autonomous-driving-prod"

# 系统管理服务配置
systemManagement:
  enabled: true
  replicaCount: 3
  
  image:
    repository: autonomous-driving/system-management
    tag: "v1.0.0"
    pullPolicy: Always
  
  service:
    type: ClusterIP
    port: 8080
    grpcPort: 9090
    metricsPort: 8081
  
  resources:
    requests:
      cpu: 200m
      memory: 256Mi
    limits:
      cpu: 1000m
      memory: 1Gi
  
  autoscaling:
    enabled: true
    minReplicas: 3
    maxReplicas: 10
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80
  
  config:
    logLevel: "info"
    jwtExpiresIn: "1h"
    rateLimiting:
      enabled: true
      requestsPerSecond: 100
      burst: 200

# 开发工具服务配置
developmentTools:
  enabled: true
  replicaCount: 3
  
  image:
    repository: autonomous-driving/development-tools
    tag: "v1.0.0"
    pullPolicy: Always
  
  service:
    type: ClusterIP
    port: 8081
    metricsPort: 8082
  
  resources:
    requests:
      cpu: 300m
      memory: 512Mi
    limits:
      cpu: 1500m
      memory: 2Gi
  
  autoscaling:
    enabled: true
    minReplicas: 3
    maxReplicas: 15
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80
  
  storage:
    enabled: true
    size: 50Gi
    storageClass: "fast-ssd"

# 仿真集成服务配置
simulationIntegration:
  enabled: true
  replicaCount: 2
  
  image:
    repository: autonomous-driving/simulation-integration
    tag: "v1.0.0"
    pullPolicy: Always
  
  service:
    type: ClusterIP
    port: 8082
    metricsPort: 8083
  
  resources:
    requests:
      cpu: 500m
      memory: 1Gi
    limits:
      cpu: 2000m
      memory: 4Gi
  
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 8
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80

# 地图编辑服务配置
mapEditor:
  enabled: true
  replicaCount: 2
  
  image:
    repository: autonomous-driving/map-editor
    tag: "v1.0.0"
    pullPolicy: Always
  
  service:
    type: ClusterIP
    port: 8083
    metricsPort: 8084
  
  resources:
    requests:
      cpu: 400m
      memory: 512Mi
    limits:
      cpu: 1500m
      memory: 2Gi
  
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 6
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80

# 部署运维服务配置
deploymentOps:
  enabled: true
  replicaCount: 2
  
  image:
    repository: autonomous-driving/deployment-ops
    tag: "v1.0.0"
    pullPolicy: Always
  
  service:
    type: ClusterIP
    port: 8084
    metricsPort: 8085
  
  resources:
    requests:
      cpu: 200m
      memory: 256Mi
    limits:
      cpu: 1000m
      memory: 1Gi
  
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 5
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80

# 前端应用配置
frontend:
  enabled: true
  replicaCount: 3
  
  image:
    repository: autonomous-driving/frontend
    tag: "v1.0.0"
    pullPolicy: Always
  
  service:
    type: ClusterIP
    port: 3000
  
  resources:
    requests:
      cpu: 100m
      memory: 128Mi
    limits:
      cpu: 500m
      memory: 512Mi
  
  autoscaling:
    enabled: true
    minReplicas: 3
    maxReplicas: 10
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80

# API网关配置
apiGateway:
  enabled: true
  replicaCount: 3
  
  image:
    repository: autonomous-driving/api-gateway
    tag: "v1.0.0"
    pullPolicy: Always
  
  service:
    type: ClusterIP
    port: 8080
    metricsPort: 8081
  
  resources:
    requests:
      cpu: 200m
      memory: 256Mi
    limits:
      cpu: 1000m
      memory: 1Gi
  
  autoscaling:
    enabled: true
    minReplicas: 3
    maxReplicas: 10
    targetCPUUtilizationPercentage: 70
    targetMemoryUtilizationPercentage: 80
  
  config:
    rateLimiting:
      enabled: true
      requestsPerSecond: 1000
      burst: 2000

# Ingress配置
ingress:
  enabled: true
  className: "nginx"
  
  annotations:
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "10m"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
  
  hosts:
    - host: autonomous-driving-platform.com
      paths:
        - path: /
          pathType: Prefix
          service: frontend
        - path: /api
          pathType: Prefix
          service: api-gateway
  
  tls:
    - secretName: autonomous-driving-tls
      hosts:
        - autonomous-driving-platform.com

# PostgreSQL数据库配置
postgresql:
  enabled: true
  auth:
    postgresPassword: "your-postgres-password"
    username: "autonomous_driving"
    password: "your-app-password"
    database: "autonomous_driving"
  
  primary:
    persistence:
      enabled: true
      size: 100Gi
      storageClass: "fast-ssd"
    
    resources:
      requests:
        cpu: 1000m
        memory: 2Gi
      limits:
        cpu: 2000m
        memory: 4Gi
  
  readReplicas:
    replicaCount: 2
    persistence:
      enabled: true
      size: 100Gi
      storageClass: "fast-ssd"
    
    resources:
      requests:
        cpu: 500m
        memory: 1Gi
      limits:
        cpu: 1000m
        memory: 2Gi

# Redis缓存配置
redis:
  enabled: true
  auth:
    enabled: true
    password: "your-redis-password"
  
  master:
    persistence:
      enabled: true
      size: 20Gi
      storageClass: "fast-ssd"
    
    resources:
      requests:
        cpu: 200m
        memory: 512Mi
      limits:
        cpu: 500m
        memory: 1Gi
  
  replica:
    replicaCount: 2
    persistence:
      enabled: true
      size: 20Gi
      storageClass: "fast-ssd"
    
    resources:
      requests:
        cpu: 100m
        memory: 256Mi
      limits:
        cpu: 300m
        memory: 512Mi

# 监控配置
monitoring:
  prometheus:
    enabled: true
    retention: "30d"
    storageSize: "50Gi"
    
    resources:
      requests:
        cpu: 500m
        memory: 1Gi
      limits:
        cpu: 2000m
        memory: 4Gi
  
  grafana:
    enabled: true
    adminPassword: "your-grafana-password"
    
    persistence:
      enabled: true
      size: 10Gi
      storageClass: "fast-ssd"
    
    resources:
      requests:
        cpu: 100m
        memory: 128Mi
      limits:
        cpu: 500m
        memory: 512Mi
  
  alertmanager:
    enabled: true
    
    persistence:
      enabled: true
      size: 5Gi
      storageClass: "fast-ssd"

# 日志配置
logging:
  elasticsearch:
    enabled: true
    master:
      replicaCount: 3
      persistence:
        size: 50Gi
        storageClass: "fast-ssd"
      
      resources:
        requests:
          cpu: 1000m
          memory: 2Gi
        limits:
          cpu: 2000m
          memory: 4Gi
    
    data:
      replicaCount: 3
      persistence:
        size: 200Gi
        storageClass: "fast-ssd"
      
      resources:
        requests:
          cpu: 1000m
          memory: 2Gi
        limits:
          cpu: 2000m
          memory: 4Gi
  
  kibana:
    enabled: true
    
    resources:
      requests:
        cpu: 200m
        memory: 512Mi
      limits:
        cpu: 1000m
        memory: 2Gi
  
  logstash:
    enabled: true
    replicaCount: 2
    
    resources:
      requests:
        cpu: 500m
        memory: 1Gi
      limits:
        cpu: 1500m
        memory: 3Gi

# 链路追踪配置
tracing:
  jaeger:
    enabled: true
    
    collector:
      resources:
        requests:
          cpu: 200m
          memory: 256Mi
        limits:
          cpu: 1000m
          memory: 1Gi
    
    query:
      resources:
        requests:
          cpu: 100m
          memory: 128Mi
        limits:
          cpu: 500m
          memory: 512Mi

# 服务发现配置
consul:
  enabled: true
  replicas: 3
  
  resources:
    requests:
      cpu: 100m
      memory: 128Mi
    limits:
      cpu: 500m
      memory: 512Mi

# 密钥管理配置
vault:
  enabled: true
  
  server:
    ha:
      enabled: true
      replicas: 3
    
    resources:
      requests:
        cpu: 200m
        memory: 256Mi
      limits:
        cpu: 1000m
        memory: 1Gi
