# 自动驾驶开发加速系统 - Helm Chart配置
apiVersion: v2
name: autonomous-driving-platform
description: 自动驾驶开发加速系统的Helm Chart，提供完整的微服务架构部署
type: application
version: 1.0.0
appVersion: "1.0.0"

# 关键词
keywords:
  - autonomous-driving
  - development-platform
  - microservices
  - kubernetes
  - golang
  - python
  - rust
  - nodejs

# 主页和源码
home: https://autonomous-driving-platform.com
sources:
  - https://github.com/autonomous-driving-platform/platform

# 维护者信息
maintainers:
  - name: Platform Team
    email: <EMAIL>
    url: https://autonomous-driving-platform.com

# 图标
icon: https://autonomous-driving-platform.com/assets/logo.png

# 依赖项
dependencies:
  # PostgreSQL数据库
  - name: postgresql
    version: "12.1.9"
    repository: https://charts.bitnami.com/bitnami
    condition: postgresql.enabled
    tags:
      - database

  # Redis缓存
  - name: redis
    version: "17.3.7"
    repository: https://charts.bitnami.com/bitnami
    condition: redis.enabled
    tags:
      - cache

  # Nginx Ingress Controller
  - name: ingress-nginx
    version: "4.4.0"
    repository: https://kubernetes.github.io/ingress-nginx
    condition: ingress.enabled
    tags:
      - ingress

  # Prometheus监控
  - name: kube-prometheus-stack
    version: "45.7.1"
    repository: https://prometheus-community.github.io/helm-charts
    condition: monitoring.prometheus.enabled
    tags:
      - monitoring

  # Elasticsearch日志
  - name: elasticsearch
    version: "19.5.0"
    repository: https://charts.bitnami.com/bitnami
    condition: logging.elasticsearch.enabled
    tags:
      - logging

  # Jaeger链路追踪
  - name: jaeger
    version: "0.69.1"
    repository: https://jaegertracing.github.io/helm-charts
    condition: tracing.jaeger.enabled
    tags:
      - tracing

  # Consul服务发现
  - name: consul
    version: "1.0.2"
    repository: https://helm.releases.hashicorp.com
    condition: consul.enabled
    tags:
      - service-discovery

  # Vault密钥管理
  - name: vault
    version: "0.23.0"
    repository: https://helm.releases.hashicorp.com
    condition: vault.enabled
    tags:
      - security

# 注解
annotations:
  # Artifact Hub相关注解
  artifacthub.io/category: "integration-delivery"
  artifacthub.io/license: "MIT"
  artifacthub.io/prerelease: "false"
  artifacthub.io/operator: "false"
  artifacthub.io/containsSecurityUpdates: "true"
  artifacthub.io/changes: |
    - kind: added
      description: 初始版本发布
    - kind: added
      description: 支持系统管理服务
    - kind: added
      description: 支持开发工具服务
    - kind: added
      description: 支持仿真集成服务
    - kind: added
      description: 支持地图编辑服务
    - kind: added
      description: 支持部署运维服务
    - kind: added
      description: 支持前端应用
    - kind: added
      description: 支持API网关
    - kind: added
      description: 支持监控和日志系统
  
  # 自定义注解
  platform.autonomous-driving.com/version: "1.0.0"
  platform.autonomous-driving.com/release-date: "2024-01-15"
  platform.autonomous-driving.com/supported-k8s-versions: ">=1.24.0"
  platform.autonomous-driving.com/components: "system-management,development-tools,simulation-integration,map-editor,deployment-ops,frontend,api-gateway"
