# 自动驾驶开发加速系统 - Nginx代理参数配置
# 统一的代理参数设置，用于所有上游服务

# 基础代理头设置
proxy_set_header Host $http_host;
proxy_set_header X-Real-IP $remote_addr;
proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
proxy_set_header X-Forwarded-Proto $scheme;
proxy_set_header X-Forwarded-Host $host;
proxy_set_header X-Forwarded-Port $server_port;

# 请求标识和追踪
proxy_set_header X-Request-ID $request_id;
proxy_set_header X-Trace-ID $request_id;
proxy_set_header X-Span-ID $request_id;

# 用户信息传递（由JWT认证设置）
proxy_set_header X-User-ID $http_x_user_id;
proxy_set_header X-User-Role $http_x_user_role;
proxy_set_header X-User-Name $http_x_user_name;

# 客户端信息
proxy_set_header User-Agent $http_user_agent;
proxy_set_header Accept $http_accept;
proxy_set_header Accept-Encoding $http_accept_encoding;
proxy_set_header Accept-Language $http_accept_language;

# 连接和超时设置
proxy_connect_timeout 30s;
proxy_send_timeout 60s;
proxy_read_timeout 60s;

# 缓冲设置
proxy_buffering on;
proxy_buffer_size 4k;
proxy_buffers 8 4k;
proxy_busy_buffers_size 8k;
proxy_temp_file_write_size 8k;

# HTTP版本
proxy_http_version 1.1;
proxy_set_header Connection "";

# 重定向处理
proxy_redirect off;

# 错误处理
proxy_intercept_errors on;
proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
proxy_next_upstream_tries 3;
proxy_next_upstream_timeout 30s;
