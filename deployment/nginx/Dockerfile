# 自动驾驶开发加速系统 - API网关Docker镜像
# 基于OpenResty构建，支持Lua脚本和JWT认证

FROM openresty/openresty:********-alpine

# 维护者信息
LABEL maintainer="Autonomous Driving Team <<EMAIL>>"
LABEL description="自动驾驶开发加速系统API网关"
LABEL version="1.0.0"

# 安装必要的包
RUN apk add --no-cache \
    curl \
    wget \
    ca-certificates \
    openssl \
    pcre \
    zlib \
    && rm -rf /var/cache/apk/*

# 安装Lua依赖
RUN /usr/local/openresty/luajit/bin/luarocks install lua-resty-jwt \
    && /usr/local/openresty/luajit/bin/luarocks install lua-resty-redis \
    && /usr/local/openresty/luajit/bin/luarocks install lua-cjson

# 创建必要的目录
RUN mkdir -p /etc/nginx/ssl \
    && mkdir -p /etc/nginx/lua \
    && mkdir -p /var/log/nginx \
    && mkdir -p /var/cache/nginx \
    && mkdir -p /usr/share/nginx/html

# 复制配置文件
COPY nginx.conf /etc/nginx/nginx.conf
COPY proxy_params /etc/nginx/proxy_params
COPY lua/ /etc/nginx/lua/

# 复制SSL证书（如果有的话）
COPY ssl/ /etc/nginx/ssl/

# 复制错误页面
COPY html/ /usr/share/nginx/html/

# 创建nginx用户
RUN addgroup -g 101 -S nginx \
    && adduser -S -D -H -u 101 -h /var/cache/nginx -s /sbin/nologin -G nginx -g nginx nginx

# 设置权限
RUN chown -R nginx:nginx /var/log/nginx \
    && chown -R nginx:nginx /var/cache/nginx \
    && chown -R nginx:nginx /etc/nginx \
    && chmod -R 755 /etc/nginx/lua

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/health || exit 1

# 暴露端口
EXPOSE 80 443

# 启动命令
CMD ["/usr/local/openresty/bin/openresty", "-g", "daemon off;"]
