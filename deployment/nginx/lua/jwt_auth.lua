-- 自动驾驶开发加速系统 - JWT认证Lua脚本
-- 在Nginx中实现JWT令牌验证和用户权限检查

local jwt = require "resty.jwt"
local cjson = require "cjson"
local redis = require "resty.redis"

local _M = {}

-- JWT配置
local JWT_SECRET = os.getenv("JWT_SECRET") or "your-default-secret-key"
local JWT_ALGORITHM = "HS256"
local JWT_CACHE_TTL = 300  -- JWT缓存时间（秒）

-- Redis配置
local REDIS_HOST = os.getenv("REDIS_HOST") or "127.0.0.1"
local REDIS_PORT = tonumber(os.getenv("REDIS_PORT")) or 6379
local REDIS_PASSWORD = os.getenv("REDIS_PASSWORD")
local REDIS_DB = tonumber(os.getenv("REDIS_DB")) or 0

-- 错误响应
local function error_response(status, message)
    ngx.status = status
    ngx.header.content_type = "application/json; charset=utf-8"
    ngx.say(cjson.encode({
        error = message,
        timestamp = ngx.time(),
        path = ngx.var.request_uri
    }))
    ngx.exit(status)
end

-- 成功响应
local function success_response(data)
    ngx.header.content_type = "application/json; charset=utf-8"
    ngx.say(cjson.encode({
        success = true,
        data = data,
        timestamp = ngx.time()
    }))
    ngx.exit(200)
end

-- 连接Redis
local function connect_redis()
    local red = redis:new()
    red:set_timeout(1000) -- 1秒超时
    
    local ok, err = red:connect(REDIS_HOST, REDIS_PORT)
    if not ok then
        ngx.log(ngx.ERR, "连接Redis失败: ", err)
        return nil
    end
    
    if REDIS_PASSWORD then
        local res, err = red:auth(REDIS_PASSWORD)
        if not res then
            ngx.log(ngx.ERR, "Redis认证失败: ", err)
            return nil
        end
    end
    
    if REDIS_DB > 0 then
        local res, err = red:select(REDIS_DB)
        if not res then
            ngx.log(ngx.ERR, "选择Redis数据库失败: ", err)
            return nil
        end
    end
    
    return red
end

-- 从缓存获取JWT验证结果
local function get_jwt_from_cache(token_hash)
    local red = connect_redis()
    if not red then
        return nil
    end
    
    local cache_key = "jwt:verified:" .. token_hash
    local cached_result, err = red:get(cache_key)
    
    -- 关闭Redis连接
    local ok, err = red:set_keepalive(10000, 100)
    if not ok then
        ngx.log(ngx.ERR, "设置Redis连接池失败: ", err)
    end
    
    if cached_result and cached_result ~= ngx.null then
        return cjson.decode(cached_result)
    end
    
    return nil
end

-- 将JWT验证结果存入缓存
local function cache_jwt_result(token_hash, payload)
    local red = connect_redis()
    if not red then
        return
    end
    
    local cache_key = "jwt:verified:" .. token_hash
    local cache_value = cjson.encode(payload)
    
    red:setex(cache_key, JWT_CACHE_TTL, cache_value)
    
    -- 关闭Redis连接
    local ok, err = red:set_keepalive(10000, 100)
    if not ok then
        ngx.log(ngx.ERR, "设置Redis连接池失败: ", err)
    end
end

-- 检查JWT是否在黑名单中
local function is_jwt_blacklisted(jti)
    if not jti then
        return false
    end
    
    local red = connect_redis()
    if not red then
        return false
    end
    
    local blacklist_key = "jwt:blacklist:" .. jti
    local result, err = red:exists(blacklist_key)
    
    -- 关闭Redis连接
    local ok, err = red:set_keepalive(10000, 100)
    if not ok then
        ngx.log(ngx.ERR, "设置Redis连接池失败: ", err)
    end
    
    return result == 1
end

-- 验证JWT令牌
function _M.verify_jwt()
    -- 获取Authorization头
    local auth_header = ngx.var.http_authorization
    if not auth_header then
        error_response(401, "缺少认证令牌")
    end
    
    -- 提取JWT令牌
    local token = string.match(auth_header, "Bearer%s+(.+)")
    if not token then
        error_response(401, "无效的认证头格式")
    end
    
    -- 计算令牌哈希用于缓存
    local token_hash = ngx.md5(token)
    
    -- 尝试从缓存获取验证结果
    local cached_payload = get_jwt_from_cache(token_hash)
    if cached_payload then
        ngx.log(ngx.INFO, "使用缓存的JWT验证结果")
        
        -- 设置用户信息到请求头
        ngx.req.set_header("X-User-ID", cached_payload.user_id)
        ngx.req.set_header("X-User-Role", cached_payload.role)
        ngx.req.set_header("X-User-Name", cached_payload.username)
        ngx.req.set_header("X-User-Email", cached_payload.email)
        
        return
    end
    
    -- 验证JWT令牌
    local jwt_obj = jwt:verify(JWT_SECRET, token, {
        alg = JWT_ALGORITHM
    })
    
    if not jwt_obj.valid then
        ngx.log(ngx.WARN, "JWT验证失败: ", jwt_obj.reason)
        error_response(401, "无效的认证令牌: " .. (jwt_obj.reason or "未知错误"))
    end
    
    local payload = jwt_obj.payload
    
    -- 检查令牌是否过期
    if payload.exp and payload.exp < ngx.time() then
        error_response(401, "认证令牌已过期")
    end
    
    -- 检查令牌是否在黑名单中
    if is_jwt_blacklisted(payload.jti) then
        error_response(401, "认证令牌已被撤销")
    end
    
    -- 验证必要字段
    if not payload.user_id or not payload.role then
        error_response(401, "认证令牌缺少必要信息")
    end
    
    -- 缓存验证结果
    cache_jwt_result(token_hash, payload)
    
    -- 设置用户信息到请求头
    ngx.req.set_header("X-User-ID", payload.user_id)
    ngx.req.set_header("X-User-Role", payload.role)
    ngx.req.set_header("X-User-Name", payload.username or "")
    ngx.req.set_header("X-User-Email", payload.email or "")
    
    ngx.log(ngx.INFO, "JWT验证成功，用户ID: ", payload.user_id, ", 角色: ", payload.role)
end

-- 检查用户权限
function _M.check_permission(required_roles)
    local user_role = ngx.var.http_x_user_role
    
    if not user_role then
        error_response(403, "缺少用户角色信息")
    end
    
    -- 如果没有指定必需角色，则允许所有已认证用户
    if not required_roles or #required_roles == 0 then
        return
    end
    
    -- 检查用户角色是否在允许的角色列表中
    for _, role in ipairs(required_roles) do
        if user_role == role then
            return
        end
    end
    
    error_response(403, "权限不足，需要角色: " .. table.concat(required_roles, ", "))
end

-- 管理员权限检查
function _M.require_admin()
    _M.check_permission({"admin"})
end

-- 开发者权限检查
function _M.require_developer()
    _M.check_permission({"admin", "developer"})
end

-- DevOps权限检查
function _M.require_devops()
    _M.check_permission({"admin", "devops"})
end

-- 用户信息获取
function _M.get_user_info()
    local user_id = ngx.var.http_x_user_id
    local user_role = ngx.var.http_x_user_role
    local user_name = ngx.var.http_x_user_name
    local user_email = ngx.var.http_x_user_email
    
    if not user_id then
        error_response(401, "用户未认证")
    end
    
    success_response({
        user_id = user_id,
        role = user_role,
        username = user_name,
        email = user_email
    })
end

-- 令牌刷新
function _M.refresh_token()
    -- 获取当前用户信息
    local user_id = ngx.var.http_x_user_id
    local user_role = ngx.var.http_x_user_role
    
    if not user_id then
        error_response(401, "用户未认证")
    end
    
    -- 生成新的JWT令牌
    local now = ngx.time()
    local new_payload = {
        user_id = user_id,
        role = user_role,
        username = ngx.var.http_x_user_name,
        email = ngx.var.http_x_user_email,
        iat = now,
        exp = now + 3600, -- 1小时后过期
        jti = ngx.var.request_id -- 使用请求ID作为JWT ID
    }
    
    local new_token = jwt:sign(JWT_SECRET, {
        header = { typ = "JWT", alg = JWT_ALGORITHM },
        payload = new_payload
    })
    
    success_response({
        access_token = new_token,
        token_type = "Bearer",
        expires_in = 3600
    })
end

-- 令牌撤销
function _M.revoke_token()
    local auth_header = ngx.var.http_authorization
    if not auth_header then
        error_response(401, "缺少认证令牌")
    end
    
    local token = string.match(auth_header, "Bearer%s+(.+)")
    if not token then
        error_response(401, "无效的认证头格式")
    end
    
    -- 解析JWT获取JTI
    local jwt_obj = jwt:load_jwt(token)
    if not jwt_obj or not jwt_obj.payload or not jwt_obj.payload.jti then
        error_response(400, "无法解析令牌")
    end
    
    local jti = jwt_obj.payload.jti
    local exp = jwt_obj.payload.exp or (ngx.time() + 3600)
    
    -- 将令牌加入黑名单
    local red = connect_redis()
    if red then
        local blacklist_key = "jwt:blacklist:" .. jti
        local ttl = exp - ngx.time()
        if ttl > 0 then
            red:setex(blacklist_key, ttl, "revoked")
        end
        
        -- 关闭Redis连接
        local ok, err = red:set_keepalive(10000, 100)
        if not ok then
            ngx.log(ngx.ERR, "设置Redis连接池失败: ", err)
        end
    end
    
    success_response({
        message = "令牌已成功撤销"
    })
end

return _M
