# 自动驾驶开发加速系统 - API网关配置
# 基于Nginx实现统一API网关，提供路由、负载均衡、认证、限流等功能

# 全局配置
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

# 事件配置
events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

# HTTP配置
http {
    # 基础配置
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct="$upstream_connect_time" '
                    'uht="$upstream_header_time" urt="$upstream_response_time"';
    
    # API网关专用日志格式
    log_format api_gateway '$remote_addr - $remote_user [$time_local] '
                          '"$request" $status $body_bytes_sent '
                          '"$http_referer" "$http_user_agent" '
                          'service="$upstream_addr" '
                          'request_time=$request_time '
                          'upstream_time=$upstream_response_time '
                          'user_id="$http_x_user_id" '
                          'trace_id="$http_x_trace_id"';
    
    access_log /var/log/nginx/access.log main;
    
    # 性能优化
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 100M;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;
    
    # 限流配置
    # 基于IP的限流：每秒10个请求
    limit_req_zone $binary_remote_addr zone=api_limit_ip:10m rate=10r/s;
    
    # 基于用户的限流：每秒20个请求
    limit_req_zone $http_x_user_id zone=api_limit_user:10m rate=20r/s;
    
    # 连接数限制：每个IP最多10个连接
    limit_conn_zone $binary_remote_addr zone=conn_limit_ip:10m;
    
    # 上游服务器组定义
    
    # 系统管理服务
    upstream system_management {
        least_conn;
        server system-management-1:8080 weight=1 max_fails=3 fail_timeout=30s;
        server system-management-2:8080 weight=1 max_fails=3 fail_timeout=30s;
        server system-management-3:8080 weight=1 max_fails=3 fail_timeout=30s backup;
        
        # 健康检查
        keepalive 32;
        keepalive_requests 100;
        keepalive_timeout 60s;
    }
    
    # 开发工具服务
    upstream development_tools {
        least_conn;
        server development-tools-1:8081 weight=1 max_fails=3 fail_timeout=30s;
        server development-tools-2:8081 weight=1 max_fails=3 fail_timeout=30s;
        
        keepalive 32;
    }
    
    # 仿真集成服务
    upstream simulation_integration {
        ip_hash;  # 使用IP哈希保持会话亲和性
        server simulation-integration-1:8082 weight=1 max_fails=3 fail_timeout=30s;
        server simulation-integration-2:8082 weight=1 max_fails=3 fail_timeout=30s;
        
        keepalive 16;
    }
    
    # 地图编辑服务
    upstream map_editor {
        least_conn;
        server map-editor-1:8083 weight=1 max_fails=3 fail_timeout=30s;
        server map-editor-2:8083 weight=1 max_fails=3 fail_timeout=30s;
        
        keepalive 16;
    }
    
    # 部署运维服务
    upstream devops_service {
        round_robin;
        server devops-service-1:8084 weight=1 max_fails=3 fail_timeout=30s;
        server devops-service-2:8084 weight=1 max_fails=3 fail_timeout=30s;
        
        keepalive 16;
    }
    
    # 前端静态资源服务
    upstream frontend_static {
        server frontend-1:3000 weight=1;
        server frontend-2:3000 weight=1;
        
        keepalive 8;
    }
    
    # Lua脚本路径（用于JWT验证等）
    lua_package_path "/etc/nginx/lua/?.lua;;";
    
    # 共享内存区域（用于缓存JWT等）
    lua_shared_dict jwt_cache 10m;
    lua_shared_dict rate_limit_cache 10m;
    
    # 主服务器配置
    server {
        listen 80;
        listen [::]:80;
        server_name api.autonomous-driving-platform.com;
        
        # 重定向到HTTPS
        return 301 https://$server_name$request_uri;
    }
    
    # HTTPS服务器配置
    server {
        listen 443 ssl http2;
        listen [::]:443 ssl http2;
        server_name api.autonomous-driving-platform.com;
        
        # SSL证书配置
        ssl_certificate /etc/nginx/ssl/api.autonomous-driving-platform.com.crt;
        ssl_certificate_key /etc/nginx/ssl/api.autonomous-driving-platform.com.key;
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;
        
        # 安全头
        add_header X-Frame-Options DENY;
        add_header X-Content-Type-Options nosniff;
        add_header X-XSS-Protection "1; mode=block";
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
        
        # 访问日志
        access_log /var/log/nginx/api_gateway.log api_gateway;
        
        # 全局限流
        limit_req zone=api_limit_ip burst=20 nodelay;
        limit_conn conn_limit_ip 10;
        
        # 健康检查端点
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }
        
        # API版本路由
        location /api/v1/ {
            # JWT认证（使用Lua脚本）
            access_by_lua_block {
                local jwt = require "resty.jwt"
                local jwt_token = ngx.var.http_authorization
                
                if not jwt_token then
                    ngx.status = 401
                    ngx.say('{"error": "缺少认证令牌"}')
                    ngx.exit(401)
                end
                
                -- 移除Bearer前缀
                jwt_token = string.gsub(jwt_token, "Bearer ", "")
                
                -- 验证JWT（这里简化处理，实际应该验证签名）
                local jwt_obj = jwt:verify("your-secret-key", jwt_token)
                if not jwt_obj.valid then
                    ngx.status = 401
                    ngx.say('{"error": "无效的认证令牌"}')
                    ngx.exit(401)
                end
                
                -- 设置用户信息到请求头
                ngx.req.set_header("X-User-ID", jwt_obj.payload.user_id)
                ngx.req.set_header("X-User-Role", jwt_obj.payload.role)
            }
            
            # 基于用户的限流
            limit_req zone=api_limit_user burst=50 nodelay;
            
            # 添加CORS头
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
            add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization";
            
            # 处理预检请求
            if ($request_method = 'OPTIONS') {
                add_header Access-Control-Max-Age 1728000;
                add_header Content-Type 'text/plain; charset=utf-8';
                add_header Content-Length 0;
                return 204;
            }
            
            # 系统管理API
            location /api/v1/system/ {
                proxy_pass http://system_management/;
                include /etc/nginx/proxy_params;
            }
            
            # 开发工具API
            location /api/v1/development/ {
                proxy_pass http://development_tools/;
                include /etc/nginx/proxy_params;
            }
            
            # 仿真集成API
            location /api/v1/simulation/ {
                proxy_pass http://simulation_integration/;
                include /etc/nginx/proxy_params;
                
                # 仿真服务可能需要更长的超时时间
                proxy_read_timeout 300s;
                proxy_send_timeout 300s;
            }
            
            # 地图编辑API
            location /api/v1/maps/ {
                proxy_pass http://map_editor/;
                include /etc/nginx/proxy_params;
                
                # WebSocket支持（用于协作编辑）
                proxy_http_version 1.1;
                proxy_set_header Upgrade $http_upgrade;
                proxy_set_header Connection "upgrade";
            }
            
            # 部署运维API
            location /api/v1/devops/ {
                proxy_pass http://devops_service/;
                include /etc/nginx/proxy_params;
                
                # 需要管理员权限
                access_by_lua_block {
                    local user_role = ngx.var.http_x_user_role
                    if user_role ~= "admin" and user_role ~= "devops" then
                        ngx.status = 403
                        ngx.say('{"error": "权限不足"}')
                        ngx.exit(403)
                    end
                }
            }
        }
        
        # WebSocket代理（用于实时通信）
        location /ws/ {
            proxy_pass http://simulation_integration;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # WebSocket特定配置
            proxy_read_timeout 86400s;
            proxy_send_timeout 86400s;
            proxy_buffering off;
        }
        
        # 静态资源代理
        location / {
            proxy_pass http://frontend_static;
            include /etc/nginx/proxy_params;
            
            # 静态资源缓存
            location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
                expires 1y;
                add_header Cache-Control "public, immutable";
                add_header X-Cache-Status "STATIC";
            }
        }
        
        # 文件上传代理
        location /upload/ {
            client_max_body_size 500M;
            proxy_pass http://development_tools/upload/;
            include /etc/nginx/proxy_params;
            
            # 上传进度支持
            upload_progress uploads 1m;
        }
        
        # 监控和指标端点
        location /metrics {
            # 只允许内网访问
            allow 10.0.0.0/8;
            allow 172.16.0.0/12;
            allow 192.168.0.0/16;
            deny all;
            
            proxy_pass http://system_management/metrics;
            include /etc/nginx/proxy_params;
        }
        
        # 错误页面
        error_page 404 /404.html;
        error_page 500 502 503 504 /50x.html;
        
        location = /404.html {
            root /usr/share/nginx/html;
            internal;
        }
        
        location = /50x.html {
            root /usr/share/nginx/html;
            internal;
        }
    }
    
    # 管理后台服务器（独立域名）
    server {
        listen 443 ssl http2;
        server_name admin.autonomous-driving-platform.com;
        
        # SSL配置（复用）
        ssl_certificate /etc/nginx/ssl/admin.autonomous-driving-platform.com.crt;
        ssl_certificate_key /etc/nginx/ssl/admin.autonomous-driving-platform.com.key;
        ssl_protocols TLSv1.2 TLSv1.3;
        
        # 管理员认证
        access_by_lua_block {
            local user_role = ngx.var.http_x_user_role
            if user_role ~= "admin" then
                ngx.status = 403
                ngx.say('{"error": "需要管理员权限"}')
                ngx.exit(403)
            end
        }
        
        # 管理API
        location /admin/api/ {
            proxy_pass http://system_management/admin/;
            include /etc/nginx/proxy_params;
        }
        
        # 管理界面
        location / {
            proxy_pass http://frontend_static/admin/;
            include /etc/nginx/proxy_params;
        }
    }
}

# 包含其他配置文件
include /etc/nginx/conf.d/*.conf;
