# 自动驾驶开发加速系统 - 数据库高可用部署
apiVersion: v1
kind: Namespace
metadata:
  name: database
  labels:
    name: database
    environment: production

---
# PostgreSQL主库部署
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: postgresql-primary
  namespace: database
  labels:
    app: postgresql
    role: primary
    environment: production
spec:
  serviceName: postgresql-primary
  replicas: 1
  selector:
    matchLabels:
      app: postgresql
      role: primary
  template:
    metadata:
      labels:
        app: postgresql
        role: primary
        environment: production
    spec:
      securityContext:
        runAsUser: 999
        runAsGroup: 999
        fsGroup: 999
      
      containers:
      - name: postgresql
        image: postgres:15-alpine
        imagePullPolicy: IfNotPresent
        
        ports:
        - name: postgresql
          containerPort: 5432
          protocol: TCP
        
        env:
        - name: POSTGRES_DB
          value: "autonomous_driving"
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: postgresql-credentials
              key: username
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgresql-credentials
              key: password
        - name: POSTGRES_REPLICATION_USER
          valueFrom:
            secretKeyRef:
              name: postgresql-credentials
              key: replication_user
        - name: POSTGRES_REPLICATION_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgresql-credentials
              key: replication_password
        - name: PGDATA
          value: "/var/lib/postgresql/data/pgdata"
        
        resources:
          requests:
            cpu: 1000m
            memory: 2Gi
          limits:
            cpu: 2000m
            memory: 4Gi
        
        livenessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - exec pg_isready -U "$POSTGRES_USER" -d "$POSTGRES_DB" -h 127.0.0.1 -p 5432
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 6
        
        readinessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - exec pg_isready -U "$POSTGRES_USER" -d "$POSTGRES_DB" -h 127.0.0.1 -p 5432
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 6
        
        volumeMounts:
        - name: postgresql-data
          mountPath: /var/lib/postgresql/data
        - name: postgresql-config
          mountPath: /etc/postgresql/postgresql.conf
          subPath: postgresql.conf
        - name: postgresql-config
          mountPath: /etc/postgresql/pg_hba.conf
          subPath: pg_hba.conf
        
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
          runAsNonRoot: true
          runAsUser: 999
          capabilities:
            drop:
            - ALL
      
      volumes:
      - name: postgresql-config
        configMap:
          name: postgresql-config
  
  volumeClaimTemplates:
  - metadata:
      name: postgresql-data
    spec:
      accessModes: ["ReadWriteOnce"]
      storageClassName: "fast-ssd"
      resources:
        requests:
          storage: 100Gi

---
# PostgreSQL从库部署
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: postgresql-replica
  namespace: database
  labels:
    app: postgresql
    role: replica
    environment: production
spec:
  serviceName: postgresql-replica
  replicas: 2
  selector:
    matchLabels:
      app: postgresql
      role: replica
  template:
    metadata:
      labels:
        app: postgresql
        role: replica
        environment: production
    spec:
      securityContext:
        runAsUser: 999
        runAsGroup: 999
        fsGroup: 999
      
      containers:
      - name: postgresql
        image: postgres:15-alpine
        imagePullPolicy: IfNotPresent
        
        ports:
        - name: postgresql
          containerPort: 5432
          protocol: TCP
        
        env:
        - name: PGUSER
          valueFrom:
            secretKeyRef:
              name: postgresql-credentials
              key: username
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgresql-credentials
              key: password
        - name: POSTGRES_MASTER_SERVICE
          value: "postgresql-primary"
        - name: POSTGRES_REPLICATION_USER
          valueFrom:
            secretKeyRef:
              name: postgresql-credentials
              key: replication_user
        - name: POSTGRES_REPLICATION_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgresql-credentials
              key: replication_password
        - name: PGDATA
          value: "/var/lib/postgresql/data/pgdata"
        
        command:
        - /bin/bash
        - -c
        - |
          set -e
          
          # 等待主库启动
          until pg_isready -h $POSTGRES_MASTER_SERVICE -p 5432 -U $POSTGRES_REPLICATION_USER; do
            echo "等待主库启动..."
            sleep 2
          done
          
          # 如果数据目录为空，从主库同步数据
          if [ ! -s "$PGDATA/PG_VERSION" ]; then
            echo "从主库同步数据..."
            pg_basebackup -h $POSTGRES_MASTER_SERVICE -D $PGDATA -U $POSTGRES_REPLICATION_USER -v -P -W
            
            # 配置从库
            echo "standby_mode = 'on'" >> $PGDATA/recovery.conf
            echo "primary_conninfo = 'host=$POSTGRES_MASTER_SERVICE port=5432 user=$POSTGRES_REPLICATION_USER'" >> $PGDATA/recovery.conf
            echo "trigger_file = '/tmp/postgresql.trigger'" >> $PGDATA/recovery.conf
          fi
          
          # 启动PostgreSQL
          exec postgres -c config_file=/etc/postgresql/postgresql.conf
        
        resources:
          requests:
            cpu: 500m
            memory: 1Gi
          limits:
            cpu: 1000m
            memory: 2Gi
        
        livenessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - exec pg_isready -U "$PGUSER" -h 127.0.0.1 -p 5432
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 6
        
        readinessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - exec pg_isready -U "$PGUSER" -h 127.0.0.1 -p 5432
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 6
        
        volumeMounts:
        - name: postgresql-data
          mountPath: /var/lib/postgresql/data
        - name: postgresql-config
          mountPath: /etc/postgresql/postgresql.conf
          subPath: postgresql.conf
        - name: postgresql-config
          mountPath: /etc/postgresql/pg_hba.conf
          subPath: pg_hba.conf
        
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
          runAsNonRoot: true
          runAsUser: 999
          capabilities:
            drop:
            - ALL
      
      volumes:
      - name: postgresql-config
        configMap:
          name: postgresql-config
  
  volumeClaimTemplates:
  - metadata:
      name: postgresql-data
    spec:
      accessModes: ["ReadWriteOnce"]
      storageClassName: "fast-ssd"
      resources:
        requests:
          storage: 100Gi

---
# PostgreSQL主库服务
apiVersion: v1
kind: Service
metadata:
  name: postgresql-primary
  namespace: database
  labels:
    app: postgresql
    role: primary
    environment: production
spec:
  type: ClusterIP
  ports:
  - name: postgresql
    port: 5432
    targetPort: postgresql
    protocol: TCP
  selector:
    app: postgresql
    role: primary

---
# PostgreSQL从库服务
apiVersion: v1
kind: Service
metadata:
  name: postgresql-replica
  namespace: database
  labels:
    app: postgresql
    role: replica
    environment: production
spec:
  type: ClusterIP
  ports:
  - name: postgresql
    port: 5432
    targetPort: postgresql
    protocol: TCP
  selector:
    app: postgresql
    role: replica

---
# PostgreSQL配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: postgresql-config
  namespace: database
  labels:
    app: postgresql
    environment: production
data:
  postgresql.conf: |
    # PostgreSQL生产环境配置
    
    # 连接设置
    listen_addresses = '*'
    port = 5432
    max_connections = 200
    
    # 内存设置
    shared_buffers = 1GB
    effective_cache_size = 3GB
    work_mem = 4MB
    maintenance_work_mem = 256MB
    
    # WAL设置
    wal_level = replica
    max_wal_senders = 3
    max_replication_slots = 3
    wal_keep_segments = 32
    
    # 检查点设置
    checkpoint_completion_target = 0.9
    checkpoint_timeout = 15min
    max_wal_size = 2GB
    min_wal_size = 1GB
    
    # 日志设置
    log_destination = 'stderr'
    logging_collector = on
    log_directory = 'pg_log'
    log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
    log_rotation_age = 1d
    log_rotation_size = 100MB
    log_min_duration_statement = 1000
    log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '
    log_checkpoints = on
    log_connections = on
    log_disconnections = on
    log_lock_waits = on
    
    # 性能设置
    random_page_cost = 1.1
    effective_io_concurrency = 200
    
    # 自动清理设置
    autovacuum = on
    autovacuum_max_workers = 3
    autovacuum_naptime = 1min
    
    # 统计设置
    track_activities = on
    track_counts = on
    track_functions = all
    track_io_timing = on
    
  pg_hba.conf: |
    # PostgreSQL客户端认证配置
    
    # TYPE  DATABASE        USER            ADDRESS                 METHOD
    
    # 本地连接
    local   all             all                                     trust
    
    # IPv4本地连接
    host    all             all             127.0.0.1/32            md5
    
    # IPv6本地连接
    host    all             all             ::1/128                 md5
    
    # 集群内连接
    host    all             all             10.0.0.0/8              md5
    
    # 复制连接
    host    replication     replicator      10.0.0.0/8              md5

---
# Redis集群部署
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: redis-cluster
  namespace: database
  labels:
    app: redis
    environment: production
spec:
  serviceName: redis-cluster
  replicas: 6
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
        environment: production
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        imagePullPolicy: IfNotPresent
        
        ports:
        - name: redis
          containerPort: 6379
          protocol: TCP
        - name: cluster
          containerPort: 16379
          protocol: TCP
        
        env:
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: redis-credentials
              key: password
        
        command:
        - redis-server
        - /etc/redis/redis.conf
        - --requirepass
        - $(REDIS_PASSWORD)
        - --cluster-enabled
        - "yes"
        - --cluster-config-file
        - /data/nodes.conf
        - --cluster-node-timeout
        - "5000"
        - --appendonly
        - "yes"
        
        resources:
          requests:
            cpu: 200m
            memory: 512Mi
          limits:
            cpu: 500m
            memory: 1Gi
        
        livenessProbe:
          exec:
            command:
            - redis-cli
            - -a
            - $(REDIS_PASSWORD)
            - ping
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        
        readinessProbe:
          exec:
            command:
            - redis-cli
            - -a
            - $(REDIS_PASSWORD)
            - ping
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        
        volumeMounts:
        - name: redis-data
          mountPath: /data
        - name: redis-config
          mountPath: /etc/redis/redis.conf
          subPath: redis.conf
        
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
          runAsNonRoot: true
          runAsUser: 999
          capabilities:
            drop:
            - ALL
      
      volumes:
      - name: redis-config
        configMap:
          name: redis-config
  
  volumeClaimTemplates:
  - metadata:
      name: redis-data
    spec:
      accessModes: ["ReadWriteOnce"]
      storageClassName: "fast-ssd"
      resources:
        requests:
          storage: 20Gi

---
# Redis服务
apiVersion: v1
kind: Service
metadata:
  name: redis-cluster
  namespace: database
  labels:
    app: redis
    environment: production
spec:
  type: ClusterIP
  clusterIP: None
  ports:
  - name: redis
    port: 6379
    targetPort: redis
    protocol: TCP
  - name: cluster
    port: 16379
    targetPort: cluster
    protocol: TCP
  selector:
    app: redis

---
# Redis配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: redis-config
  namespace: database
  labels:
    app: redis
    environment: production
data:
  redis.conf: |
    # Redis生产环境配置
    
    # 网络设置
    bind 0.0.0.0
    port 6379
    protected-mode yes
    
    # 内存设置
    maxmemory 512mb
    maxmemory-policy allkeys-lru
    
    # 持久化设置
    save 900 1
    save 300 10
    save 60 10000
    
    # AOF设置
    appendonly yes
    appendfsync everysec
    
    # 日志设置
    loglevel notice
    logfile ""
    
    # 客户端设置
    timeout 300
    tcp-keepalive 300
    
    # 安全设置
    rename-command FLUSHDB ""
    rename-command FLUSHALL ""
    rename-command KEYS ""
    rename-command CONFIG "CONFIG_b835c3b4e5f2a3d7c9e1f8a6b2d4e7f9"
