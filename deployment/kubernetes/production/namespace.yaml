# 自动驾驶开发加速系统 - 生产环境命名空间配置
apiVersion: v1
kind: Namespace
metadata:
  name: autonomous-driving-prod
  labels:
    name: autonomous-driving-prod
    environment: production
    project: autonomous-driving-platform
  annotations:
    description: "自动驾驶开发加速系统生产环境"
    contact: "<EMAIL>"
    version: "1.0.0"

---
# 资源配额
apiVersion: v1
kind: ResourceQuota
metadata:
  name: autonomous-driving-quota
  namespace: autonomous-driving-prod
spec:
  hard:
    # 计算资源限制
    requests.cpu: "20"
    requests.memory: 40Gi
    limits.cpu: "40"
    limits.memory: 80Gi
    
    # 存储资源限制
    requests.storage: 500Gi
    persistentvolumeclaims: "20"
    
    # 对象数量限制
    pods: "100"
    services: "20"
    secrets: "50"
    configmaps: "50"
    replicationcontrollers: "20"
    
    # 网络资源限制
    services.loadbalancers: "5"
    services.nodeports: "10"

---
# 网络策略
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: autonomous-driving-network-policy
  namespace: autonomous-driving-prod
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  
  # 入站规则
  ingress:
  # 允许来自同一命名空间的流量
  - from:
    - namespaceSelector:
        matchLabels:
          name: autonomous-driving-prod
  
  # 允许来自Ingress控制器的流量
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
    ports:
    - protocol: TCP
      port: 80
    - protocol: TCP
      port: 443
  
  # 允许来自监控系统的流量
  - from:
    - namespaceSelector:
        matchLabels:
          name: monitoring
    ports:
    - protocol: TCP
      port: 8080
    - protocol: TCP
      port: 9090
  
  # 出站规则
  egress:
  # 允许DNS查询
  - to: []
    ports:
    - protocol: UDP
      port: 53
    - protocol: TCP
      port: 53
  
  # 允许HTTPS出站
  - to: []
    ports:
    - protocol: TCP
      port: 443
  
  # 允许到数据库的连接
  - to:
    - namespaceSelector:
        matchLabels:
          name: database
    ports:
    - protocol: TCP
      port: 5432
    - protocol: TCP
      port: 6379

---
# Pod安全策略
apiVersion: policy/v1beta1
kind: PodSecurityPolicy
metadata:
  name: autonomous-driving-psp
  namespace: autonomous-driving-prod
spec:
  privileged: false
  allowPrivilegeEscalation: false
  requiredDropCapabilities:
    - ALL
  volumes:
    - 'configMap'
    - 'emptyDir'
    - 'projected'
    - 'secret'
    - 'downwardAPI'
    - 'persistentVolumeClaim'
  hostNetwork: false
  hostIPC: false
  hostPID: false
  runAsUser:
    rule: 'MustRunAsNonRoot'
  supplementalGroups:
    rule: 'MustRunAs'
    ranges:
      - min: 1
        max: 65535
  fsGroup:
    rule: 'MustRunAs'
    ranges:
      - min: 1
        max: 65535
  readOnlyRootFilesystem: false

---
# 服务账户
apiVersion: v1
kind: ServiceAccount
metadata:
  name: autonomous-driving-sa
  namespace: autonomous-driving-prod
  labels:
    app: autonomous-driving
    environment: production
automountServiceAccountToken: true

---
# 角色
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: autonomous-driving-role
  namespace: autonomous-driving-prod
rules:
- apiGroups: [""]
  resources: ["pods", "services", "endpoints", "configmaps", "secrets"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["apps"]
  resources: ["deployments", "replicasets"]
  verbs: ["get", "list", "watch"]
- apiGroups: ["extensions"]
  resources: ["ingresses"]
  verbs: ["get", "list", "watch"]

---
# 角色绑定
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: autonomous-driving-rolebinding
  namespace: autonomous-driving-prod
subjects:
- kind: ServiceAccount
  name: autonomous-driving-sa
  namespace: autonomous-driving-prod
roleRef:
  kind: Role
  name: autonomous-driving-role
  apiGroup: rbac.authorization.k8s.io

---
# 限制范围
apiVersion: v1
kind: LimitRange
metadata:
  name: autonomous-driving-limits
  namespace: autonomous-driving-prod
spec:
  limits:
  # Pod限制
  - type: Pod
    max:
      cpu: "4"
      memory: 8Gi
    min:
      cpu: 100m
      memory: 128Mi
  
  # 容器限制
  - type: Container
    default:
      cpu: 500m
      memory: 1Gi
    defaultRequest:
      cpu: 100m
      memory: 128Mi
    max:
      cpu: "2"
      memory: 4Gi
    min:
      cpu: 50m
      memory: 64Mi
  
  # PVC限制
  - type: PersistentVolumeClaim
    max:
      storage: 100Gi
    min:
      storage: 1Gi
