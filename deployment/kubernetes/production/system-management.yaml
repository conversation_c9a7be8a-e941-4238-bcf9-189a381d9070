# 自动驾驶开发加速系统 - 系统管理服务生产环境部署
apiVersion: apps/v1
kind: Deployment
metadata:
  name: system-management
  namespace: autonomous-driving-prod
  labels:
    app: system-management
    component: backend
    environment: production
    version: v1.0.0
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: system-management
  template:
    metadata:
      labels:
        app: system-management
        component: backend
        environment: production
        version: v1.0.0
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8080"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: autonomous-driving-sa
      securityContext:
        runAsNonRoot: true
        runAsUser: 1000
        fsGroup: 1000
      
      containers:
      - name: system-management
        image: harbor.autonomous-driving.com/autonomous-driving/system-management:v1.0.0
        imagePullPolicy: Always
        
        ports:
        - name: http
          containerPort: 8080
          protocol: TCP
        - name: grpc
          containerPort: 9090
          protocol: TCP
        - name: metrics
          containerPort: 8081
          protocol: TCP
        
        env:
        - name: ENVIRONMENT
          value: "production"
        - name: LOG_LEVEL
          value: "info"
        - name: PORT
          value: "8080"
        - name: GRPC_PORT
          value: "9090"
        - name: METRICS_PORT
          value: "8081"
        
        # 数据库配置
        - name: DB_HOST
          valueFrom:
            secretKeyRef:
              name: database-credentials
              key: host
        - name: DB_PORT
          valueFrom:
            secretKeyRef:
              name: database-credentials
              key: port
        - name: DB_NAME
          valueFrom:
            secretKeyRef:
              name: database-credentials
              key: database
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: database-credentials
              key: username
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: database-credentials
              key: password
        
        # Redis配置
        - name: REDIS_HOST
          valueFrom:
            secretKeyRef:
              name: redis-credentials
              key: host
        - name: REDIS_PORT
          valueFrom:
            secretKeyRef:
              name: redis-credentials
              key: port
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: redis-credentials
              key: password
        
        # JWT配置
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: jwt-secret
              key: secret
        - name: JWT_EXPIRES_IN
          value: "1h"
        
        # 服务发现配置
        - name: CONSUL_HOST
          value: "consul.consul.svc.cluster.local"
        - name: CONSUL_PORT
          value: "8500"
        
        resources:
          requests:
            cpu: 200m
            memory: 256Mi
          limits:
            cpu: 1000m
            memory: 1Gi
        
        livenessProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        
        readinessProbe:
          httpGet:
            path: /ready
            port: http
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        
        startupProbe:
          httpGet:
            path: /health
            port: http
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 30
        
        volumeMounts:
        - name: config
          mountPath: /app/config
          readOnly: true
        - name: logs
          mountPath: /app/logs
        
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: 1000
          capabilities:
            drop:
            - ALL
      
      volumes:
      - name: config
        configMap:
          name: system-management-config
      - name: logs
        emptyDir: {}
      
      imagePullSecrets:
      - name: harbor-registry-secret
      
      affinity:
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - weight: 100
            podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: app
                  operator: In
                  values:
                  - system-management
              topologyKey: kubernetes.io/hostname
      
      tolerations:
      - key: "node.kubernetes.io/not-ready"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300
      - key: "node.kubernetes.io/unreachable"
        operator: "Exists"
        effect: "NoExecute"
        tolerationSeconds: 300

---
# 服务配置
apiVersion: v1
kind: Service
metadata:
  name: system-management
  namespace: autonomous-driving-prod
  labels:
    app: system-management
    component: backend
    environment: production
  annotations:
    prometheus.io/scrape: "true"
    prometheus.io/port: "8081"
    prometheus.io/path: "/metrics"
spec:
  type: ClusterIP
  ports:
  - name: http
    port: 8080
    targetPort: http
    protocol: TCP
  - name: grpc
    port: 9090
    targetPort: grpc
    protocol: TCP
  - name: metrics
    port: 8081
    targetPort: metrics
    protocol: TCP
  selector:
    app: system-management

---
# ConfigMap配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: system-management-config
  namespace: autonomous-driving-prod
  labels:
    app: system-management
    component: backend
    environment: production
data:
  app.yaml: |
    server:
      host: "0.0.0.0"
      port: 8080
      read_timeout: 30s
      write_timeout: 30s
      idle_timeout: 120s
    
    grpc:
      host: "0.0.0.0"
      port: 9090
      max_recv_msg_size: 4194304
      max_send_msg_size: 4194304
    
    database:
      max_open_conns: 25
      max_idle_conns: 5
      conn_max_lifetime: 300s
      conn_max_idle_time: 60s
    
    redis:
      max_retries: 3
      min_retry_backoff: 8ms
      max_retry_backoff: 512ms
      dial_timeout: 5s
      read_timeout: 3s
      write_timeout: 3s
      pool_size: 10
      min_idle_conns: 5
      pool_timeout: 4s
      idle_timeout: 300s
    
    jwt:
      issuer: "autonomous-driving-platform"
      audience: "autonomous-driving-users"
    
    logging:
      level: "info"
      format: "json"
      output: "stdout"
    
    metrics:
      enabled: true
      path: "/metrics"
      port: 8081
    
    tracing:
      enabled: true
      jaeger_endpoint: "http://jaeger-collector.monitoring.svc.cluster.local:14268/api/traces"
      service_name: "system-management"
    
    rate_limiting:
      enabled: true
      requests_per_second: 100
      burst: 200
    
    cors:
      allowed_origins:
        - "https://autonomous-driving-platform.com"
        - "https://*.autonomous-driving-platform.com"
      allowed_methods:
        - "GET"
        - "POST"
        - "PUT"
        - "DELETE"
        - "OPTIONS"
      allowed_headers:
        - "Authorization"
        - "Content-Type"
        - "X-Requested-With"
      max_age: 86400

---
# HPA自动扩缩容
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: system-management-hpa
  namespace: autonomous-driving-prod
  labels:
    app: system-management
    component: backend
    environment: production
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: system-management
  minReplicas: 3
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 50
        periodSeconds: 60
      - type: Pods
        value: 2
        periodSeconds: 60
      selectPolicy: Max

---
# PodDisruptionBudget
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: system-management-pdb
  namespace: autonomous-driving-prod
  labels:
    app: system-management
    component: backend
    environment: production
spec:
  minAvailable: 2
  selector:
    matchLabels:
      app: system-management
