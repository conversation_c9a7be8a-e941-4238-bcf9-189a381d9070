# 自动驾驶开发加速系统 - 系统告警规则
groups:
  # 实例可用性告警
  - name: instance.rules
    rules:
      # 实例宕机告警
      - alert: InstanceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "实例 {{ $labels.instance }} 已宕机"
          description: "实例 {{ $labels.instance }} 在过去1分钟内无法访问"

      # 实例重启告警
      - alert: InstanceRestart
        expr: changes(process_start_time_seconds[5m]) > 0
        for: 0m
        labels:
          severity: warning
        annotations:
          summary: "实例 {{ $labels.instance }} 已重启"
          description: "实例 {{ $labels.instance }} 在过去5分钟内重启了"

  # CPU使用率告警
  - name: cpu.rules
    rules:
      # CPU使用率过高
      - alert: HighCpuUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "实例 {{ $labels.instance }} CPU使用率过高"
          description: "实例 {{ $labels.instance }} CPU使用率为 {{ $value }}%，超过80%阈值"

      # CPU使用率严重过高
      - alert: CriticalCpuUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 95
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "实例 {{ $labels.instance }} CPU使用率严重过高"
          description: "实例 {{ $labels.instance }} CPU使用率为 {{ $value }}%，超过95%阈值"

  # 内存使用率告警
  - name: memory.rules
    rules:
      # 内存使用率过高
      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "实例 {{ $labels.instance }} 内存使用率过高"
          description: "实例 {{ $labels.instance }} 内存使用率为 {{ $value }}%，超过80%阈值"

      # 内存使用率严重过高
      - alert: CriticalMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 95
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "实例 {{ $labels.instance }} 内存使用率严重过高"
          description: "实例 {{ $labels.instance }} 内存使用率为 {{ $value }}%，超过95%阈值"

  # 磁盘使用率告警
  - name: disk.rules
    rules:
      # 磁盘空间不足
      - alert: DiskSpaceUsage
        expr: (1 - (node_filesystem_avail_bytes{fstype!="tmpfs"} / node_filesystem_size_bytes{fstype!="tmpfs"})) * 100 > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "实例 {{ $labels.instance }} 磁盘空间不足"
          description: "实例 {{ $labels.instance }} 挂载点 {{ $labels.mountpoint }} 磁盘使用率为 {{ $value }}%，超过80%阈值"

      # 磁盘空间严重不足
      - alert: CriticalDiskSpaceUsage
        expr: (1 - (node_filesystem_avail_bytes{fstype!="tmpfs"} / node_filesystem_size_bytes{fstype!="tmpfs"})) * 100 > 95
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "实例 {{ $labels.instance }} 磁盘空间严重不足"
          description: "实例 {{ $labels.instance }} 挂载点 {{ $labels.mountpoint }} 磁盘使用率为 {{ $value }}%，超过95%阈值"

      # 磁盘IO等待时间过长
      - alert: HighDiskIOWait
        expr: irate(node_cpu_seconds_total{mode="iowait"}[5m]) * 100 > 20
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "实例 {{ $labels.instance }} 磁盘IO等待时间过长"
          description: "实例 {{ $labels.instance }} 磁盘IO等待时间为 {{ $value }}%，超过20%阈值"

  # 网络告警
  - name: network.rules
    rules:
      # 网络接收错误率过高
      - alert: HighNetworkReceiveErrors
        expr: rate(node_network_receive_errs_total[5m]) > 10
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "实例 {{ $labels.instance }} 网络接收错误率过高"
          description: "实例 {{ $labels.instance }} 网卡 {{ $labels.device }} 接收错误率为 {{ $value }} 错误/秒"

      # 网络发送错误率过高
      - alert: HighNetworkTransmitErrors
        expr: rate(node_network_transmit_errs_total[5m]) > 10
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "实例 {{ $labels.instance }} 网络发送错误率过高"
          description: "实例 {{ $labels.instance }} 网卡 {{ $labels.device }} 发送错误率为 {{ $value }} 错误/秒"

  # 负载告警
  - name: load.rules
    rules:
      # 系统负载过高
      - alert: HighSystemLoad
        expr: node_load1 / count by(instance) (node_cpu_seconds_total{mode="idle"}) > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "实例 {{ $labels.instance }} 系统负载过高"
          description: "实例 {{ $labels.instance }} 1分钟平均负载为 {{ $value }}，超过CPU核数的80%"

      # 系统负载严重过高
      - alert: CriticalSystemLoad
        expr: node_load1 / count by(instance) (node_cpu_seconds_total{mode="idle"}) > 1.5
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "实例 {{ $labels.instance }} 系统负载严重过高"
          description: "实例 {{ $labels.instance }} 1分钟平均负载为 {{ $value }}，超过CPU核数的150%"

  # 文件描述符告警
  - name: fd.rules
    rules:
      # 文件描述符使用率过高
      - alert: HighFileDescriptorUsage
        expr: (node_filefd_allocated / node_filefd_maximum) * 100 > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "实例 {{ $labels.instance }} 文件描述符使用率过高"
          description: "实例 {{ $labels.instance }} 文件描述符使用率为 {{ $value }}%，超过80%阈值"

  # 时间同步告警
  - name: time.rules
    rules:
      # 时间偏移过大
      - alert: ClockSkew
        expr: abs(node_timex_offset_seconds) > 0.05
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "实例 {{ $labels.instance }} 时间偏移过大"
          description: "实例 {{ $labels.instance }} 时间偏移为 {{ $value }} 秒，超过50ms阈值"
