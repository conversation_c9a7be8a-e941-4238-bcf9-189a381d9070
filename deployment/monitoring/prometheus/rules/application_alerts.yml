# 自动驾驶开发加速系统 - 应用告警规则
groups:
  # 服务可用性告警
  - name: service.rules
    rules:
      # 服务不可用
      - alert: ServiceDown
        expr: up{job=~"system-management|development-tools|simulation-integration|map-editor|devops-service"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "服务 {{ $labels.job }} 不可用"
          description: "服务 {{ $labels.job }} 在实例 {{ $labels.instance }} 上已停止响应超过1分钟"

      # HTTP健康检查失败
      - alert: HTTPHealthCheckFailed
        expr: probe_success{job="blackbox-http"} == 0
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "HTTP健康检查失败"
          description: "目标 {{ $labels.instance }} HTTP健康检查失败，持续时间超过2分钟"

      # TCP端口检查失败
      - alert: TCPPortCheckFailed
        expr: probe_success{job="blackbox-tcp"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "TCP端口检查失败"
          description: "目标 {{ $labels.instance }} TCP端口检查失败，持续时间超过1分钟"

  # HTTP性能告警
  - name: http.rules
    rules:
      # HTTP响应时间过长
      - alert: HighHTTPResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "HTTP响应时间过长"
          description: "服务 {{ $labels.service }} 的95%分位HTTP响应时间为 {{ $value }}秒，超过1秒阈值"

      # HTTP错误率过高
      - alert: HighHTTPErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) * 100 > 5
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "HTTP错误率过高"
          description: "服务 {{ $labels.service }} 的HTTP 5xx错误率为 {{ $value }}%，超过5%阈值"

      # HTTP请求量异常
      - alert: HTTPRequestRateAnomaly
        expr: |
          (
            rate(http_requests_total[5m]) > 
            (
              avg_over_time(rate(http_requests_total[5m])[1h:5m]) + 
              3 * stddev_over_time(rate(http_requests_total[5m])[1h:5m])
            )
          ) or (
            rate(http_requests_total[5m]) < 
            (
              avg_over_time(rate(http_requests_total[5m])[1h:5m]) - 
              3 * stddev_over_time(rate(http_requests_total[5m])[1h:5m])
            )
          )
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "HTTP请求量异常"
          description: "服务 {{ $labels.service }} 的HTTP请求量异常，当前值为 {{ $value }} 请求/秒"

  # gRPC性能告警
  - name: grpc.rules
    rules:
      # gRPC响应时间过长
      - alert: HighGRPCResponseTime
        expr: histogram_quantile(0.95, rate(grpc_server_handling_seconds_bucket[5m])) > 1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "gRPC响应时间过长"
          description: "服务 {{ $labels.service }} 方法 {{ $labels.grpc_method }} 的95%分位响应时间为 {{ $value }}秒，超过1秒阈值"

      # gRPC错误率过高
      - alert: HighGRPCErrorRate
        expr: rate(grpc_server_handled_total{grpc_code!="OK"}[5m]) / rate(grpc_server_handled_total[5m]) * 100 > 5
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "gRPC错误率过高"
          description: "服务 {{ $labels.service }} 方法 {{ $labels.grpc_method }} 的错误率为 {{ $value }}%，超过5%阈值"

  # 数据库连接告警
  - name: database_connection.rules
    rules:
      # 数据库连接池使用率过高
      - alert: HighDatabaseConnectionPoolUsage
        expr: (db_connections_active / db_connections_max) * 100 > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "数据库连接池使用率过高"
          description: "服务 {{ $labels.service }} 数据库连接池使用率为 {{ $value }}%，超过80%阈值"

      # 数据库连接超时
      - alert: DatabaseConnectionTimeout
        expr: rate(db_connection_timeouts_total[5m]) > 0
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "数据库连接超时"
          description: "服务 {{ $labels.service }} 出现数据库连接超时，速率为 {{ $value }} 次/秒"

  # 缓存性能告警
  - name: cache.rules
    rules:
      # 缓存命中率过低
      - alert: LowCacheHitRate
        expr: (rate(cache_hits_total[5m]) / (rate(cache_hits_total[5m]) + rate(cache_misses_total[5m]))) * 100 < 80
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "缓存命中率过低"
          description: "服务 {{ $labels.service }} 缓存命中率为 {{ $value }}%，低于80%阈值"

  # 队列告警
  - name: queue.rules
    rules:
      # 队列长度过长
      - alert: HighQueueLength
        expr: queue_length > 1000
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "队列长度过长"
          description: "服务 {{ $labels.service }} 队列 {{ $labels.queue_name }} 长度为 {{ $value }}，超过1000阈值"

      # 队列处理延迟过高
      - alert: HighQueueProcessingDelay
        expr: queue_processing_delay_seconds > 60
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "队列处理延迟过高"
          description: "服务 {{ $labels.service }} 队列 {{ $labels.queue_name }} 处理延迟为 {{ $value }}秒，超过60秒阈值"

  # 内存泄漏告警
  - name: memory_leak.rules
    rules:
      # 内存使用量持续增长
      - alert: MemoryLeakSuspected
        expr: increase(process_resident_memory_bytes[1h]) > 100 * 1024 * 1024
        for: 2h
        labels:
          severity: warning
        annotations:
          summary: "疑似内存泄漏"
          description: "服务 {{ $labels.service }} 在过去1小时内内存使用量增长了 {{ $value | humanize1024 }}B，疑似内存泄漏"

  # 垃圾回收告警
  - name: gc.rules
    rules:
      # GC时间过长
      - alert: HighGCTime
        expr: rate(go_gc_duration_seconds_sum[5m]) / rate(go_gc_duration_seconds_count[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "GC时间过长"
          description: "服务 {{ $labels.service }} 平均GC时间为 {{ $value }}秒，超过0.1秒阈值"

      # GC频率过高
      - alert: HighGCFrequency
        expr: rate(go_gc_duration_seconds_count[5m]) > 10
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "GC频率过高"
          description: "服务 {{ $labels.service }} GC频率为 {{ $value }} 次/秒，超过10次/秒阈值"

  # 协程告警
  - name: goroutine.rules
    rules:
      # 协程数量过多
      - alert: HighGoroutineCount
        expr: go_goroutines > 10000
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "协程数量过多"
          description: "服务 {{ $labels.service }} 协程数量为 {{ $value }}，超过10000阈值"
