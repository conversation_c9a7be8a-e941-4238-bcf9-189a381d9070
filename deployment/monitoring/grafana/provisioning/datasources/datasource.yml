# 自动驾驶开发加速系统 - Grafana数据源配置
apiVersion: 1

datasources:
  # Prometheus数据源
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
    jsonData:
      httpMethod: POST
      manageAlerts: true
      prometheusType: Prometheus
      prometheusVersion: 2.47.0
      cacheLevel: 'High'
      disableRecordingRules: false
      incrementalQueryOverlapWindow: 10m
      queryTimeout: 60s
      timeInterval: 15s
    secureJsonData: {}

  # Loki日志数据源
  - name: Loki
    type: loki
    access: proxy
    url: http://loki:3100
    editable: true
    jsonData:
      maxLines: 1000
      derivedFields:
        - datasourceUid: jaeger
          matcherRegex: "traceID=(\\w+)"
          name: TraceID
          url: "$${__value.raw}"

  # Jaeger链路追踪数据源
  - name: Jaeger
    type: jaeger
    uid: jaeger
    access: proxy
    url: http://jaeger:16686
    editable: true
    jsonData:
      tracesToLogs:
        datasourceUid: loki
        tags: ['job', 'instance', 'pod', 'namespace']
        mappedTags: [{ key: 'service.name', value: 'service' }]
        mapTagNamesEnabled: false
        spanStartTimeShift: '1h'
        spanEndTimeShift: '1h'
        filterByTraceID: false
        filterBySpanID: false

  # Elasticsearch数据源
  - name: Elasticsearch
    type: elasticsearch
    access: proxy
    url: http://elasticsearch:9200
    database: "logstash-*"
    editable: true
    jsonData:
      interval: Daily
      timeField: "@timestamp"
      esVersion: "8.9.0"
      maxConcurrentShardRequests: 5
      logMessageField: message
      logLevelField: level

  # PostgreSQL数据源（用于业务数据查询）
  - name: PostgreSQL
    type: postgres
    access: proxy
    url: postgres-master:5432
    database: autodriving
    user: autodriving
    editable: true
    jsonData:
      sslmode: disable
      maxOpenConns: 100
      maxIdleConns: 100
      maxIdleConnsAuto: true
      connMaxLifetime: 14400
      postgresVersion: 1500
      timescaledb: false
    secureJsonData:
      password: ${POSTGRES_PASSWORD}

  # Redis数据源
  - name: Redis
    type: redis-datasource
    access: proxy
    url: redis://redis-master:6379
    editable: true
    jsonData:
      client: standalone
      poolSize: 5
      timeout: 10
      pingInterval: 0
      pipelineWindow: 0
    secureJsonData:
      password: ${REDIS_PASSWORD}

  # InfluxDB数据源（可选，用于时序数据）
  - name: InfluxDB
    type: influxdb
    access: proxy
    url: http://influxdb:8086
    database: autodriving
    user: autodriving
    editable: true
    jsonData:
      version: Flux
      organization: autodriving
      defaultBucket: metrics
      tlsSkipVerify: true
    secureJsonData:
      token: ${INFLUXDB_TOKEN}

  # TestData数据源（用于测试）
  - name: TestData
    type: testdata
    access: proxy
    editable: true
    jsonData: {}

# 删除数据源配置
deleteDatasources:
  - name: TestData
    orgId: 1
