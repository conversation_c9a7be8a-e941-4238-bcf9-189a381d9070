# 自动驾驶开发加速系统 - AlertManager告警配置
global:
  # SMTP配置
  smtp_smarthost: 'smtp.qq.com:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'your-email-password'
  smtp_require_tls: true

  # 钉钉机器人配置
  wechat_api_url: 'https://qyapi.weixin.qq.com/cgi-bin/'
  wechat_api_secret: 'your-wechat-secret'
  wechat_api_corp_id: 'your-corp-id'

# 模板配置
templates:
  - '/etc/alertmanager/templates/*.tmpl'

# 路由配置
route:
  # 默认分组
  group_by: ['alertname', 'cluster', 'service']
  
  # 分组等待时间
  group_wait: 10s
  
  # 分组间隔时间
  group_interval: 10s
  
  # 重复告警间隔
  repeat_interval: 1h
  
  # 默认接收器
  receiver: 'default'
  
  # 子路由
  routes:
    # 严重告警立即发送
    - match:
        severity: critical
      receiver: 'critical-alerts'
      group_wait: 0s
      group_interval: 5s
      repeat_interval: 30m
      
    # 系统告警
    - match:
        alertname: 'InstanceDown'
      receiver: 'system-alerts'
      group_wait: 5s
      repeat_interval: 15m
      
    # 数据库告警
    - match_re:
        service: '(postgres|redis|elasticsearch)'
      receiver: 'database-alerts'
      group_wait: 10s
      repeat_interval: 30m
      
    # 应用告警
    - match_re:
        service: '(system-management|development-tools|simulation-integration|map-editor|devops-service)'
      receiver: 'application-alerts'
      group_wait: 15s
      repeat_interval: 1h
      
    # 网络告警
    - match:
        alertname: 'BlackboxProbeFailed'
      receiver: 'network-alerts'
      group_wait: 5s
      repeat_interval: 10m

# 抑制规则
inhibit_rules:
  # 如果实例宕机，抑制该实例的其他告警
  - source_match:
      alertname: 'InstanceDown'
    target_match_re:
      alertname: '(HighCpuUsage|HighMemoryUsage|DiskSpaceUsage)'
    equal: ['instance']
    
  # 如果服务不可用，抑制该服务的其他告警
  - source_match:
      alertname: 'ServiceDown'
    target_match_re:
      alertname: '(HighResponseTime|HighErrorRate)'
    equal: ['service']

# 接收器配置
receivers:
  # 默认接收器
  - name: 'default'
    email_configs:
      - to: '<EMAIL>'
        subject: '[自动驾驶系统] {{ .GroupLabels.alertname }} 告警'
        body: |
          {{ range .Alerts }}
          告警名称: {{ .Annotations.summary }}
          告警详情: {{ .Annotations.description }}
          告警级别: {{ .Labels.severity }}
          告警时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ if .Labels.instance }}实例: {{ .Labels.instance }}{{ end }}
          {{ if .Labels.service }}服务: {{ .Labels.service }}{{ end }}
          {{ end }}

  # 严重告警接收器
  - name: 'critical-alerts'
    email_configs:
      - to: '<EMAIL>,<EMAIL>'
        subject: '[紧急] {{ .GroupLabels.alertname }} 严重告警'
        body: |
          🚨 严重告警 🚨
          
          {{ range .Alerts }}
          告警名称: {{ .Annotations.summary }}
          告警详情: {{ .Annotations.description }}
          告警级别: {{ .Labels.severity }}
          告警时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ if .Labels.instance }}实例: {{ .Labels.instance }}{{ end }}
          {{ if .Labels.service }}服务: {{ .Labels.service }}{{ end }}
          
          请立即处理！
          {{ end }}
    
    # 钉钉通知
    webhook_configs:
      - url: 'http://webhook-dingtalk:8080/webhook/dingtalk'
        send_resolved: true
        http_config:
          basic_auth:
            username: 'webhook'
            password: 'webhook-password'

  # 系统告警接收器
  - name: 'system-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '[系统告警] {{ .GroupLabels.alertname }}'
        body: |
          系统告警通知
          
          {{ range .Alerts }}
          告警名称: {{ .Annotations.summary }}
          告警详情: {{ .Annotations.description }}
          告警级别: {{ .Labels.severity }}
          告警时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          实例: {{ .Labels.instance }}
          {{ end }}

  # 数据库告警接收器
  - name: 'database-alerts'
    email_configs:
      - to: '<EMAIL>,<EMAIL>'
        subject: '[数据库告警] {{ .GroupLabels.alertname }}'
        body: |
          数据库告警通知
          
          {{ range .Alerts }}
          告警名称: {{ .Annotations.summary }}
          告警详情: {{ .Annotations.description }}
          告警级别: {{ .Labels.severity }}
          告警时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          服务: {{ .Labels.service }}
          实例: {{ .Labels.instance }}
          {{ end }}

  # 应用告警接收器
  - name: 'application-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '[应用告警] {{ .GroupLabels.alertname }}'
        body: |
          应用告警通知
          
          {{ range .Alerts }}
          告警名称: {{ .Annotations.summary }}
          告警详情: {{ .Annotations.description }}
          告警级别: {{ .Labels.severity }}
          告警时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          服务: {{ .Labels.service }}
          实例: {{ .Labels.instance }}
          {{ end }}

  # 网络告警接收器
  - name: 'network-alerts'
    email_configs:
      - to: '<EMAIL>,<EMAIL>'
        subject: '[网络告警] {{ .GroupLabels.alertname }}'
        body: |
          网络告警通知
          
          {{ range .Alerts }}
          告警名称: {{ .Annotations.summary }}
          告警详情: {{ .Annotations.description }}
          告警级别: {{ .Labels.severity }}
          告警时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          目标: {{ .Labels.instance }}
          {{ end }}

  # Slack通知（可选）
  - name: 'slack-alerts'
    slack_configs:
      - api_url: 'https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK'
        channel: '#alerts'
        username: 'AlertManager'
        icon_emoji: ':warning:'
        title: '{{ .GroupLabels.alertname }} 告警'
        text: |
          {{ range .Alerts }}
          {{ .Annotations.summary }}
          {{ .Annotations.description }}
          {{ end }}
        send_resolved: true

  # 企业微信通知（可选）
  - name: 'wechat-alerts'
    wechat_configs:
      - corp_id: 'your-corp-id'
        api_secret: 'your-api-secret'
        to_user: '@all'
        agent_id: 'your-agent-id'
        message: |
          {{ range .Alerts }}
          告警: {{ .Annotations.summary }}
          详情: {{ .Annotations.description }}
          时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ end }}
        send_resolved: true
