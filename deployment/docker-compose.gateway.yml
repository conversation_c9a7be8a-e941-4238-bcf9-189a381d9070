# 自动驾驶开发加速系统 - API网关Docker Compose配置
# 包含API网关、Redis缓存和相关服务

version: '3.8'

services:
  # API网关服务
  api-gateway:
    build:
      context: ./nginx
      dockerfile: Dockerfile
    container_name: autodriving-api-gateway
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      # 配置文件挂载
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/proxy_params:/etc/nginx/proxy_params:ro
      - ./nginx/lua:/etc/nginx/lua:ro
      
      # SSL证书挂载
      - ./nginx/ssl:/etc/nginx/ssl:ro
      
      # 日志挂载
      - ./logs/nginx:/var/log/nginx
      
      # 静态文件挂载
      - ./nginx/html:/usr/share/nginx/html:ro
    environment:
      # JWT配置
      - JWT_SECRET=${JWT_SECRET:-your-default-secret-key}
      
      # Redis配置
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
      - REDIS_DB=0
      
      # 日志级别
      - NGINX_LOG_LEVEL=warn
    depends_on:
      - redis
    networks:
      - autodriving-network
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.api-gateway.rule=Host(`api.autonomous-driving-platform.com`)"
      - "traefik.http.routers.api-gateway.tls=true"
      - "traefik.http.services.api-gateway.loadbalancer.server.port=80"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: autodriving-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
      - ./redis/redis.conf:/etc/redis/redis.conf:ro
    command: redis-server /etc/redis/redis.conf
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
    networks:
      - autodriving-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    sysctls:
      - net.core.somaxconn=65535

  # Redis Sentinel（高可用配置）
  redis-sentinel:
    image: redis:7-alpine
    container_name: autodriving-redis-sentinel
    restart: unless-stopped
    ports:
      - "26379:26379"
    volumes:
      - ./redis/sentinel.conf:/etc/redis/sentinel.conf:ro
    command: redis-sentinel /etc/redis/sentinel.conf
    depends_on:
      - redis
    networks:
      - autodriving-network
    healthcheck:
      test: ["CMD", "redis-cli", "-p", "26379", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Prometheus Exporter（监控）
  nginx-exporter:
    image: nginx/nginx-prometheus-exporter:0.11
    container_name: autodriving-nginx-exporter
    restart: unless-stopped
    ports:
      - "9113:9113"
    command:
      - -nginx.scrape-uri=http://api-gateway/nginx_status
    depends_on:
      - api-gateway
    networks:
      - autodriving-network
    labels:
      - "prometheus.io/scrape=true"
      - "prometheus.io/port=9113"

  # 日志收集器（Fluent Bit）
  fluent-bit:
    image: fluent/fluent-bit:2.1
    container_name: autodriving-fluent-bit
    restart: unless-stopped
    volumes:
      - ./logs/nginx:/var/log/nginx:ro
      - ./fluent-bit/fluent-bit.conf:/fluent-bit/etc/fluent-bit.conf:ro
      - ./fluent-bit/parsers.conf:/fluent-bit/etc/parsers.conf:ro
    depends_on:
      - api-gateway
    networks:
      - autodriving-network
    environment:
      - ELASTICSEARCH_HOST=${ELASTICSEARCH_HOST:-elasticsearch}
      - ELASTICSEARCH_PORT=${ELASTICSEARCH_PORT:-9200}

  # 证书自动更新服务（Let's Encrypt）
  certbot:
    image: certbot/certbot:latest
    container_name: autodriving-certbot
    volumes:
      - ./nginx/ssl:/etc/letsencrypt
      - ./nginx/html:/var/www/html
    command: >
      sh -c "
        while :; do
          certbot renew --webroot --webroot-path=/var/www/html --quiet
          sleep 12h
        done
      "
    depends_on:
      - api-gateway
    networks:
      - autodriving-network

# 网络配置
networks:
  autodriving-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷配置
volumes:
  redis-data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data/redis

# 环境变量配置
# 创建 .env 文件来设置以下变量：
# JWT_SECRET=your-super-secret-jwt-key
# REDIS_PASSWORD=your-redis-password
# ELASTICSEARCH_HOST=elasticsearch.example.com
# ELASTICSEARCH_PORT=9200
