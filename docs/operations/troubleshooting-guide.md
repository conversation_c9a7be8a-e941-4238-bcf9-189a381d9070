# 自动驾驶开发加速系统 - 故障排查指南

## 概述

本文档提供自动驾驶开发加速系统常见故障的排查方法和解决方案，帮助运维人员快速定位和解决问题。

## 目录

1. [系统监控和诊断](#系统监控和诊断)
2. [服务故障排查](#服务故障排查)
3. [数据库问题](#数据库问题)
4. [网络连接问题](#网络连接问题)
5. [性能问题](#性能问题)
6. [安全问题](#安全问题)
7. [日志分析](#日志分析)

## 系统监控和诊断

### 快速健康检查

```bash
#!/bin/bash
# 系统健康检查脚本

echo "=== 系统健康检查 ==="
echo "检查时间: $(date)"
echo

# 检查系统资源
echo "1. 系统资源状态:"
echo "CPU使用率:"
top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1

echo "内存使用率:"
free -m | awk 'NR==2{printf "%.2f%%\n", $3*100/$2}'

echo "磁盘使用率:"
df -h | grep -vE '^Filesystem|tmpfs|cdrom' | awk '{print $5 " " $1}'

echo

# 检查Kubernetes集群状态
echo "2. Kubernetes集群状态:"
kubectl get nodes
kubectl get pods -n autonomous-driving-prod --field-selector=status.phase!=Running

echo

# 检查服务状态
echo "3. 核心服务状态:"
kubectl get services -n autonomous-driving-prod
kubectl get ingress -n autonomous-driving-prod

echo

# 检查数据库连接
echo "4. 数据库连接状态:"
kubectl exec -n database postgresql-primary-0 -- pg_isready
kubectl exec -n database redis-cluster-0 -- redis-cli ping

echo "=== 健康检查完成 ==="
```

### 监控指标检查

```bash
# 检查Prometheus指标
curl -s http://prometheus:9090/api/v1/query?query=up | jq '.data.result[] | select(.value[1] == "0")'

# 检查关键服务的响应时间
curl -s http://prometheus:9090/api/v1/query?query='histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))'

# 检查错误率
curl -s http://prometheus:9090/api/v1/query?query='rate(http_requests_total{status=~"5.."}[5m])'
```

## 服务故障排查

### Pod启动失败

**症状**: Pod处于Pending、CrashLoopBackOff或Error状态

**排查步骤**:

1. **查看Pod状态**:
```bash
kubectl get pods -n autonomous-driving-prod
kubectl describe pod <pod-name> -n autonomous-driving-prod
```

2. **查看Pod日志**:
```bash
kubectl logs <pod-name> -n autonomous-driving-prod
kubectl logs <pod-name> -n autonomous-driving-prod --previous
```

3. **检查资源限制**:
```bash
kubectl top pods -n autonomous-driving-prod
kubectl describe node <node-name>
```

**常见问题和解决方案**:

| 问题 | 原因 | 解决方案 |
|------|------|----------|
| ImagePullBackOff | 镜像拉取失败 | 检查镜像名称和仓库权限 |
| CrashLoopBackOff | 应用启动失败 | 检查应用配置和依赖 |
| Pending | 资源不足 | 检查节点资源和调度策略 |
| OOMKilled | 内存不足 | 增加内存限制或优化应用 |

### 服务无法访问

**症状**: 服务返回502、503或连接超时

**排查步骤**:

1. **检查服务和端点**:
```bash
kubectl get services -n autonomous-driving-prod
kubectl get endpoints -n autonomous-driving-prod
kubectl describe service <service-name> -n autonomous-driving-prod
```

2. **检查Ingress配置**:
```bash
kubectl get ingress -n autonomous-driving-prod
kubectl describe ingress <ingress-name> -n autonomous-driving-prod
```

3. **测试服务连通性**:
```bash
# 在集群内测试
kubectl run test-pod --image=curlimages/curl -it --rm -- /bin/sh
curl http://<service-name>.<namespace>.svc.cluster.local:<port>/health

# 检查DNS解析
nslookup <service-name>.<namespace>.svc.cluster.local
```

### 微服务间通信问题

**症状**: 服务间调用失败或超时

**排查步骤**:

1. **检查服务发现**:
```bash
# 检查Consul服务注册
kubectl exec -n consul consul-0 -- consul members
kubectl exec -n consul consul-0 -- consul catalog services
```

2. **检查网络策略**:
```bash
kubectl get networkpolicies -n autonomous-driving-prod
kubectl describe networkpolicy <policy-name> -n autonomous-driving-prod
```

3. **检查gRPC连接**:
```bash
# 使用grpcurl测试gRPC服务
grpcurl -plaintext <service-host>:<port> list
grpcurl -plaintext <service-host>:<port> <service>/<method>
```

## 数据库问题

### PostgreSQL连接问题

**症状**: 应用无法连接数据库

**排查步骤**:

1. **检查数据库状态**:
```bash
kubectl get pods -n database -l app=postgresql
kubectl logs postgresql-primary-0 -n database
```

2. **测试数据库连接**:
```bash
kubectl exec -it postgresql-primary-0 -n database -- psql -U autonomous_driving -d autonomous_driving -c "SELECT version();"
```

3. **检查连接数**:
```bash
kubectl exec -it postgresql-primary-0 -n database -- psql -U autonomous_driving -d autonomous_driving -c "SELECT count(*) FROM pg_stat_activity;"
```

**常见问题解决**:

```sql
-- 检查数据库锁
SELECT * FROM pg_locks WHERE NOT granted;

-- 检查慢查询
SELECT query, mean_time, calls FROM pg_stat_statements ORDER BY mean_time DESC LIMIT 10;

-- 检查数据库大小
SELECT pg_size_pretty(pg_database_size('autonomous_driving'));

-- 终止长时间运行的查询
SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE state = 'active' AND query_start < now() - interval '5 minutes';
```

### Redis连接问题

**症状**: 缓存服务不可用

**排查步骤**:

1. **检查Redis集群状态**:
```bash
kubectl get pods -n database -l app=redis
kubectl exec -it redis-cluster-0 -n database -- redis-cli cluster nodes
```

2. **测试Redis连接**:
```bash
kubectl exec -it redis-cluster-0 -n database -- redis-cli ping
kubectl exec -it redis-cluster-0 -n database -- redis-cli info replication
```

3. **检查内存使用**:
```bash
kubectl exec -it redis-cluster-0 -n database -- redis-cli info memory
```

## 网络连接问题

### DNS解析问题

**症状**: 服务名无法解析

**排查步骤**:

1. **检查CoreDNS状态**:
```bash
kubectl get pods -n kube-system -l k8s-app=kube-dns
kubectl logs -n kube-system -l k8s-app=kube-dns
```

2. **测试DNS解析**:
```bash
kubectl run dns-test --image=busybox -it --rm -- nslookup kubernetes.default.svc.cluster.local
```

3. **检查DNS配置**:
```bash
kubectl get configmap coredns -n kube-system -o yaml
```

### 负载均衡问题

**症状**: 请求分发不均匀或失败

**排查步骤**:

1. **检查Nginx配置**:
```bash
kubectl exec -n ingress-nginx <nginx-pod> -- nginx -t
kubectl logs -n ingress-nginx <nginx-pod>
```

2. **检查后端服务健康**:
```bash
kubectl get endpoints -n autonomous-driving-prod
curl -I http://<backend-service>/health
```

## 性能问题

### 高CPU使用率

**排查步骤**:

1. **识别高CPU进程**:
```bash
kubectl top pods -n autonomous-driving-prod --sort-by=cpu
kubectl exec -it <pod-name> -n autonomous-driving-prod -- top
```

2. **分析应用性能**:
```bash
# 查看Go应用的pprof
kubectl port-forward <pod-name> 6060:6060 -n autonomous-driving-prod
curl http://localhost:6060/debug/pprof/profile?seconds=30 > cpu.prof
go tool pprof cpu.prof
```

3. **检查资源限制**:
```bash
kubectl describe pod <pod-name> -n autonomous-driving-prod | grep -A 5 "Limits\|Requests"
```

### 内存泄漏

**排查步骤**:

1. **监控内存使用趋势**:
```bash
kubectl top pods -n autonomous-driving-prod --sort-by=memory
```

2. **分析内存使用**:
```bash
# Go应用内存分析
curl http://localhost:6060/debug/pprof/heap > heap.prof
go tool pprof heap.prof

# Python应用内存分析
kubectl exec -it <pod-name> -n autonomous-driving-prod -- python -m memory_profiler <script>
```

### 数据库性能问题

**排查步骤**:

1. **检查慢查询**:
```sql
-- PostgreSQL慢查询
SELECT query, mean_time, calls, total_time 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;

-- 检查锁等待
SELECT * FROM pg_stat_activity WHERE wait_event IS NOT NULL;
```

2. **检查索引使用**:
```sql
-- 检查未使用的索引
SELECT schemaname, tablename, indexname, idx_scan
FROM pg_stat_user_indexes
WHERE idx_scan = 0;

-- 检查表扫描
SELECT schemaname, tablename, seq_scan, seq_tup_read
FROM pg_stat_user_tables
WHERE seq_scan > 0
ORDER BY seq_tup_read DESC;
```

## 安全问题

### 异常登录检测

**排查步骤**:

1. **检查登录日志**:
```bash
grep "login" /var/log/security/security.log | tail -100
```

2. **分析IP地址**:
```bash
# 统计登录IP
awk '/login_attempt/ {print $NF}' /var/log/security/security.log | sort | uniq -c | sort -nr
```

3. **检查失败登录**:
```bash
grep "Failed login" /var/log/security/security.log | tail -50
```

### 权限异常

**排查步骤**:

1. **检查权限拒绝日志**:
```bash
grep "permission_denied" /var/log/security/security.log
```

2. **验证用户权限**:
```bash
kubectl exec -it <pod-name> -n autonomous-driving-prod -- curl -H "Authorization: Bearer <token>" http://localhost:8080/api/v1/users/profile
```

## 日志分析

### 集中化日志查询

**使用Kibana查询**:

1. **错误日志查询**:
```
level:ERROR AND @timestamp:[now-1h TO now]
```

2. **特定服务日志**:
```
service:system-management AND level:WARN
```

3. **性能相关日志**:
```
message:"slow query" OR message:"timeout"
```

### 日志聚合分析

**使用命令行工具**:

```bash
# 统计错误类型
kubectl logs -n autonomous-driving-prod -l app=system-management | grep ERROR | awk '{print $4}' | sort | uniq -c

# 分析响应时间
kubectl logs -n autonomous-driving-prod -l app=api-gateway | grep "response_time" | awk '{print $NF}' | sort -n

# 检查异常模式
kubectl logs -n autonomous-driving-prod --since=1h | grep -E "(panic|fatal|exception)" | head -20
```

## 应急响应流程

### 严重故障处理

1. **立即响应** (5分钟内):
   - 确认故障范围和影响
   - 通知相关人员
   - 启动应急预案

2. **快速恢复** (30分钟内):
   - 执行回滚操作
   - 切换到备用系统
   - 恢复关键服务

3. **根因分析** (2小时内):
   - 收集故障日志
   - 分析故障原因
   - 制定修复方案

4. **后续改进** (24小时内):
   - 实施永久修复
   - 更新监控规则
   - 完善应急预案

### 常用应急命令

```bash
# 快速重启服务
kubectl rollout restart deployment/<deployment-name> -n autonomous-driving-prod

# 扩容服务
kubectl scale deployment/<deployment-name> --replicas=5 -n autonomous-driving-prod

# 回滚部署
kubectl rollout undo deployment/<deployment-name> -n autonomous-driving-prod

# 紧急维护模式
kubectl patch ingress <ingress-name> -n autonomous-driving-prod -p '{"metadata":{"annotations":{"nginx.ingress.kubernetes.io/default-backend":"maintenance-page"}}}'

# 数据库紧急备份
kubectl exec postgresql-primary-0 -n database -- pg_dump -U autonomous_driving autonomous_driving > emergency-backup-$(date +%Y%m%d_%H%M%S).sql
```

## 联系信息

### 紧急联系方式

- **运维值班**: +86-xxx-xxxx-xxxx
- **技术负责人**: +86-xxx-xxxx-xxxx
- **安全团队**: <EMAIL>

### 外部支持

- **云服务商支持**: 根据具体云平台联系
- **数据库厂商**: PostgreSQL/Redis技术支持
- **监控工具**: Prometheus/Grafana社区支持

## 相关文档

- [部署操作手册](./deployment-guide.md)
- [监控告警配置](./monitoring-guide.md)
- [安全事件响应](../security/incident-response.md)
- [备份恢复流程](./backup-recovery.md)
