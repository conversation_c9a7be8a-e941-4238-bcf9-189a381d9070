# 自动驾驶开发加速系统 - 监控告警指南

## 概述

本文档详细说明自动驾驶开发加速系统的监控体系架构、关键指标、告警规则配置和故障处理流程，帮助运维团队有效监控系统运行状态。

## 监控架构

### 监控组件

```
┌─────────────────────────────────────────────────────────────┐
│                    监控数据流                                │
├─────────────────────────────────────────────────────────────┤
│  应用服务 → Prometheus → Grafana → AlertManager → 通知渠道  │
│     ↓                                                       │
│  日志数据 → Filebeat → Logstash → Elasticsearch → Kibana   │
│     ↓                                                       │
│  链路追踪 → Jaeger Collector → Jaeger Query → Jaeger UI    │
└─────────────────────────────────────────────────────────────┘
```

### 核心组件

1. **Prometheus**: 指标收集和存储
2. **Grafana**: 数据可视化和仪表盘
3. **AlertManager**: 告警管理和通知
4. **ELK Stack**: 日志收集和分析
5. **Jaeger**: 分布式链路追踪

## 关键监控指标

### 1. 系统基础指标

#### 服务器资源
```yaml
# CPU使用率
node_cpu_usage_percent = 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100)

# 内存使用率
node_memory_usage_percent = (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100

# 磁盘使用率
node_disk_usage_percent = (1 - (node_filesystem_avail_bytes / node_filesystem_size_bytes)) * 100

# 网络流量
node_network_receive_bytes_rate = rate(node_network_receive_bytes_total[5m])
node_network_transmit_bytes_rate = rate(node_network_transmit_bytes_total[5m])
```

#### Kubernetes集群
```yaml
# Pod状态
kube_pod_status_phase{phase="Running|Pending|Failed"}

# 节点状态
kube_node_status_condition{condition="Ready"}

# 资源使用
container_cpu_usage_seconds_total
container_memory_working_set_bytes
```

### 2. 应用性能指标

#### HTTP请求指标
```yaml
# 请求率
http_requests_per_second = rate(http_requests_total[5m])

# 响应时间
http_request_duration_p50 = histogram_quantile(0.50, rate(http_request_duration_seconds_bucket[5m]))
http_request_duration_p95 = histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))
http_request_duration_p99 = histogram_quantile(0.99, rate(http_request_duration_seconds_bucket[5m]))

# 错误率
http_error_rate = rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m])
```

#### 数据库指标
```yaml
# PostgreSQL连接数
pg_stat_database_numbackends

# 查询性能
pg_stat_statements_mean_time_ms
pg_stat_statements_calls

# 锁等待
pg_locks_count

# Redis指标
redis_connected_clients
redis_used_memory_bytes
redis_keyspace_hits_total
redis_keyspace_misses_total
```

### 3. 业务指标

#### 用户活动
```yaml
# 活跃用户数
active_users_total

# 登录成功率
login_success_rate = rate(login_attempts_total{status="success"}[5m]) / rate(login_attempts_total[5m])

# 项目创建数
projects_created_total

# 仿真执行数
simulations_executed_total
```

#### 功能使用
```yaml
# 代码生成次数
code_generation_total

# 地图编辑会话
map_editing_sessions_active

# 部署任务数
deployment_jobs_total
```

## Grafana仪表盘

### 1. 系统概览仪表盘

**面板配置**:
- **系统状态**: 服务在线数、离线数、总请求率、平均CPU使用率
- **服务可用性**: 各服务的健康状态表格
- **资源使用**: CPU、内存、磁盘、网络使用率图表
- **请求性能**: HTTP请求率、响应时间分布、错误率

**查询示例**:
```promql
# 服务可用性
up{job=~"system-management|development-tools|simulation-integration|map-editor|deployment-ops"}

# 平均响应时间
histogram_quantile(0.95, sum(rate(http_request_duration_seconds_bucket{job=~".*"}[5m])) by (le, job))

# 错误率
sum(rate(http_requests_total{status=~"5.."}[5m])) by (job) / sum(rate(http_requests_total[5m])) by (job)
```

### 2. 应用性能仪表盘

**面板配置**:
- **请求量**: 各服务的QPS趋势
- **响应时间**: P50、P95、P99响应时间
- **错误分析**: 错误率趋势和错误类型分布
- **数据库性能**: 连接数、查询时间、慢查询

### 3. 基础设施仪表盘

**面板配置**:
- **节点状态**: Kubernetes节点健康状态
- **Pod状态**: Pod运行状态和资源使用
- **存储状态**: PV使用情况和IO性能
- **网络状态**: 集群网络流量和连接状态

## 告警规则配置

### 1. 系统级告警

```yaml
groups:
- name: system-alerts
  rules:
  # 节点宕机
  - alert: NodeDown
    expr: up{job="node-exporter"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "节点 {{ $labels.instance }} 宕机"
      description: "节点 {{ $labels.instance }} 已宕机超过1分钟"

  # CPU使用率过高
  - alert: HighCPUUsage
    expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "{{ $labels.instance }} CPU使用率过高"
      description: "{{ $labels.instance }} CPU使用率为 {{ $value }}%，持续5分钟"

  # 内存使用率过高
  - alert: HighMemoryUsage
    expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 85
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "{{ $labels.instance }} 内存使用率过高"
      description: "{{ $labels.instance }} 内存使用率为 {{ $value }}%，持续5分钟"

  # 磁盘空间不足
  - alert: DiskSpaceLow
    expr: (1 - (node_filesystem_avail_bytes / node_filesystem_size_bytes)) * 100 > 90
    for: 2m
    labels:
      severity: critical
    annotations:
      summary: "{{ $labels.instance }} 磁盘空间不足"
      description: "{{ $labels.instance }} 磁盘 {{ $labels.mountpoint }} 使用率为 {{ $value }}%"
```

### 2. 应用级告警

```yaml
groups:
- name: application-alerts
  rules:
  # 服务宕机
  - alert: ServiceDown
    expr: up{job=~"system-management|development-tools|simulation-integration|map-editor|deployment-ops"} == 0
    for: 1m
    labels:
      severity: critical
    annotations:
      summary: "服务 {{ $labels.job }} 宕机"
      description: "服务 {{ $labels.job }} 在实例 {{ $labels.instance }} 上宕机"

  # 高错误率
  - alert: HighErrorRate
    expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) > 0.05
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "{{ $labels.job }} 错误率过高"
      description: "{{ $labels.job }} 5xx错误率为 {{ $value | humanizePercentage }}，持续5分钟"

  # 响应时间过长
  - alert: HighResponseTime
    expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "{{ $labels.job }} 响应时间过长"
      description: "{{ $labels.job }} P95响应时间为 {{ $value }}s，持续5分钟"

  # 数据库连接数过多
  - alert: HighDatabaseConnections
    expr: pg_stat_database_numbackends > 80
    for: 3m
    labels:
      severity: warning
    annotations:
      summary: "数据库连接数过多"
      description: "PostgreSQL连接数为 {{ $value }}，接近最大连接数"
```

### 3. 业务级告警

```yaml
groups:
- name: business-alerts
  rules:
  # 登录失败率过高
  - alert: HighLoginFailureRate
    expr: rate(login_attempts_total{status="failed"}[10m]) / rate(login_attempts_total[10m]) > 0.3
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "登录失败率过高"
      description: "登录失败率为 {{ $value | humanizePercentage }}，可能存在安全问题"

  # 仿真任务失败率过高
  - alert: HighSimulationFailureRate
    expr: rate(simulations_total{status="failed"}[30m]) / rate(simulations_total[30m]) > 0.2
    for: 10m
    labels:
      severity: warning
    annotations:
      summary: "仿真任务失败率过高"
      description: "仿真任务失败率为 {{ $value | humanizePercentage }}"

  # 代码生成失败
  - alert: CodeGenerationFailures
    expr: increase(code_generation_failures_total[1h]) > 10
    for: 0m
    labels:
      severity: warning
    annotations:
      summary: "代码生成失败次数过多"
      description: "过去1小时内代码生成失败 {{ $value }} 次"
```

## AlertManager配置

### 通知渠道配置

```yaml
# alertmanager.yml
global:
  smtp_smarthost: 'smtp.example.com:587'
  smtp_from: '<EMAIL>'

route:
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'default-receiver'
  routes:
  - match:
      severity: critical
    receiver: 'critical-alerts'
    group_wait: 5s
    repeat_interval: 30m
  - match:
      severity: warning
    receiver: 'warning-alerts'
    repeat_interval: 2h

receivers:
- name: 'default-receiver'
  email_configs:
  - to: '<EMAIL>'
    subject: '[监控告警] {{ .GroupLabels.alertname }}'
    body: |
      {{ range .Alerts }}
      告警: {{ .Annotations.summary }}
      详情: {{ .Annotations.description }}
      时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
      {{ end }}

- name: 'critical-alerts'
  email_configs:
  - to: '<EMAIL>,<EMAIL>'
    subject: '[紧急告警] {{ .GroupLabels.alertname }}'
    body: |
      🚨 紧急告警 🚨
      {{ range .Alerts }}
      告警: {{ .Annotations.summary }}
      详情: {{ .Annotations.description }}
      时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
      {{ end }}
  
  webhook_configs:
  - url: 'https://oapi.dingtalk.com/robot/send?access_token=your-token'
    send_resolved: true

- name: 'warning-alerts'
  email_configs:
  - to: '<EMAIL>'
    subject: '[警告] {{ .GroupLabels.alertname }}'
```

## 日志监控

### 1. 日志收集配置

**Filebeat配置**:
```yaml
filebeat.inputs:
- type: log
  enabled: true
  paths:
    - /var/log/autonomous-driving/*/*.log
  fields:
    service: autonomous-driving
  multiline.pattern: '^\d{4}-\d{2}-\d{2}'
  multiline.negate: true
  multiline.match: after

output.logstash:
  hosts: ["logstash:5044"]
```

**Logstash配置**:
```ruby
input {
  beats {
    port => 5044
  }
}

filter {
  if [fields][service] == "autonomous-driving" {
    grok {
      match => { 
        "message" => "%{TIMESTAMP_ISO8601:timestamp} \[%{LOGLEVEL:level}\] %{DATA:logger} - %{GREEDYDATA:message}"
      }
    }
    
    date {
      match => [ "timestamp", "ISO8601" ]
    }
  }
}

output {
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "autonomous-driving-%{+YYYY.MM.dd}"
  }
}
```

### 2. 日志告警规则

**ElastAlert配置**:
```yaml
# 错误日志告警
name: error-log-alert
type: frequency
index: autonomous-driving-*
num_events: 10
timeframe:
  minutes: 5

filter:
- term:
    level: "ERROR"

alert:
- "email"

email:
- "<EMAIL>"

alert_text: |
  在过去5分钟内检测到 {0} 个错误日志
  
  错误详情:
  {1}

include:
- "timestamp"
- "level"
- "logger"
- "message"
```

## 性能监控

### 1. 应用性能监控

**关键指标**:
- **吞吐量**: 每秒请求数 (QPS)
- **延迟**: 响应时间分布 (P50, P95, P99)
- **错误率**: 4xx/5xx错误比例
- **可用性**: 服务正常运行时间

**监控查询**:
```promql
# 服务QPS
sum(rate(http_requests_total[5m])) by (job)

# 平均响应时间
avg(rate(http_request_duration_seconds_sum[5m])) by (job) / avg(rate(http_request_duration_seconds_count[5m])) by (job)

# 错误率
sum(rate(http_requests_total{status=~"[45].."}[5m])) by (job) / sum(rate(http_requests_total[5m])) by (job)
```

### 2. 数据库性能监控

**PostgreSQL监控**:
```sql
-- 慢查询监控
SELECT query, mean_time, calls, total_time 
FROM pg_stat_statements 
WHERE mean_time > 1000 
ORDER BY mean_time DESC;

-- 锁等待监控
SELECT * FROM pg_stat_activity 
WHERE wait_event IS NOT NULL;

-- 连接数监控
SELECT count(*) FROM pg_stat_activity;
```

**Redis监控**:
```bash
# Redis性能指标
redis-cli info stats | grep -E "(keyspace_hits|keyspace_misses|used_memory)"
```

## 故障处理流程

### 1. 告警响应流程

```mermaid
graph TD
    A[收到告警] --> B{告警级别}
    B -->|Critical| C[立即响应 5分钟内]
    B -->|Warning| D[30分钟内响应]
    C --> E[确认故障范围]
    D --> E
    E --> F[执行应急处理]
    F --> G[通知相关人员]
    G --> H[根因分析]
    H --> I[制定修复方案]
    I --> J[实施修复]
    J --> K[验证修复效果]
    K --> L[更新文档]
```

### 2. 常见故障处理

#### 服务宕机处理
```bash
# 1. 检查服务状态
kubectl get pods -n autonomous-driving-prod

# 2. 查看Pod日志
kubectl logs <pod-name> -n autonomous-driving-prod

# 3. 重启服务
kubectl rollout restart deployment/<deployment-name> -n autonomous-driving-prod

# 4. 验证恢复
kubectl get pods -n autonomous-driving-prod -w
```

#### 数据库连接问题
```bash
# 1. 检查数据库状态
kubectl exec -it postgresql-primary-0 -n database -- pg_isready

# 2. 检查连接数
kubectl exec -it postgresql-primary-0 -n database -- psql -c "SELECT count(*) FROM pg_stat_activity;"

# 3. 终止长时间连接
kubectl exec -it postgresql-primary-0 -n database -- psql -c "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE state = 'idle' AND state_change < now() - interval '1 hour';"
```

#### 高负载处理
```bash
# 1. 检查资源使用
kubectl top nodes
kubectl top pods -n autonomous-driving-prod

# 2. 扩容服务
kubectl scale deployment <deployment-name> --replicas=5 -n autonomous-driving-prod

# 3. 检查HPA状态
kubectl get hpa -n autonomous-driving-prod
```

## 监控最佳实践

### 1. 指标设计原则

- **USE方法**: Utilization (使用率)、Saturation (饱和度)、Errors (错误)
- **RED方法**: Rate (速率)、Errors (错误)、Duration (持续时间)
- **四个黄金信号**: 延迟、流量、错误、饱和度

### 2. 告警设计原则

- **可操作性**: 每个告警都应该有明确的处理步骤
- **相关性**: 告警应该与业务影响相关
- **及时性**: 告警应该在问题影响用户前触发
- **准确性**: 避免误报和漏报

### 3. 仪表盘设计原则

- **层次化**: 从概览到详细的层次结构
- **相关性**: 相关指标放在一起显示
- **可读性**: 使用清晰的图表和颜色
- **实用性**: 关注运维人员真正需要的信息

## 相关文档

- [部署操作手册](./deployment-guide.md)
- [故障排查指南](./troubleshooting-guide.md)
- [备份恢复流程](./backup-recovery.md)
- [系统架构文档](../architecture/system-architecture.md)
