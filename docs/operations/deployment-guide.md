# 自动驾驶开发加速系统 - 部署操作手册

## 概述

本文档提供自动驾驶开发加速系统的完整部署指南，包括环境准备、系统部署、配置管理和验证测试等步骤。

## 目录

1. [环境准备](#环境准备)
2. [基础设施部署](#基础设施部署)
3. [应用服务部署](#应用服务部署)
4. [监控系统部署](#监控系统部署)
5. [安全配置](#安全配置)
6. [验证测试](#验证测试)
7. [故障排查](#故障排查)

## 环境准备

### 系统要求

#### 硬件要求
- **CPU**: 最少16核，推荐32核
- **内存**: 最少64GB，推荐128GB
- **存储**: 最少1TB SSD，推荐2TB NVMe SSD
- **网络**: 千兆网络，推荐万兆网络

#### 软件要求
- **操作系统**: Ubuntu 20.04 LTS 或 CentOS 8
- **Kubernetes**: v1.24+
- **Docker**: v20.10+
- **Helm**: v3.8+
- **kubectl**: v1.24+

### 环境检查

```bash
# 检查系统版本
cat /etc/os-release

# 检查硬件资源
lscpu
free -h
df -h

# 检查网络连接
ping -c 3 8.8.8.8

# 检查Docker状态
docker version
docker info

# 检查Kubernetes集群状态
kubectl cluster-info
kubectl get nodes
kubectl get pods --all-namespaces
```

### 依赖安装

```bash
# 安装必要工具
sudo apt update
sudo apt install -y curl wget git jq

# 安装kubectl
curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
sudo install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl

# 安装Helm
curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash

# 验证安装
kubectl version --client
helm version
```

## 基础设施部署

### 1. 创建命名空间

```bash
# 应用命名空间配置
kubectl apply -f deployment/kubernetes/production/namespace.yaml

# 验证命名空间创建
kubectl get namespaces
kubectl describe namespace autonomous-driving-prod
```

### 2. 配置存储类

```bash
# 创建高性能存储类
cat <<EOF | kubectl apply -f -
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: fast-ssd
  annotations:
    storageclass.kubernetes.io/is-default-class: "false"
provisioner: kubernetes.io/aws-ebs
parameters:
  type: gp3
  iops: "3000"
  throughput: "125"
  encrypted: "true"
allowVolumeExpansion: true
volumeBindingMode: WaitForFirstConsumer
reclaimPolicy: Delete
EOF
```

### 3. 配置密钥

```bash
# 创建数据库密钥
kubectl create secret generic database-credentials \
  --from-literal=host=postgresql-primary.database.svc.cluster.local \
  --from-literal=port=5432 \
  --from-literal=database=autonomous_driving \
  --from-literal=username=autonomous_driving \
  --from-literal=password=your-secure-password \
  --from-literal=replication_user=replicator \
  --from-literal=replication_password=your-replication-password \
  -n autonomous-driving-prod

# 创建Redis密钥
kubectl create secret generic redis-credentials \
  --from-literal=host=redis-cluster.database.svc.cluster.local \
  --from-literal=port=6379 \
  --from-literal=password=your-redis-password \
  -n autonomous-driving-prod

# 创建JWT密钥
kubectl create secret generic jwt-secret \
  --from-literal=secret=your-jwt-secret-key-should-be-very-long-and-random \
  -n autonomous-driving-prod

# 创建镜像仓库密钥
kubectl create secret docker-registry harbor-registry-secret \
  --docker-server=harbor.autonomous-driving.com \
  --docker-username=admin \
  --docker-password=your-harbor-password \
  --docker-email=<EMAIL> \
  -n autonomous-driving-prod
```

### 4. 部署数据库

```bash
# 部署PostgreSQL和Redis
kubectl apply -f deployment/kubernetes/production/database.yaml

# 等待数据库启动
kubectl wait --for=condition=ready pod -l app=postgresql,role=primary -n database --timeout=300s
kubectl wait --for=condition=ready pod -l app=redis -n database --timeout=300s

# 验证数据库状态
kubectl get pods -n database
kubectl logs -l app=postgresql,role=primary -n database
```

## 应用服务部署

### 1. 使用Helm部署

```bash
# 添加依赖仓库
helm repo add bitnami https://charts.bitnami.com/bitnami
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
helm repo add ingress-nginx https://kubernetes.github.io/ingress-nginx
helm repo add jaegertracing https://jaegertracing.github.io/helm-charts
helm repo add hashicorp https://helm.releases.hashicorp.com
helm repo update

# 部署系统
cd deployment/helm/autonomous-driving-platform

# 检查配置
helm lint .
helm template . --debug

# 部署到生产环境
helm install autonomous-driving-platform . \
  --namespace autonomous-driving-prod \
  --values values.yaml \
  --timeout 20m \
  --wait

# 验证部署状态
helm status autonomous-driving-platform -n autonomous-driving-prod
helm list -n autonomous-driving-prod
```

### 2. 逐步部署验证

```bash
# 检查Pod状态
kubectl get pods -n autonomous-driving-prod -o wide

# 检查服务状态
kubectl get services -n autonomous-driving-prod

# 检查Ingress状态
kubectl get ingress -n autonomous-driving-prod

# 检查HPA状态
kubectl get hpa -n autonomous-driving-prod

# 查看详细状态
kubectl describe deployment system-management -n autonomous-driving-prod
```

### 3. 配置域名和SSL

```bash
# 安装cert-manager
kubectl apply -f https://github.com/cert-manager/cert-manager/releases/download/v1.11.0/cert-manager.yaml

# 创建Let's Encrypt证书颁发者
cat <<EOF | kubectl apply -f -
apiVersion: cert-manager.io/v1
kind: ClusterIssuer
metadata:
  name: letsencrypt-prod
spec:
  acme:
    server: https://acme-v02.api.letsencrypt.org/directory
    email: <EMAIL>
    privateKeySecretRef:
      name: letsencrypt-prod
    solvers:
    - http01:
        ingress:
          class: nginx
EOF

# 验证证书状态
kubectl get certificates -n autonomous-driving-prod
kubectl describe certificate autonomous-driving-tls -n autonomous-driving-prod
```

## 监控系统部署

### 1. 部署Prometheus监控

```bash
# 应用Prometheus配置
kubectl apply -f monitoring/prometheus/

# 验证Prometheus状态
kubectl get pods -n monitoring -l app=prometheus
kubectl port-forward -n monitoring svc/prometheus 9090:9090
```

### 2. 部署Grafana仪表盘

```bash
# 导入仪表盘配置
kubectl create configmap grafana-dashboards \
  --from-file=monitoring/grafana/dashboards/ \
  -n monitoring

# 访问Grafana
kubectl port-forward -n monitoring svc/grafana 3000:3000
```

### 3. 部署日志系统

```bash
# 部署ELK Stack
kubectl apply -f logging/

# 验证日志系统状态
kubectl get pods -n logging
kubectl port-forward -n logging svc/kibana 5601:5601
```

## 安全配置

### 1. 应用安全策略

```bash
# 运行安全加固脚本
chmod +x security/config/security-hardening.sh
./security/config/security-hardening.sh

# 应用网络策略
kubectl apply -f security/network-policies/

# 验证安全配置
kubectl get networkpolicies -n autonomous-driving-prod
kubectl get podsecuritypolicies
```

### 2. 配置RBAC权限

```bash
# 创建服务账户和角色绑定
kubectl apply -f security/rbac/

# 验证RBAC配置
kubectl get serviceaccounts -n autonomous-driving-prod
kubectl get rolebindings -n autonomous-driving-prod
```

## 验证测试

### 1. 健康检查

```bash
# 检查所有Pod状态
kubectl get pods -n autonomous-driving-prod --field-selector=status.phase!=Running

# 检查服务端点
kubectl get endpoints -n autonomous-driving-prod

# 测试服务连通性
kubectl run test-pod --image=curlimages/curl -it --rm -- /bin/sh
# 在Pod内执行
curl http://system-management:8080/health
curl http://development-tools:8081/health
```

### 2. 功能测试

```bash
# 运行端到端测试
cd tests/e2e
npm install
npm run test:production

# 运行性能测试
cd tests/performance
k6 run load-test.js --env BASE_URL=https://autonomous-driving-platform.com
```

### 3. 安全测试

```bash
# 运行安全审计
cd security/tests
python3 security-audit.py --url https://autonomous-driving-platform.com --output security-report.json

# 检查漏洞扫描结果
cat security-report.json | jq '.scan_info'
```

## 故障排查

### 1. 常见问题

#### Pod启动失败
```bash
# 查看Pod状态
kubectl describe pod <pod-name> -n autonomous-driving-prod

# 查看Pod日志
kubectl logs <pod-name> -n autonomous-driving-prod --previous

# 查看事件
kubectl get events -n autonomous-driving-prod --sort-by='.lastTimestamp'
```

#### 服务无法访问
```bash
# 检查服务配置
kubectl describe service <service-name> -n autonomous-driving-prod

# 检查端点
kubectl get endpoints <service-name> -n autonomous-driving-prod

# 测试服务连通性
kubectl run debug --image=nicolaka/netshoot -it --rm -- /bin/bash
```

#### 数据库连接问题
```bash
# 检查数据库状态
kubectl exec -it postgresql-primary-0 -n database -- psql -U autonomous_driving -d autonomous_driving -c "SELECT version();"

# 检查Redis状态
kubectl exec -it redis-cluster-0 -n database -- redis-cli -a your-redis-password ping
```

### 2. 日志分析

```bash
# 查看应用日志
kubectl logs -f deployment/system-management -n autonomous-driving-prod

# 查看Nginx访问日志
kubectl logs -f deployment/nginx -n ingress-nginx

# 在Kibana中查询日志
# 访问 http://localhost:5601
# 使用查询: service:system-management AND level:ERROR
```

### 3. 性能监控

```bash
# 查看资源使用情况
kubectl top nodes
kubectl top pods -n autonomous-driving-prod

# 查看HPA状态
kubectl describe hpa -n autonomous-driving-prod

# 在Grafana中查看监控指标
# 访问 http://localhost:3000
# 查看系统概览仪表盘
```

## 维护操作

### 1. 滚动更新

```bash
# 更新应用镜像
helm upgrade autonomous-driving-platform . \
  --namespace autonomous-driving-prod \
  --set systemManagement.image.tag=v1.0.1 \
  --timeout 10m

# 查看更新状态
kubectl rollout status deployment/system-management -n autonomous-driving-prod

# 回滚更新（如果需要）
kubectl rollout undo deployment/system-management -n autonomous-driving-prod
```

### 2. 扩缩容操作

```bash
# 手动扩容
kubectl scale deployment system-management --replicas=5 -n autonomous-driving-prod

# 查看扩容状态
kubectl get deployment system-management -n autonomous-driving-prod -w
```

### 3. 备份恢复

```bash
# 数据库备份
kubectl exec postgresql-primary-0 -n database -- pg_dump -U autonomous_driving autonomous_driving > backup-$(date +%Y%m%d).sql

# 配置备份
kubectl get configmaps -n autonomous-driving-prod -o yaml > configmaps-backup-$(date +%Y%m%d).yaml
kubectl get secrets -n autonomous-driving-prod -o yaml > secrets-backup-$(date +%Y%m%d).yaml
```

## 联系信息

- **运维团队**: <EMAIL>
- **技术支持**: <EMAIL>
- **紧急联系**: +86-xxx-xxxx-xxxx

## 相关文档

- [系统架构文档](../architecture/system-architecture.md)
- [API文档](../api/api-reference.md)
- [故障排查指南](./troubleshooting-guide.md)
- [监控告警手册](./monitoring-guide.md)
