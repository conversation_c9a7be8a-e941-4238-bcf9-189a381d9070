# 自动驾驶开发加速系统 - 备份恢复流程

## 概述

本文档详细说明自动驾驶开发加速系统的数据备份策略、恢复流程和灾难恢复计划，确保系统数据的安全性和业务连续性。

## 备份策略

### 备份范围

#### 1. 数据库备份
- **PostgreSQL主数据库**: 用户数据、项目信息、配置数据
- **Redis缓存**: 会话数据、临时缓存（可选）
- **元数据**: 数据库结构、索引、权限配置

#### 2. 文件系统备份
- **项目文件**: 用户上传的项目代码和资源
- **地图文件**: 地图数据和版本历史
- **配置文件**: 系统配置、证书、密钥
- **日志文件**: 重要的审计和操作日志

#### 3. 应用配置备份
- **Kubernetes配置**: Deployment、Service、ConfigMap、Secret
- **Helm Charts**: 应用部署模板和配置
- **监控配置**: Prometheus规则、Grafana仪表盘

### 备份频率

| 数据类型 | 备份频率 | 保留期限 | 备份方式 |
|----------|----------|----------|----------|
| PostgreSQL | 每日全量 + 每小时增量 | 30天 | pg_dump + WAL归档 |
| Redis | 每日快照 | 7天 | RDB快照 |
| 项目文件 | 每日增量 | 90天 | rsync + 版本控制 |
| 地图文件 | 实时同步 | 永久 | Git LFS |
| 配置文件 | 变更时备份 | 永久 | Git版本控制 |
| 系统日志 | 每日归档 | 180天 | 日志轮转 |

## 数据库备份

### PostgreSQL备份

#### 1. 全量备份脚本

```bash
#!/bin/bash
# PostgreSQL全量备份脚本

# 配置变量
BACKUP_DIR="/backup/postgresql"
DB_HOST="postgresql-primary.database.svc.cluster.local"
DB_PORT="5432"
DB_NAME="autonomous_driving"
DB_USER="backup_user"
RETENTION_DAYS=30

# 创建备份目录
mkdir -p $BACKUP_DIR/$(date +%Y/%m/%d)

# 备份文件名
BACKUP_FILE="$BACKUP_DIR/$(date +%Y/%m/%d)/autonomous_driving_$(date +%Y%m%d_%H%M%S).sql"

# 执行备份
echo "开始PostgreSQL全量备份: $(date)"
kubectl exec postgresql-primary-0 -n database -- pg_dump \
  -h $DB_HOST \
  -p $DB_PORT \
  -U $DB_USER \
  -d $DB_NAME \
  --verbose \
  --no-password \
  --format=custom \
  --compress=9 \
  --file=/tmp/backup.dump

# 复制备份文件
kubectl cp database/postgresql-primary-0:/tmp/backup.dump $BACKUP_FILE

# 压缩备份文件
gzip $BACKUP_FILE

# 验证备份文件
if [ -f "${BACKUP_FILE}.gz" ]; then
    echo "备份成功: ${BACKUP_FILE}.gz"
    echo "备份大小: $(du -h ${BACKUP_FILE}.gz | cut -f1)"
else
    echo "备份失败!" >&2
    exit 1
fi

# 清理旧备份
find $BACKUP_DIR -name "*.sql.gz" -mtime +$RETENTION_DAYS -delete

echo "PostgreSQL备份完成: $(date)"
```

#### 2. 增量备份 (WAL归档)

```bash
#!/bin/bash
# PostgreSQL WAL归档脚本

WAL_ARCHIVE_DIR="/backup/postgresql/wal"
WAL_FILE=$1
WAL_PATH=$2

# 创建归档目录
mkdir -p $WAL_ARCHIVE_DIR/$(date +%Y/%m/%d)

# 复制WAL文件
cp $WAL_PATH $WAL_ARCHIVE_DIR/$(date +%Y/%m/%d)/$WAL_FILE

# 压缩WAL文件
gzip $WAL_ARCHIVE_DIR/$(date +%Y/%m/%d)/$WAL_FILE

echo "WAL文件归档完成: $WAL_FILE"
```

#### 3. PostgreSQL配置

```yaml
# postgresql.conf 备份相关配置
wal_level = replica
archive_mode = on
archive_command = '/backup/scripts/wal_archive.sh %f %p'
max_wal_senders = 3
wal_keep_segments = 32
```

### Redis备份

#### 1. Redis备份脚本

```bash
#!/bin/bash
# Redis备份脚本

BACKUP_DIR="/backup/redis"
RETENTION_DAYS=7

# 创建备份目录
mkdir -p $BACKUP_DIR/$(date +%Y/%m/%d)

# 获取Redis节点列表
REDIS_NODES=$(kubectl get pods -n database -l app=redis -o jsonpath='{.items[*].metadata.name}')

for node in $REDIS_NODES; do
    echo "备份Redis节点: $node"
    
    # 触发BGSAVE
    kubectl exec $node -n database -- redis-cli BGSAVE
    
    # 等待备份完成
    while [ "$(kubectl exec $node -n database -- redis-cli LASTSAVE)" = "$(kubectl exec $node -n database -- redis-cli LASTSAVE)" ]; do
        sleep 5
    done
    
    # 复制RDB文件
    kubectl cp database/$node:/data/dump.rdb $BACKUP_DIR/$(date +%Y/%m/%d)/${node}_$(date +%Y%m%d_%H%M%S).rdb
    
    # 压缩备份文件
    gzip $BACKUP_DIR/$(date +%Y/%m/%d)/${node}_$(date +%Y%m%d_%H%M%S).rdb
done

# 清理旧备份
find $BACKUP_DIR -name "*.rdb.gz" -mtime +$RETENTION_DAYS -delete

echo "Redis备份完成: $(date)"
```

## 文件系统备份

### 项目文件备份

```bash
#!/bin/bash
# 项目文件备份脚本

SOURCE_DIR="/data/projects"
BACKUP_DIR="/backup/projects"
RETENTION_DAYS=90

# 创建备份目录
mkdir -p $BACKUP_DIR/$(date +%Y/%m/%d)

# 使用rsync进行增量备份
rsync -av \
  --delete \
  --backup \
  --backup-dir=$BACKUP_DIR/$(date +%Y/%m/%d)/deleted \
  --exclude='*.tmp' \
  --exclude='node_modules/' \
  --exclude='.git/' \
  $SOURCE_DIR/ \
  $BACKUP_DIR/current/

# 创建快照
cp -al $BACKUP_DIR/current $BACKUP_DIR/$(date +%Y/%m/%d)/snapshot

# 清理旧备份
find $BACKUP_DIR -maxdepth 1 -type d -mtime +$RETENTION_DAYS -exec rm -rf {} \;

echo "项目文件备份完成: $(date)"
```

### 地图文件备份

```bash
#!/bin/bash
# 地图文件Git备份脚本

MAP_REPO_DIR="/data/maps"
BACKUP_REPO="git@backup-server:maps-backup.git"

cd $MAP_REPO_DIR

# 提交所有变更
git add .
git commit -m "自动备份: $(date)"

# 推送到备份仓库
git push $BACKUP_REPO main

# 创建每日标签
git tag "backup-$(date +%Y%m%d)"
git push $BACKUP_REPO --tags

echo "地图文件备份完成: $(date)"
```

## 配置备份

### Kubernetes配置备份

```bash
#!/bin/bash
# Kubernetes配置备份脚本

BACKUP_DIR="/backup/kubernetes"
NAMESPACES="autonomous-driving-prod database monitoring"

mkdir -p $BACKUP_DIR/$(date +%Y/%m/%d)

for ns in $NAMESPACES; do
    echo "备份命名空间: $ns"
    
    # 备份所有资源
    kubectl get all,configmap,secret,pv,pvc,ingress -n $ns -o yaml > \
        $BACKUP_DIR/$(date +%Y/%m/%d)/${ns}-resources.yaml
    
    # 备份RBAC配置
    kubectl get role,rolebinding,serviceaccount -n $ns -o yaml > \
        $BACKUP_DIR/$(date +%Y/%m/%d)/${ns}-rbac.yaml
done

# 备份集群级资源
kubectl get clusterrole,clusterrolebinding,storageclass,node -o yaml > \
    $BACKUP_DIR/$(date +%Y/%m/%d)/cluster-resources.yaml

# 压缩备份
tar -czf $BACKUP_DIR/k8s-backup-$(date +%Y%m%d).tar.gz \
    $BACKUP_DIR/$(date +%Y/%m/%d)/

echo "Kubernetes配置备份完成: $(date)"
```

## 自动化备份

### Cron任务配置

```bash
# 编辑crontab
crontab -e

# 添加备份任务
# PostgreSQL全量备份 - 每天凌晨2点
0 2 * * * /backup/scripts/postgresql_backup.sh >> /var/log/backup/postgresql.log 2>&1

# PostgreSQL增量备份 - 每小时
0 * * * * /backup/scripts/postgresql_incremental.sh >> /var/log/backup/postgresql.log 2>&1

# Redis备份 - 每天凌晨3点
0 3 * * * /backup/scripts/redis_backup.sh >> /var/log/backup/redis.log 2>&1

# 项目文件备份 - 每天凌晨4点
0 4 * * * /backup/scripts/projects_backup.sh >> /var/log/backup/projects.log 2>&1

# Kubernetes配置备份 - 每天凌晨5点
0 5 * * * /backup/scripts/k8s_backup.sh >> /var/log/backup/k8s.log 2>&1

# 备份验证 - 每天上午8点
0 8 * * * /backup/scripts/backup_verify.sh >> /var/log/backup/verify.log 2>&1
```

### Kubernetes CronJob

```yaml
# 数据库备份CronJob
apiVersion: batch/v1
kind: CronJob
metadata:
  name: postgresql-backup
  namespace: database
spec:
  schedule: "0 2 * * *"
  jobTemplate:
    spec:
      template:
        spec:
          containers:
          - name: backup
            image: postgres:15
            command:
            - /bin/bash
            - -c
            - |
              pg_dump -h postgresql-primary -U backup_user -d autonomous_driving \
                --format=custom --compress=9 \
                --file=/backup/autonomous_driving_$(date +%Y%m%d_%H%M%S).dump
            env:
            - name: PGPASSWORD
              valueFrom:
                secretKeyRef:
                  name: postgresql-backup-secret
                  key: password
            volumeMounts:
            - name: backup-storage
              mountPath: /backup
          volumes:
          - name: backup-storage
            persistentVolumeClaim:
              claimName: backup-pvc
          restartPolicy: OnFailure
```

## 数据恢复

### PostgreSQL恢复

#### 1. 全量恢复

```bash
#!/bin/bash
# PostgreSQL全量恢复脚本

BACKUP_FILE=$1
DB_NAME="autonomous_driving"
DB_USER="postgres"

if [ -z "$BACKUP_FILE" ]; then
    echo "用法: $0 <backup_file>"
    exit 1
fi

echo "开始恢复数据库: $DB_NAME"
echo "备份文件: $BACKUP_FILE"

# 停止应用服务
kubectl scale deployment --replicas=0 -n autonomous-driving-prod --all

# 删除现有数据库
kubectl exec postgresql-primary-0 -n database -- dropdb -U $DB_USER $DB_NAME

# 创建新数据库
kubectl exec postgresql-primary-0 -n database -- createdb -U $DB_USER $DB_NAME

# 恢复数据
kubectl cp $BACKUP_FILE database/postgresql-primary-0:/tmp/restore.dump
kubectl exec postgresql-primary-0 -n database -- pg_restore \
    -U $DB_USER \
    -d $DB_NAME \
    --verbose \
    --clean \
    --if-exists \
    /tmp/restore.dump

# 重启应用服务
kubectl scale deployment --replicas=3 -n autonomous-driving-prod --all

echo "数据库恢复完成"
```

#### 2. 时间点恢复 (PITR)

```bash
#!/bin/bash
# PostgreSQL时间点恢复脚本

TARGET_TIME=$1
BASE_BACKUP=$2
WAL_ARCHIVE_DIR="/backup/postgresql/wal"

if [ -z "$TARGET_TIME" ] || [ -z "$BASE_BACKUP" ]; then
    echo "用法: $0 <target_time> <base_backup>"
    echo "示例: $0 '2024-01-15 14:30:00' /backup/postgresql/2024/01/15/backup.dump"
    exit 1
fi

echo "开始时间点恢复到: $TARGET_TIME"

# 停止PostgreSQL
kubectl scale statefulset postgresql-primary --replicas=0 -n database

# 清理数据目录
kubectl exec postgresql-primary-0 -n database -- rm -rf /var/lib/postgresql/data/*

# 恢复基础备份
kubectl cp $BASE_BACKUP database/postgresql-primary-0:/tmp/base_backup.dump
kubectl exec postgresql-primary-0 -n database -- pg_restore \
    -U postgres \
    -d template1 \
    --create \
    /tmp/base_backup.dump

# 配置恢复
kubectl exec postgresql-primary-0 -n database -- bash -c "
cat > /var/lib/postgresql/data/recovery.conf << EOF
restore_command = 'cp $WAL_ARCHIVE_DIR/%f %p'
recovery_target_time = '$TARGET_TIME'
recovery_target_action = 'promote'
EOF
"

# 启动PostgreSQL
kubectl scale statefulset postgresql-primary --replicas=1 -n database

echo "时间点恢复完成"
```

### Redis恢复

```bash
#!/bin/bash
# Redis恢复脚本

BACKUP_FILE=$1
REDIS_NODE=$2

if [ -z "$BACKUP_FILE" ] || [ -z "$REDIS_NODE" ]; then
    echo "用法: $0 <backup_file> <redis_node>"
    exit 1
fi

echo "恢复Redis节点: $REDIS_NODE"

# 停止Redis
kubectl exec $REDIS_NODE -n database -- redis-cli SHUTDOWN NOSAVE

# 复制备份文件
kubectl cp $BACKUP_FILE database/$REDIS_NODE:/data/dump.rdb

# 启动Redis
kubectl delete pod $REDIS_NODE -n database

echo "Redis恢复完成"
```

## 灾难恢复

### 灾难恢复计划

#### 1. 恢复优先级

| 优先级 | 组件 | RTO | RPO | 恢复策略 |
|--------|------|-----|-----|----------|
| P0 | 用户认证服务 | 15分钟 | 1小时 | 热备切换 |
| P0 | 数据库服务 | 30分钟 | 1小时 | 主从切换 |
| P1 | 开发工具服务 | 1小时 | 4小时 | 备份恢复 |
| P1 | 仿真集成服务 | 2小时 | 8小时 | 重新部署 |
| P2 | 地图编辑服务 | 4小时 | 24小时 | 备份恢复 |

#### 2. 恢复流程

**阶段1: 紧急响应 (0-15分钟)**
```bash
# 1. 评估灾难范围
kubectl get nodes
kubectl get pods --all-namespaces

# 2. 启动应急响应
# 通知相关人员
# 激活灾难恢复团队
# 启动备用数据中心

# 3. 保护现场
# 停止自动化操作
# 保存日志和状态信息
```

**阶段2: 服务恢复 (15分钟-2小时)**
```bash
# 1. 恢复核心基础设施
# 恢复Kubernetes集群
# 恢复网络连接
# 恢复存储系统

# 2. 恢复数据库服务
/backup/scripts/postgresql_disaster_recovery.sh
/backup/scripts/redis_disaster_recovery.sh

# 3. 恢复应用服务
helm install autonomous-driving-platform ./helm/autonomous-driving-platform \
    --namespace autonomous-driving-prod \
    --values values-disaster-recovery.yaml
```

**阶段3: 数据恢复 (2-8小时)**
```bash
# 1. 恢复用户数据
/backup/scripts/restore_user_data.sh

# 2. 恢复项目文件
/backup/scripts/restore_project_files.sh

# 3. 恢复配置数据
/backup/scripts/restore_configurations.sh
```

### 备用站点配置

```yaml
# 备用站点Helm配置
# values-disaster-recovery.yaml
global:
  environment: "disaster-recovery"
  domain: "dr.autonomous-driving-platform.com"

# 减少资源配置以快速启动
systemManagement:
  replicaCount: 1
  resources:
    requests:
      cpu: 100m
      memory: 128Mi

developmentTools:
  replicaCount: 1
  resources:
    requests:
      cpu: 200m
      memory: 256Mi

# 使用备份数据库
postgresql:
  enabled: false
  external:
    host: "backup-postgresql.example.com"
    port: 5432
```

## 备份验证

### 自动验证脚本

```bash
#!/bin/bash
# 备份验证脚本

BACKUP_DIR="/backup"
LOG_FILE="/var/log/backup/verify.log"

echo "开始备份验证: $(date)" >> $LOG_FILE

# 验证PostgreSQL备份
echo "验证PostgreSQL备份..." >> $LOG_FILE
LATEST_PG_BACKUP=$(find $BACKUP_DIR/postgresql -name "*.sql.gz" -mtime -1 | head -1)
if [ -n "$LATEST_PG_BACKUP" ]; then
    # 测试备份文件完整性
    gunzip -t $LATEST_PG_BACKUP
    if [ $? -eq 0 ]; then
        echo "PostgreSQL备份验证成功: $LATEST_PG_BACKUP" >> $LOG_FILE
    else
        echo "PostgreSQL备份验证失败: $LATEST_PG_BACKUP" >> $LOG_FILE
        # 发送告警
        /backup/scripts/send_alert.sh "PostgreSQL备份验证失败"
    fi
else
    echo "未找到最新的PostgreSQL备份文件" >> $LOG_FILE
    /backup/scripts/send_alert.sh "PostgreSQL备份文件缺失"
fi

# 验证Redis备份
echo "验证Redis备份..." >> $LOG_FILE
LATEST_REDIS_BACKUP=$(find $BACKUP_DIR/redis -name "*.rdb.gz" -mtime -1 | head -1)
if [ -n "$LATEST_REDIS_BACKUP" ]; then
    gunzip -t $LATEST_REDIS_BACKUP
    if [ $? -eq 0 ]; then
        echo "Redis备份验证成功: $LATEST_REDIS_BACKUP" >> $LOG_FILE
    else
        echo "Redis备份验证失败: $LATEST_REDIS_BACKUP" >> $LOG_FILE
        /backup/scripts/send_alert.sh "Redis备份验证失败"
    fi
else
    echo "未找到最新的Redis备份文件" >> $LOG_FILE
    /backup/scripts/send_alert.sh "Redis备份文件缺失"
fi

echo "备份验证完成: $(date)" >> $LOG_FILE
```

### 恢复测试

```bash
#!/bin/bash
# 定期恢复测试脚本

TEST_ENV="backup-test"
BACKUP_DATE=$(date -d "yesterday" +%Y/%m/%d)

echo "开始恢复测试: $(date)"

# 创建测试环境
kubectl create namespace $TEST_ENV

# 部署测试数据库
helm install test-postgresql bitnami/postgresql \
    --namespace $TEST_ENV \
    --set auth.database=test_restore

# 恢复测试数据
BACKUP_FILE="/backup/postgresql/$BACKUP_DATE/autonomous_driving_*.sql.gz"
gunzip -c $BACKUP_FILE | kubectl exec -i test-postgresql-0 -n $TEST_ENV -- psql -U postgres -d test_restore

# 验证数据完整性
kubectl exec test-postgresql-0 -n $TEST_ENV -- psql -U postgres -d test_restore -c "SELECT COUNT(*) FROM users;"

# 清理测试环境
kubectl delete namespace $TEST_ENV

echo "恢复测试完成: $(date)"
```

## 监控和告警

### 备份监控指标

```yaml
# Prometheus监控规则
groups:
- name: backup-monitoring
  rules:
  - alert: BackupFailed
    expr: backup_job_success == 0
    for: 5m
    labels:
      severity: critical
    annotations:
      summary: "备份任务失败"
      description: "{{ $labels.job }} 备份任务失败"

  - alert: BackupMissing
    expr: time() - backup_last_success_time > 86400
    for: 10m
    labels:
      severity: warning
    annotations:
      summary: "备份文件缺失"
      description: "超过24小时未发现新的备份文件"

  - alert: BackupSizeAnomaly
    expr: abs(backup_size_bytes - backup_size_bytes offset 1d) / backup_size_bytes > 0.5
    for: 5m
    labels:
      severity: warning
    annotations:
      summary: "备份文件大小异常"
      description: "备份文件大小与昨天相比变化超过50%"
```

## 最佳实践

### 备份策略建议

1. **3-2-1备份原则**:
   - 保留3份数据副本
   - 使用2种不同的存储介质
   - 1份异地备份

2. **定期测试恢复**:
   - 每月进行完整恢复测试
   - 每季度进行灾难恢复演练
   - 记录恢复时间和问题

3. **监控和告警**:
   - 监控备份任务执行状态
   - 验证备份文件完整性
   - 及时处理备份异常

4. **文档和培训**:
   - 维护详细的恢复文档
   - 定期培训运维人员
   - 更新应急联系方式

## 相关文档

- [部署操作手册](./deployment-guide.md)
- [故障排查指南](./troubleshooting-guide.md)
- [监控告警配置](./monitoring-guide.md)
- [安全事件响应](../security/incident-response.md)
