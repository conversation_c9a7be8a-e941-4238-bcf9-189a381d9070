# 自动驾驶开发加速系统 - 监控和日志系统配置指南

## 1. 概述

本文档描述了自动驾驶开发加速系统的监控和日志系统架构、配置方案和使用指南。系统采用Prometheus + Grafana进行指标监控，ELK Stack进行日志管理，Jaeger进行链路追踪，构建了完整的可观测性体系。

## 2. 架构设计

### 2.1 整体架构

```
应用层
    ↓
指标收集层 (Exporters)
    ↓
监控存储层 (Prometheus, Elasticsearch)
    ↓
可视化层 (Grafana, Kibana)
    ↓
告警层 (AlertManager)
```

### 2.2 组件说明

| 组件 | 功能 | 端口 | 说明 |
|------|------|------|------|
| Prometheus | 指标收集和存储 | 9090 | 时序数据库，监控核心 |
| Grafana | 指标可视化 | 3001 | 仪表板和图表展示 |
| AlertManager | 告警管理 | 9093 | 告警路由和通知 |
| Elasticsearch | 日志存储 | 9200 | 分布式搜索引擎 |
| Kibana | 日志可视化 | 5601 | 日志分析和查询 |
| Logstash | 日志处理 | 5044 | 日志解析和转换 |
| Filebeat | 日志收集 | - | 轻量级日志采集器 |
| Jaeger | 链路追踪 | 16686 | 分布式追踪系统 |
| Loki | 日志聚合 | 3100 | 类Prometheus的日志系统 |

## 3. 部署配置

### 3.1 Docker部署

```bash
# 1. 创建数据目录
mkdir -p data/{prometheus,grafana,alertmanager,elasticsearch,loki}
mkdir -p logs

# 2. 设置权限
sudo chown -R 472:472 data/grafana
sudo chown -R 1000:1000 data/elasticsearch
sudo chown -R 10001:10001 data/loki

# 3. 创建环境变量文件
cat > .env << EOF
GRAFANA_ADMIN_USER=admin
GRAFANA_ADMIN_PASSWORD=your-grafana-password
POSTGRES_USER=autodriving
POSTGRES_PASSWORD=your-postgres-password
POSTGRES_DB=autodriving
REDIS_PASSWORD=your-redis-password
EOF

# 4. 启动监控服务
docker-compose -f docker-compose.monitoring.yml up -d

# 5. 验证服务状态
docker-compose -f docker-compose.monitoring.yml ps
```

### 3.2 Kubernetes部署

```yaml
# monitoring-namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: monitoring
---
# prometheus-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: prometheus
  namespace: monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: prometheus
  template:
    metadata:
      labels:
        app: prometheus
    spec:
      containers:
      - name: prometheus
        image: prom/prometheus:v2.47.0
        ports:
        - containerPort: 9090
        volumeMounts:
        - name: config
          mountPath: /etc/prometheus
        - name: data
          mountPath: /prometheus
      volumes:
      - name: config
        configMap:
          name: prometheus-config
      - name: data
        persistentVolumeClaim:
          claimName: prometheus-pvc
```

## 4. 监控配置

### 4.1 Prometheus配置

**主要监控目标：**
- 系统指标：CPU、内存、磁盘、网络
- 应用指标：HTTP请求、gRPC调用、数据库连接
- 业务指标：用户活跃度、任务执行情况
- 基础设施：数据库、缓存、消息队列

**关键配置：**
```yaml
# 抓取间隔
scrape_interval: 15s

# 监控目标
scrape_configs:
  - job_name: 'microservices'
    static_configs:
      - targets: ['service1:9100', 'service2:9100']
```

### 4.2 告警规则

**系统告警：**
- 实例宕机：`up == 0`
- CPU使用率过高：`cpu_usage > 80%`
- 内存使用率过高：`memory_usage > 80%`
- 磁盘空间不足：`disk_usage > 80%`

**应用告警：**
- 服务不可用：`service_up == 0`
- 响应时间过长：`response_time > 1s`
- 错误率过高：`error_rate > 5%`
- 队列积压：`queue_length > 1000`

### 4.3 Grafana仪表板

**系统监控仪表板：**
- 主机概览：CPU、内存、磁盘、网络
- 容器监控：Docker容器资源使用
- 集群状态：Kubernetes集群健康状态

**应用监控仪表板：**
- 服务概览：请求量、响应时间、错误率
- 数据库监控：连接数、查询性能、慢查询
- 缓存监控：命中率、内存使用、连接数

**业务监控仪表板：**
- 用户活跃度：登录用户数、活跃会话
- 项目统计：项目数量、构建次数、部署频率
- 仿真任务：任务执行情况、成功率、资源使用

## 5. 日志管理

### 5.1 日志收集

**Filebeat配置：**
```yaml
filebeat.inputs:
- type: container
  paths:
    - '/var/lib/docker/containers/*/*.log'
  processors:
  - add_docker_metadata:
      host: "unix:///var/run/docker.sock"

output.logstash:
  hosts: ["logstash:5044"]
```

**日志格式标准：**
```json
{
  "timestamp": "2023-12-01T10:00:00Z",
  "level": "INFO",
  "service": "system-management",
  "message": "用户登录成功",
  "user_id": "user123",
  "trace_id": "abc123",
  "span_id": "def456"
}
```

### 5.2 日志处理

**Logstash管道配置：**
```ruby
input {
  beats {
    port => 5044
  }
}

filter {
  if [container][name] {
    mutate {
      add_field => { "service" => "%{[container][name]}" }
    }
  }
  
  json {
    source => "message"
  }
  
  date {
    match => [ "timestamp", "ISO8601" ]
  }
}

output {
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "autodriving-logs-%{+YYYY.MM.dd}"
  }
}
```

### 5.3 日志查询

**常用查询语句：**
```bash
# 查询错误日志
level:ERROR AND service:system-management

# 查询特定用户的操作
user_id:user123 AND timestamp:[now-1h TO now]

# 查询慢查询
message:"slow query" AND duration:>1000

# 查询API调用
path:/api/v1/users AND method:POST
```

## 6. 链路追踪

### 6.1 Jaeger配置

**追踪数据流：**
```
应用 → Jaeger Agent → Jaeger Collector → Elasticsearch
```

**Go应用集成：**
```go
import (
    "go.opentelemetry.io/otel"
    "go.opentelemetry.io/otel/exporters/jaeger"
)

func initTracing() {
    exporter, _ := jaeger.New(jaeger.WithCollectorEndpoint(
        jaeger.WithEndpoint("http://jaeger:14268/api/traces"),
    ))
    
    tp := trace.NewTracerProvider(
        trace.WithBatcher(exporter),
        trace.WithResource(resource.NewWithAttributes(
            semconv.ServiceNameKey.String("system-management"),
        )),
    )
    
    otel.SetTracerProvider(tp)
}
```

### 6.2 追踪分析

**性能分析：**
- 请求链路：完整的请求调用链
- 耗时分析：各个服务的响应时间
- 错误定位：异常发生的具体位置
- 依赖关系：服务间的调用关系

## 7. 告警配置

### 7.1 告警规则

**告警级别：**
- Critical：严重告警，需要立即处理
- Warning：警告告警，需要关注
- Info：信息告警，仅记录

**告警路由：**
```yaml
routes:
  - match:
      severity: critical
    receiver: 'critical-alerts'
    repeat_interval: 30m
  
  - match:
      service: database
    receiver: 'database-alerts'
    repeat_interval: 1h
```

### 7.2 通知渠道

**支持的通知方式：**
- 邮件通知：发送到指定邮箱
- 钉钉通知：发送到钉钉群
- 企业微信：发送到企业微信群
- Slack：发送到Slack频道
- 短信通知：紧急告警短信

## 8. 性能优化

### 8.1 Prometheus优化

```yaml
# 存储优化
storage:
  tsdb:
    retention.time: 30d
    retention.size: 100GB
    min-block-duration: 2h
    max-block-duration: 25h

# 查询优化
query:
  max-concurrency: 20
  timeout: 2m
  max-samples: 50000000
```

### 8.2 Elasticsearch优化

```yaml
# 索引模板
{
  "index_patterns": ["autodriving-logs-*"],
  "settings": {
    "number_of_shards": 3,
    "number_of_replicas": 1,
    "refresh_interval": "30s",
    "index.lifecycle.name": "autodriving-policy"
  }
}

# 生命周期策略
{
  "policy": {
    "phases": {
      "hot": {
        "actions": {
          "rollover": {
            "max_size": "10GB",
            "max_age": "1d"
          }
        }
      },
      "warm": {
        "min_age": "7d",
        "actions": {
          "allocate": {
            "number_of_replicas": 0
          }
        }
      },
      "delete": {
        "min_age": "30d"
      }
    }
  }
}
```

## 9. 故障排查

### 9.1 常见问题

**Prometheus问题：**
```bash
# 检查目标状态
curl http://prometheus:9090/api/v1/targets

# 检查配置
curl http://prometheus:9090/api/v1/status/config

# 重新加载配置
curl -X POST http://prometheus:9090/-/reload
```

**Elasticsearch问题：**
```bash
# 检查集群健康
curl http://elasticsearch:9200/_cluster/health

# 检查索引状态
curl http://elasticsearch:9200/_cat/indices

# 清理过期索引
curl -X DELETE http://elasticsearch:9200/old-index-*
```

### 9.2 性能调优

**监控指标：**
- Prometheus查询延迟
- Elasticsearch索引速度
- Grafana仪表板加载时间
- 告警响应时间

**优化建议：**
- 合理设置抓取间隔
- 优化查询语句
- 使用索引模板
- 定期清理历史数据

## 10. 维护操作

### 10.1 备份恢复

```bash
# Prometheus数据备份
docker exec prometheus tar -czf /backup/prometheus-$(date +%Y%m%d).tar.gz /prometheus

# Elasticsearch快照
curl -X PUT "elasticsearch:9200/_snapshot/backup/snapshot_$(date +%Y%m%d)"

# Grafana配置备份
docker exec grafana tar -czf /backup/grafana-$(date +%Y%m%d).tar.gz /var/lib/grafana
```

### 10.2 升级维护

```bash
# 滚动升级Prometheus
docker-compose -f docker-compose.monitoring.yml up -d prometheus

# 升级Grafana插件
docker exec grafana grafana-cli plugins update-all

# 重建Elasticsearch索引
curl -X POST "elasticsearch:9200/_reindex" -H 'Content-Type: application/json' -d'
{
  "source": {
    "index": "old-index"
  },
  "dest": {
    "index": "new-index"
  }
}'
```

---

**文档版本**：v1.0  
**编写日期**：2025-08-26  
**更新日期**：2025-08-26  
**负责人**：运维工程师  
**审核人**：系统架构师
