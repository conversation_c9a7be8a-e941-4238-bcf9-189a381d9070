# 自动驾驶开发加速系统 - API网关配置和使用指南

## 1. 概述

API网关是自动驾驶开发加速系统的统一入口，基于OpenResty（Nginx + Lua）构建，提供以下核心功能：

- **统一路由**：将客户端请求路由到相应的后端服务
- **身份认证**：基于JWT的用户认证和授权
- **负载均衡**：多种负载均衡算法支持
- **限流保护**：基于IP和用户的请求限流
- **SSL终端**：HTTPS证书管理和SSL卸载
- **监控日志**：详细的访问日志和性能监控

## 2. 架构设计

### 2.1 组件架构

```
客户端 → API网关 → 后端服务
   ↓        ↓         ↓
 HTTPS    JWT认证   微服务集群
   ↓        ↓         ↓
 限流     负载均衡   健康检查
```

### 2.2 服务映射

| 路径前缀 | 后端服务 | 端口 | 说明 |
|---------|---------|------|------|
| `/api/v1/system/` | 系统管理服务 | 8080 | 用户认证、权限管理 |
| `/api/v1/development/` | 开发工具服务 | 8081 | 代码生成、项目管理 |
| `/api/v1/simulation/` | 仿真集成服务 | 8082 | 仿真器控制、数据采集 |
| `/api/v1/maps/` | 地图编辑服务 | 8083 | 地图编辑、协作功能 |
| `/api/v1/devops/` | 部署运维服务 | 8084 | CI/CD、环境管理 |
| `/ws/` | WebSocket服务 | 8082 | 实时通信 |
| `/` | 前端静态资源 | 3000 | React应用 |

## 3. 部署配置

### 3.1 Docker部署

```bash
# 1. 克隆配置文件
git clone <repository-url>
cd deployment

# 2. 创建环境变量文件
cat > .env << EOF
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
REDIS_PASSWORD=your-redis-password
ELASTICSEARCH_HOST=elasticsearch.example.com
ELASTICSEARCH_PORT=9200
EOF

# 3. 创建SSL证书目录
mkdir -p nginx/ssl

# 4. 生成自签名证书（开发环境）
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout nginx/ssl/api.autonomous-driving-platform.com.key \
  -out nginx/ssl/api.autonomous-driving-platform.com.crt \
  -subj "/C=CN/ST=Beijing/L=Beijing/O=AutonomousDriving/CN=api.autonomous-driving-platform.com"

# 5. 启动服务
docker-compose -f docker-compose.gateway.yml up -d
```

### 3.2 Kubernetes部署

```yaml
# api-gateway-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: api-gateway
  namespace: autodriving
spec:
  replicas: 3
  selector:
    matchLabels:
      app: api-gateway
  template:
    metadata:
      labels:
        app: api-gateway
    spec:
      containers:
      - name: api-gateway
        image: autodriving/api-gateway:1.0.0
        ports:
        - containerPort: 80
        - containerPort: 443
        env:
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: jwt-secret
              key: secret
        - name: REDIS_HOST
          value: "redis-service"
        volumeMounts:
        - name: ssl-certs
          mountPath: /etc/nginx/ssl
          readOnly: true
      volumes:
      - name: ssl-certs
        secret:
          secretName: api-gateway-tls
```

## 4. JWT认证配置

### 4.1 JWT令牌格式

```json
{
  "header": {
    "typ": "JWT",
    "alg": "HS256"
  },
  "payload": {
    "user_id": "user123",
    "username": "张三",
    "email": "<EMAIL>",
    "role": "developer",
    "iat": 1640995200,
    "exp": 1640998800,
    "jti": "unique-token-id"
  }
}
```

### 4.2 认证流程

1. **用户登录**：向 `/api/v1/system/auth/login` 发送用户名密码
2. **获取令牌**：系统返回JWT访问令牌
3. **请求认证**：在请求头中添加 `Authorization: Bearer <token>`
4. **令牌验证**：API网关验证令牌有效性
5. **用户信息**：将用户信息添加到请求头传递给后端

### 4.3 权限控制

| 角色 | 权限范围 | 可访问路径 |
|------|---------|-----------|
| `admin` | 系统管理员 | 所有API |
| `developer` | 开发人员 | 开发工具、仿真、地图编辑 |
| `devops` | 运维人员 | 部署运维、系统监控 |
| `user` | 普通用户 | 基础查询功能 |

## 5. 限流配置

### 5.1 限流策略

```nginx
# 基于IP的限流：每秒10个请求
limit_req_zone $binary_remote_addr zone=api_limit_ip:10m rate=10r/s;

# 基于用户的限流：每秒20个请求
limit_req_zone $http_x_user_id zone=api_limit_user:10m rate=20r/s;

# 连接数限制：每个IP最多10个连接
limit_conn_zone $binary_remote_addr zone=conn_limit_ip:10m;
```

### 5.2 限流应用

```nginx
location /api/v1/ {
    # IP限流
    limit_req zone=api_limit_ip burst=20 nodelay;
    
    # 用户限流
    limit_req zone=api_limit_user burst=50 nodelay;
    
    # 连接限制
    limit_conn conn_limit_ip 10;
}
```

## 6. 负载均衡配置

### 6.1 负载均衡算法

- **round_robin**：轮询（默认）
- **least_conn**：最少连接数
- **ip_hash**：IP哈希（会话保持）
- **weighted**：加权轮询

### 6.2 健康检查

```nginx
upstream system_management {
    least_conn;
    server system-management-1:8080 weight=1 max_fails=3 fail_timeout=30s;
    server system-management-2:8080 weight=1 max_fails=3 fail_timeout=30s;
    server system-management-3:8080 weight=1 max_fails=3 fail_timeout=30s backup;
    
    keepalive 32;
    keepalive_requests 100;
    keepalive_timeout 60s;
}
```

## 7. 监控和日志

### 7.1 访问日志格式

```nginx
log_format api_gateway '$remote_addr - $remote_user [$time_local] '
                      '"$request" $status $body_bytes_sent '
                      '"$http_referer" "$http_user_agent" '
                      'service="$upstream_addr" '
                      'request_time=$request_time '
                      'upstream_time=$upstream_response_time '
                      'user_id="$http_x_user_id" '
                      'trace_id="$http_x_trace_id"';
```

### 7.2 监控指标

- **请求量**：每秒请求数（RPS）
- **响应时间**：平均响应时间和P99延迟
- **错误率**：4xx和5xx错误比例
- **上游状态**：后端服务健康状态
- **限流统计**：触发限流的请求数

### 7.3 Prometheus监控

```yaml
# 监控指标示例
nginx_http_requests_total{method="GET",status="200"} 1234
nginx_http_request_duration_seconds{quantile="0.99"} 0.1
nginx_upstream_response_time_seconds 0.05
```

## 8. 安全配置

### 8.1 SSL/TLS配置

```nginx
# SSL协议和加密套件
ssl_protocols TLSv1.2 TLSv1.3;
ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
ssl_prefer_server_ciphers off;

# SSL会话缓存
ssl_session_cache shared:SSL:10m;
ssl_session_timeout 10m;
```

### 8.2 安全头设置

```nginx
# 安全响应头
add_header X-Frame-Options DENY;
add_header X-Content-Type-Options nosniff;
add_header X-XSS-Protection "1; mode=block";
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
```

### 8.3 IP白名单

```nginx
# 管理接口IP限制
location /admin/ {
    allow 10.0.0.0/8;
    allow **********/12;
    allow ***********/16;
    deny all;
}
```

## 9. 故障排查

### 9.1 常见问题

**问题1：502 Bad Gateway**
```bash
# 检查后端服务状态
docker ps | grep system-management
curl -f http://system-management-1:8080/health

# 检查网络连通性
docker exec api-gateway ping system-management-1
```

**问题2：JWT认证失败**
```bash
# 检查JWT密钥配置
docker exec api-gateway env | grep JWT_SECRET

# 检查Redis连接
docker exec api-gateway redis-cli -h redis ping
```

**问题3：限流触发过多**
```bash
# 查看限流日志
docker logs api-gateway | grep "limiting requests"

# 调整限流参数
vim nginx/nginx.conf
# 修改 rate=10r/s 为更大值
```

### 9.2 日志分析

```bash
# 实时查看访问日志
docker logs -f api-gateway

# 分析错误日志
docker exec api-gateway tail -f /var/log/nginx/error.log

# 统计状态码分布
docker exec api-gateway awk '{print $9}' /var/log/nginx/access.log | sort | uniq -c
```

## 10. 性能优化

### 10.1 Nginx优化

```nginx
# 工作进程数
worker_processes auto;

# 连接数配置
worker_connections 1024;
keepalive_timeout 65;

# 缓冲区配置
proxy_buffering on;
proxy_buffer_size 4k;
proxy_buffers 8 4k;
```

### 10.2 缓存策略

```nginx
# 静态资源缓存
location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}

# API响应缓存
location /api/v1/system/config {
    proxy_cache api_cache;
    proxy_cache_valid 200 5m;
    proxy_cache_key "$request_uri";
}
```

## 11. 维护操作

### 11.1 证书更新

```bash
# 手动更新Let's Encrypt证书
docker exec certbot certbot renew --force-renewal

# 重新加载Nginx配置
docker exec api-gateway nginx -s reload
```

### 11.2 配置热更新

```bash
# 测试配置文件语法
docker exec api-gateway nginx -t

# 重新加载配置（不中断服务）
docker exec api-gateway nginx -s reload

# 查看配置状态
docker exec api-gateway nginx -T
```

### 11.3 备份和恢复

```bash
# 备份配置文件
tar -czf nginx-config-backup-$(date +%Y%m%d).tar.gz nginx/

# 备份SSL证书
tar -czf ssl-certs-backup-$(date +%Y%m%d).tar.gz nginx/ssl/

# 恢复配置
tar -xzf nginx-config-backup-20231201.tar.gz
docker-compose -f docker-compose.gateway.yml restart api-gateway
```

---

**文档版本**：v1.0  
**编写日期**：2025-08-26  
**更新日期**：2025-08-26  
**负责人**：DevOps工程师  
**审核人**：系统架构师
