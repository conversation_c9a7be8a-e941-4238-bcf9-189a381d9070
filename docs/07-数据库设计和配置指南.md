# 自动驾驶开发加速系统 - 数据库设计和配置指南

## 1. 概述

本文档描述了自动驾驶开发加速系统的数据库架构设计、配置方案和使用指南。系统采用PostgreSQL作为主数据库，Redis作为缓存和会话存储，实现了高可用、高性能的数据存储方案。

## 2. 架构设计

### 2.1 整体架构

```
应用层
    ↓
连接池层 (PgBouncer)
    ↓
数据库层
├── PostgreSQL 主从集群
│   ├── Master (读写)
│   └── Slave (只读)
└── Redis 主从集群
    ├── Master (读写)
    ├── Slave (只读)
    └── Sentinel (监控)
```

### 2.2 数据分布策略

| 数据类型 | 存储位置 | 说明 |
|---------|---------|------|
| 用户信息、项目数据 | PostgreSQL | 持久化存储，ACID保证 |
| 会话信息、JWT缓存 | Redis DB0 | 快速访问，自动过期 |
| 实时仿真数据 | Redis DB1 | 高频读写，临时存储 |
| 地图编辑锁 | Redis DB2 | 分布式锁，协作控制 |
| 系统监控指标 | Redis DB3 | 时序数据，定期清理 |
| 异步任务队列 | Redis DB4 | 消息队列，任务调度 |

## 3. PostgreSQL配置

### 3.1 表结构设计

#### 核心表说明

**用户管理表**
- `users`: 用户基本信息
- `user_sessions`: 用户会话管理
- `permissions`: 权限定义
- `role_permissions`: 角色权限关联

**项目管理表**
- `projects`: 项目信息
- `project_members`: 项目成员
- `code_templates`: 代码模板
- `builds`: 构建记录
- `deployments`: 部署记录

**仿真相关表**
- `simulation_scenarios`: 仿真场景
- `simulation_tasks`: 仿真任务

**地图管理表**
- `maps`: 地图信息
- `map_edit_sessions`: 编辑会话
- `map_edit_operations`: 编辑操作记录

**监控审计表**
- `system_metrics`: 系统指标
- `system_events`: 系统事件
- `audit_logs`: 审计日志

### 3.2 索引优化

```sql
-- 高频查询索引
CREATE INDEX CONCURRENTLY idx_users_email ON users(email);
CREATE INDEX CONCURRENTLY idx_projects_owner_id ON projects(owner_id);
CREATE INDEX CONCURRENTLY idx_builds_project_status ON builds(project_id, status);

-- 复合索引
CREATE INDEX CONCURRENTLY idx_simulation_tasks_project_status_created 
ON simulation_tasks(project_id, status, created_at);

-- 部分索引
CREATE INDEX CONCURRENTLY idx_active_users 
ON users(id) WHERE status = 'active';
```

### 3.3 主从复制配置

**主库配置 (postgresql.conf)**
```ini
# 复制配置
wal_level = replica
max_wal_senders = 3
wal_keep_size = 1GB
synchronous_commit = on
synchronous_standby_names = 'slave1'

# 性能优化
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB
```

**从库配置 (recovery.conf)**
```ini
standby_mode = 'on'
primary_conninfo = 'host=postgres-master port=5432 user=replicator'
recovery_target_timeline = 'latest'
```

## 4. Redis配置

### 4.1 内存优化

```ini
# 内存配置
maxmemory 2gb
maxmemory-policy allkeys-lru
maxmemory-samples 5

# 数据结构优化
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
set-max-intset-entries 512
```

### 4.2 持久化策略

```ini
# RDB配置
save 900 1
save 300 10
save 60 10000
rdbcompression yes

# AOF配置
appendonly yes
appendfsync everysec
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb
```

### 4.3 高可用配置

**Sentinel配置**
```ini
sentinel monitor mymaster redis-master 6379 2
sentinel auth-pass mymaster your-redis-password
sentinel down-after-milliseconds mymaster 30000
sentinel parallel-syncs mymaster 1
sentinel failover-timeout mymaster 180000
```

## 5. 部署指南

### 5.1 Docker部署

```bash
# 1. 创建数据目录
mkdir -p data/{postgres-master,postgres-slave,redis-master,redis-slave,pgadmin}
mkdir -p logs/{postgres,postgres-slave,redis,redis-slave}
mkdir -p backups

# 2. 设置权限
sudo chown -R 999:999 data/postgres-*
sudo chown -R 999:999 data/redis-*
sudo chown -R 5050:5050 data/pgadmin

# 3. 创建环境变量文件
cat > .env << EOF
POSTGRES_DB=autodriving
POSTGRES_USER=autodriving
POSTGRES_PASSWORD=your-strong-postgres-password
POSTGRES_REPLICATION_USER=replicator
POSTGRES_REPLICATION_PASSWORD=your-replication-password
REDIS_PASSWORD=your-strong-redis-password
PGADMIN_EMAIL=<EMAIL>
PGADMIN_PASSWORD=your-pgadmin-password
EOF

# 4. 启动数据库服务
docker-compose -f docker-compose.database.yml up -d

# 5. 验证服务状态
docker-compose -f docker-compose.database.yml ps
```

### 5.2 Kubernetes部署

```yaml
# postgres-master-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres-master
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres-master
  template:
    metadata:
      labels:
        app: postgres-master
    spec:
      containers:
      - name: postgres
        image: postgres:15-alpine
        env:
        - name: POSTGRES_DB
          value: "autodriving"
        - name: POSTGRES_USER
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: username
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: password
        volumeMounts:
        - name: postgres-data
          mountPath: /var/lib/postgresql/data
        - name: postgres-config
          mountPath: /etc/postgresql
      volumes:
      - name: postgres-data
        persistentVolumeClaim:
          claimName: postgres-master-pvc
      - name: postgres-config
        configMap:
          name: postgres-config
```

## 6. 数据库操作

### 6.1 连接管理

```python
# Python连接示例
import psycopg2
from redis import Redis, Sentinel

# PostgreSQL连接
def get_postgres_connection(read_only=False):
    host = 'postgres-slave' if read_only else 'postgres-master'
    return psycopg2.connect(
        host=host,
        database='autodriving',
        user='autodriving',
        password='your-password'
    )

# Redis连接（使用Sentinel）
def get_redis_connection():
    sentinel = Sentinel([
        ('redis-sentinel-1', 26379),
        ('redis-sentinel-2', 26379),
        ('redis-sentinel-3', 26379)
    ])
    return sentinel.master_for('mymaster', password='your-password')
```

### 6.2 常用查询

```sql
-- 查看用户项目统计
SELECT 
    u.username,
    COUNT(p.id) as project_count,
    COUNT(b.id) as build_count
FROM users u
LEFT JOIN projects p ON u.id = p.owner_id
LEFT JOIN builds b ON p.id = b.project_id
GROUP BY u.id, u.username;

-- 查看仿真任务执行情况
SELECT 
    DATE(created_at) as date,
    status,
    COUNT(*) as task_count,
    AVG(duration_seconds) as avg_duration
FROM simulation_tasks
WHERE created_at >= CURRENT_DATE - INTERVAL '30 days'
GROUP BY DATE(created_at), status
ORDER BY date DESC;

-- 查看系统资源使用趋势
SELECT 
    DATE_TRUNC('hour', timestamp) as hour,
    metric_name,
    AVG(metric_value) as avg_value,
    MAX(metric_value) as max_value
FROM system_metrics
WHERE metric_type = 'cpu' 
    AND timestamp >= CURRENT_TIMESTAMP - INTERVAL '24 hours'
GROUP BY hour, metric_name
ORDER BY hour DESC;
```

## 7. 备份和恢复

### 7.1 PostgreSQL备份

```bash
#!/bin/bash
# 全量备份脚本

BACKUP_DIR="/backups/postgres"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="autodriving"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 执行备份
pg_dump -h postgres-master -U autodriving -d $DB_NAME \
    --verbose --clean --no-owner --no-privileges \
    --format=custom \
    --file=$BACKUP_DIR/autodriving_$DATE.backup

# 压缩备份文件
gzip $BACKUP_DIR/autodriving_$DATE.backup

# 清理旧备份（保留30天）
find $BACKUP_DIR -name "*.backup.gz" -mtime +30 -delete

echo "备份完成: autodriving_$DATE.backup.gz"
```

### 7.2 Redis备份

```bash
#!/bin/bash
# Redis备份脚本

BACKUP_DIR="/backups/redis"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p $BACKUP_DIR

# 执行BGSAVE
redis-cli -h redis-master -a your-password BGSAVE

# 等待备份完成
while [ $(redis-cli -h redis-master -a your-password LASTSAVE) -eq $LASTSAVE ]; do
    sleep 1
done

# 复制RDB文件
docker cp autodriving-redis-master:/data/dump.rdb $BACKUP_DIR/redis_$DATE.rdb

# 压缩备份文件
gzip $BACKUP_DIR/redis_$DATE.rdb

echo "Redis备份完成: redis_$DATE.rdb.gz"
```

### 7.3 数据恢复

```bash
# PostgreSQL恢复
pg_restore -h postgres-master -U autodriving -d autodriving \
    --verbose --clean --no-owner --no-privileges \
    /backups/postgres/autodriving_20231201_020000.backup.gz

# Redis恢复
# 1. 停止Redis服务
docker stop autodriving-redis-master

# 2. 替换RDB文件
gunzip -c /backups/redis/redis_20231201_030000.rdb.gz > /data/redis-master/dump.rdb

# 3. 启动Redis服务
docker start autodriving-redis-master
```

## 8. 监控和维护

### 8.1 性能监控

```sql
-- 查看慢查询
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows
FROM pg_stat_statements
ORDER BY total_time DESC
LIMIT 10;

-- 查看数据库大小
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

### 8.2 Redis监控

```bash
# Redis信息查看
redis-cli -h redis-master -a your-password INFO memory
redis-cli -h redis-master -a your-password INFO replication
redis-cli -h redis-master -a your-password INFO stats

# 慢查询日志
redis-cli -h redis-master -a your-password SLOWLOG GET 10
```

### 8.3 自动化维护

```bash
# 定期维护脚本
#!/bin/bash

# 1. 更新统计信息
psql -h postgres-master -U autodriving -d autodriving -c "ANALYZE;"

# 2. 清理过期会话
psql -h postgres-master -U autodriving -d autodriving -c "
DELETE FROM user_sessions WHERE expires_at < NOW() - INTERVAL '1 day';"

# 3. 清理旧的系统指标
psql -h postgres-master -U autodriving -d autodriving -c "
DELETE FROM system_metrics WHERE timestamp < NOW() - INTERVAL '30 days';"

# 4. Redis内存清理
redis-cli -h redis-master -a your-password --scan --pattern "session:*" | \
    xargs -I {} redis-cli -h redis-master -a your-password TTL {} | \
    awk '$1 == -1 {print $2}' | \
    xargs -I {} redis-cli -h redis-master -a your-password DEL {}

echo "数据库维护完成"
```

## 9. 故障排查

### 9.1 常见问题

**连接问题**
```bash
# 检查服务状态
docker ps | grep postgres
docker logs autodriving-postgres-master

# 检查网络连通性
docker exec -it autodriving-postgres-master pg_isready

# 检查配置
docker exec -it autodriving-postgres-master cat /etc/postgresql/postgresql.conf
```

**性能问题**
```sql
-- 检查锁等待
SELECT * FROM pg_stat_activity WHERE wait_event IS NOT NULL;

-- 检查连接数
SELECT count(*) FROM pg_stat_activity;

-- 检查缓存命中率
SELECT 
    sum(heap_blks_read) as heap_read,
    sum(heap_blks_hit) as heap_hit,
    sum(heap_blks_hit) / (sum(heap_blks_hit) + sum(heap_blks_read)) as ratio
FROM pg_statio_user_tables;
```

### 9.2 故障恢复

**主从切换**
```bash
# 1. 提升从库为主库
docker exec -it autodriving-postgres-slave pg_promote

# 2. 更新应用配置
# 修改应用连接字符串指向新的主库

# 3. 重建从库
# 从新主库创建基础备份，重新配置从库
```

---

**文档版本**：v1.0  
**编写日期**：2025-08-26  
**更新日期**：2025-08-26  
**负责人**：数据库管理员  
**审核人**：系统架构师
