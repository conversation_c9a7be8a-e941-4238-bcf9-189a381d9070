// 自动驾驶开发加速系统 - 应用入口文件
import React from 'react';
import ReactDOM from 'react-dom/client';

import App from './App';

// 开发环境下启用React严格模式
const StrictModeWrapper: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  if (import.meta.env.DEV) {
    return <React.StrictMode>{children}</React.StrictMode>;
  }
  return <>{children}</>;
};

// 获取根元素
const rootElement = document.getElementById('root');

if (!rootElement) {
  throw new Error('根元素未找到，请检查index.html文件');
}

// 创建React根节点并渲染应用
const root = ReactDOM.createRoot(rootElement);

root.render(
  <StrictModeWrapper>
    <App />
  </StrictModeWrapper>
);

// 开发环境下的热更新支持
if (import.meta.env.DEV && import.meta.hot) {
  import.meta.hot.accept();
}

// 生产环境下注册Service Worker
if (import.meta.env.PROD && 'serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker
      .register('/sw.js')
      .then(registration => {
        console.log('SW registered: ', registration);
      })
      .catch(registrationError => {
        console.log('SW registration failed: ', registrationError);
      });
  });
}

// 全局错误处理
window.addEventListener('error', event => {
  console.error('全局错误:', event.error);
  // 这里可以添加错误上报逻辑
});

window.addEventListener('unhandledrejection', event => {
  console.error('未处理的Promise拒绝:', event.reason);
  // 这里可以添加错误上报逻辑
});

// 性能监控
if (import.meta.env.PROD) {
  // 监控首屏加载时间
  window.addEventListener('load', () => {
    const perfData = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
    const loadTime = perfData.loadEventEnd - perfData.fetchStart;
    console.log(`页面加载时间: ${loadTime}ms`);
    
    // 这里可以添加性能数据上报逻辑
  });
  
  // 监控资源加载
  const observer = new PerformanceObserver(list => {
    for (const entry of list.getEntries()) {
      if (entry.entryType === 'resource') {
        const resourceEntry = entry as PerformanceResourceTiming;
        if (resourceEntry.duration > 1000) {
          console.warn(`慢资源加载: ${resourceEntry.name} - ${resourceEntry.duration}ms`);
        }
      }
    }
  });
  
  observer.observe({ entryTypes: ['resource'] });
}
