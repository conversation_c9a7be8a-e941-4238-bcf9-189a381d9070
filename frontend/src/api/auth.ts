// 自动驾驶开发加速系统 - 认证API
import { get, post } from './client';
import type { User, LoginRequest, LoginResponse, RegisterRequest } from '@types/auth';

/**
 * 认证相关API
 */
export const authAPI = {
  /**
   * 用户登录
   */
  login: (credentials: LoginRequest): Promise<LoginResponse> => {
    return post<LoginResponse>('/auth/login', credentials);
  },

  /**
   * 用户注册
   */
  register: (userData: RegisterRequest): Promise<User> => {
    return post<User>('/auth/register', userData);
  },

  /**
   * 用户登出
   */
  logout: (): Promise<void> => {
    return post<void>('/auth/logout');
  },

  /**
   * 刷新访问令牌
   */
  refreshToken: (refreshToken: string): Promise<LoginResponse> => {
    return post<LoginResponse>('/auth/refresh', { refresh_token: refreshToken });
  },

  /**
   * 获取当前用户信息
   */
  getCurrentUser: (): Promise<User> => {
    return get<User>('/auth/me');
  },

  /**
   * 更新用户信息
   */
  updateProfile: (userData: Partial<User>): Promise<User> => {
    return post<User>('/auth/profile', userData);
  },

  /**
   * 修改密码
   */
  changePassword: (data: {
    current_password: string;
    new_password: string;
    confirm_password: string;
  }): Promise<void> => {
    return post<void>('/auth/change-password', data);
  },

  /**
   * 忘记密码
   */
  forgotPassword: (email: string): Promise<void> => {
    return post<void>('/auth/forgot-password', { email });
  },

  /**
   * 重置密码
   */
  resetPassword: (data: {
    token: string;
    password: string;
    confirm_password: string;
  }): Promise<void> => {
    return post<void>('/auth/reset-password', data);
  },

  /**
   * 验证邮箱
   */
  verifyEmail: (token: string): Promise<void> => {
    return post<void>('/auth/verify-email', { token });
  },

  /**
   * 重新发送验证邮件
   */
  resendVerificationEmail: (): Promise<void> => {
    return post<void>('/auth/resend-verification');
  },
};
