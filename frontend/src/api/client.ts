// 自动驾驶开发加速系统 - API客户端封装
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
import { message } from 'antd';

import { storage } from '@utils/storage';
import { store } from '@store/index';
import { logout, refreshToken } from '@store/slices/authSlice';

// API基础配置
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || '/api/v1';
const REQUEST_TIMEOUT = 30000; // 30秒超时

/**
 * API响应数据结构
 */
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  timestamp: number;
}

/**
 * 分页响应数据结构
 */
export interface PaginatedResponse<T = any> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

/**
 * 分页请求参数
 */
export interface PaginationParams {
  page?: number;
  pageSize?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * 创建Axios实例
 */
const createApiClient = (): AxiosInstance => {
  const client = axios.create({
    baseURL: API_BASE_URL,
    timeout: REQUEST_TIMEOUT,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
  });

  // 请求拦截器
  client.interceptors.request.use(
    (config) => {
      // 添加认证令牌
      const token = storage.getToken();
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }

      // 添加请求ID用于追踪
      config.headers['X-Request-ID'] = generateRequestId();

      // 添加时间戳
      config.headers['X-Timestamp'] = Date.now().toString();

      // 开发环境下打印请求信息
      if (import.meta.env.DEV) {
        console.log(`🚀 API请求: ${config.method?.toUpperCase()} ${config.url}`, {
          params: config.params,
          data: config.data,
        });
      }

      return config;
    },
    (error) => {
      console.error('请求拦截器错误:', error);
      return Promise.reject(error);
    }
  );

  // 响应拦截器
  client.interceptors.response.use(
    (response: AxiosResponse<ApiResponse>) => {
      // 开发环境下打印响应信息
      if (import.meta.env.DEV) {
        console.log(`✅ API响应: ${response.config.method?.toUpperCase()} ${response.config.url}`, {
          status: response.status,
          data: response.data,
        });
      }

      // 检查业务状态码
      if (response.data.code !== 0) {
        const errorMessage = response.data.message || '请求失败';
        message.error(errorMessage);
        return Promise.reject(new Error(errorMessage));
      }

      return response;
    },
    async (error: AxiosError<ApiResponse>) => {
      const { response, config } = error;

      // 开发环境下打印错误信息
      if (import.meta.env.DEV) {
        console.error(`❌ API错误: ${config?.method?.toUpperCase()} ${config?.url}`, {
          status: response?.status,
          data: response?.data,
          message: error.message,
        });
      }

      // 处理不同的HTTP状态码
      if (response) {
        switch (response.status) {
          case 401:
            // 未授权，尝试刷新令牌
            if (config && !config.url?.includes('/auth/refresh')) {
              try {
                await store.dispatch(refreshToken()).unwrap();
                // 重新发送原请求
                const token = storage.getToken();
                if (token && config.headers) {
                  config.headers.Authorization = `Bearer ${token}`;
                }
                return client.request(config);
              } catch (refreshError) {
                // 刷新失败，登出用户
                store.dispatch(logout());
                message.error('登录已过期，请重新登录');
                return Promise.reject(refreshError);
              }
            } else {
              // 刷新令牌失败，登出用户
              store.dispatch(logout());
              message.error('登录已过期，请重新登录');
            }
            break;

          case 403:
            message.error('权限不足，无法访问该资源');
            break;

          case 404:
            message.error('请求的资源不存在');
            break;

          case 422:
            // 表单验证错误
            const validationErrors = response.data?.data;
            if (validationErrors && typeof validationErrors === 'object') {
              const errorMessages = Object.values(validationErrors).flat();
              message.error(errorMessages.join(', '));
            } else {
              message.error(response.data?.message || '请求参数错误');
            }
            break;

          case 429:
            message.error('请求过于频繁，请稍后再试');
            break;

          case 500:
            message.error('服务器内部错误，请稍后再试');
            break;

          case 502:
          case 503:
          case 504:
            message.error('服务暂时不可用，请稍后再试');
            break;

          default:
            message.error(response.data?.message || '网络错误，请检查网络连接');
        }
      } else if (error.code === 'ECONNABORTED') {
        message.error('请求超时，请稍后再试');
      } else if (error.message === 'Network Error') {
        message.error('网络连接失败，请检查网络设置');
      } else {
        message.error('未知错误，请稍后再试');
      }

      return Promise.reject(error);
    }
  );

  return client;
};

/**
 * 生成请求ID
 */
const generateRequestId = (): string => {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
};

/**
 * API客户端实例
 */
export const apiClient = createApiClient();

/**
 * GET请求封装
 */
export const get = <T = any>(
  url: string,
  params?: any,
  config?: AxiosRequestConfig
): Promise<T> => {
  return apiClient.get<ApiResponse<T>>(url, { params, ...config }).then(res => res.data.data);
};

/**
 * POST请求封装
 */
export const post = <T = any>(
  url: string,
  data?: any,
  config?: AxiosRequestConfig
): Promise<T> => {
  return apiClient.post<ApiResponse<T>>(url, data, config).then(res => res.data.data);
};

/**
 * PUT请求封装
 */
export const put = <T = any>(
  url: string,
  data?: any,
  config?: AxiosRequestConfig
): Promise<T> => {
  return apiClient.put<ApiResponse<T>>(url, data, config).then(res => res.data.data);
};

/**
 * PATCH请求封装
 */
export const patch = <T = any>(
  url: string,
  data?: any,
  config?: AxiosRequestConfig
): Promise<T> => {
  return apiClient.patch<ApiResponse<T>>(url, data, config).then(res => res.data.data);
};

/**
 * DELETE请求封装
 */
export const del = <T = any>(
  url: string,
  config?: AxiosRequestConfig
): Promise<T> => {
  return apiClient.delete<ApiResponse<T>>(url, config).then(res => res.data.data);
};

/**
 * 文件上传封装
 */
export const upload = <T = any>(
  url: string,
  file: File,
  onProgress?: (progress: number) => void,
  config?: AxiosRequestConfig
): Promise<T> => {
  const formData = new FormData();
  formData.append('file', file);

  return apiClient.post<ApiResponse<T>>(url, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    onUploadProgress: (progressEvent) => {
      if (onProgress && progressEvent.total) {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        onProgress(progress);
      }
    },
    ...config,
  }).then(res => res.data.data);
};

/**
 * 文件下载封装
 */
export const download = (
  url: string,
  filename?: string,
  config?: AxiosRequestConfig
): Promise<void> => {
  return apiClient.get(url, {
    responseType: 'blob',
    ...config,
  }).then(response => {
    const blob = new Blob([response.data]);
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = filename || 'download';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);
  });
};

/**
 * 批量请求封装
 */
export const batch = <T = any>(
  requests: Array<() => Promise<any>>
): Promise<T[]> => {
  return Promise.all(requests.map(request => request()));
};

/**
 * 重试请求封装
 */
export const retry = <T = any>(
  request: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> => {
  return new Promise((resolve, reject) => {
    let retries = 0;

    const attemptRequest = () => {
      request()
        .then(resolve)
        .catch(error => {
          retries++;
          if (retries <= maxRetries) {
            setTimeout(attemptRequest, delay * retries);
          } else {
            reject(error);
          }
        });
    };

    attemptRequest();
  });
};

/**
 * 取消请求令牌
 */
export const createCancelToken = () => {
  return axios.CancelToken.source();
};

/**
 * 检查请求是否被取消
 */
export const isCancel = (error: any): boolean => {
  return axios.isCancel(error);
};

export default apiClient;
