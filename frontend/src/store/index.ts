// 自动驾驶开发加速系统 - Redux Store配置
import { configureStore } from '@reduxjs/toolkit';
import type { TypedUseSelectorHook } from 'react-redux';
import { useDispatch, useSelector } from 'react-redux';

// 导入各个slice
import authSlice from './slices/authSlice';
import userSlice from './slices/userSlice';
import themeSlice from './slices/themeSlice';
import projectSlice from './slices/projectSlice';
import simulationSlice from './slices/simulationSlice';
import mapSlice from './slices/mapSlice';
import systemSlice from './slices/systemSlice';

// 创建store
export const store = configureStore({
  reducer: {
    auth: authSlice,
    user: userSlice,
    theme: themeSlice,
    project: projectSlice,
    simulation: simulationSlice,
    map: mapSlice,
    system: systemSlice,
  },
  middleware: getDefaultMiddleware =>
    getDefaultMiddleware({
      serializableCheck: {
        // 忽略这些action类型的序列化检查
        ignoredActions: [
          'persist/PERSIST',
          'persist/REHYDRATE',
          'persist/PAUSE',
          'persist/PURGE',
          'persist/REGISTER',
        ],
        // 忽略这些路径的序列化检查
        ignoredPaths: ['register'],
      },
    }),
  devTools: import.meta.env.DEV,
});

// 导出类型
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// 导出类型化的hooks
export const useAppDispatch: () => AppDispatch = useDispatch;
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

// 导出store
export default store;
