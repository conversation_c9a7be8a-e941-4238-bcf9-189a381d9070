// 自动驾驶开发加速系统 - 用户状态管理
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';

import { get, post, put, del } from '@api/client';
import type { User } from '@types/auth';

// 用户状态接口
export interface UserState {
  // 用户列表
  users: User[];
  
  // 当前查看的用户
  currentViewUser: User | null;
  
  // 加载状态
  isLoading: boolean;
  isCreating: boolean;
  isUpdating: boolean;
  isDeleting: boolean;
  
  // 分页信息
  pagination: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
  
  // 搜索和过滤
  searchKeyword: string;
  filters: {
    role?: string;
    status?: string;
    department?: string;
  };
  
  // 错误信息
  error: string | null;
}

// 初始状态
const initialState: UserState = {
  users: [],
  currentViewUser: null,
  isLoading: false,
  isCreating: false,
  isUpdating: false,
  isDeleting: false,
  pagination: {
    page: 1,
    pageSize: 20,
    total: 0,
    totalPages: 0,
  },
  searchKeyword: '',
  filters: {},
  error: null,
};

// 异步actions

/**
 * 获取用户列表
 */
export const fetchUsers = createAsyncThunk(
  'user/fetchUsers',
  async (params: {
    page?: number;
    pageSize?: number;
    search?: string;
    role?: string;
    status?: string;
  } = {}) => {
    const response = await get('/users', params);
    return response;
  }
);

/**
 * 获取用户详情
 */
export const fetchUserById = createAsyncThunk(
  'user/fetchUserById',
  async (userId: string) => {
    const response = await get(`/users/${userId}`);
    return response;
  }
);

/**
 * 创建用户
 */
export const createUser = createAsyncThunk(
  'user/createUser',
  async (userData: Omit<User, 'id' | 'created_at' | 'updated_at'>) => {
    const response = await post('/users', userData);
    return response;
  }
);

/**
 * 更新用户
 */
export const updateUser = createAsyncThunk(
  'user/updateUser',
  async ({ id, ...userData }: Partial<User> & { id: string }) => {
    const response = await put(`/users/${id}`, userData);
    return response;
  }
);

/**
 * 删除用户
 */
export const deleteUser = createAsyncThunk(
  'user/deleteUser',
  async (userId: string) => {
    await del(`/users/${userId}`);
    return userId;
  }
);

/**
 * 批量删除用户
 */
export const batchDeleteUsers = createAsyncThunk(
  'user/batchDeleteUsers',
  async (userIds: string[]) => {
    await post('/users/batch-delete', { user_ids: userIds });
    return userIds;
  }
);

/**
 * 重置用户密码
 */
export const resetUserPassword = createAsyncThunk(
  'user/resetUserPassword',
  async (userId: string) => {
    const response = await post(`/users/${userId}/reset-password`);
    return response;
  }
);

/**
 * 启用/禁用用户
 */
export const toggleUserStatus = createAsyncThunk(
  'user/toggleUserStatus',
  async ({ userId, status }: { userId: string; status: 'active' | 'inactive' }) => {
    const response = await put(`/users/${userId}/status`, { status });
    return response;
  }
);

// 创建slice
const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    // 设置搜索关键词
    setSearchKeyword: (state, action: PayloadAction<string>) => {
      state.searchKeyword = action.payload;
    },
    
    // 设置过滤条件
    setFilters: (state, action: PayloadAction<Partial<UserState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    
    // 清除过滤条件
    clearFilters: (state) => {
      state.filters = {};
      state.searchKeyword = '';
    },
    
    // 设置分页
    setPagination: (state, action: PayloadAction<Partial<UserState['pagination']>>) => {
      state.pagination = { ...state.pagination, ...action.payload };
    },
    
    // 清除错误
    clearError: (state) => {
      state.error = null;
    },
    
    // 清除当前查看用户
    clearCurrentViewUser: (state) => {
      state.currentViewUser = null;
    },
  },
  extraReducers: (builder) => {
    // 获取用户列表
    builder
      .addCase(fetchUsers.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchUsers.fulfilled, (state, action) => {
        state.isLoading = false;
        state.users = action.payload.items;
        state.pagination = {
          page: action.payload.page,
          pageSize: action.payload.pageSize,
          total: action.payload.total,
          totalPages: action.payload.totalPages,
        };
      })
      .addCase(fetchUsers.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || '获取用户列表失败';
      });
    
    // 获取用户详情
    builder
      .addCase(fetchUserById.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchUserById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.currentViewUser = action.payload;
      })
      .addCase(fetchUserById.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || '获取用户详情失败';
      });
    
    // 创建用户
    builder
      .addCase(createUser.pending, (state) => {
        state.isCreating = true;
        state.error = null;
      })
      .addCase(createUser.fulfilled, (state, action) => {
        state.isCreating = false;
        state.users.unshift(action.payload);
        state.pagination.total += 1;
      })
      .addCase(createUser.rejected, (state, action) => {
        state.isCreating = false;
        state.error = action.error.message || '创建用户失败';
      });
    
    // 更新用户
    builder
      .addCase(updateUser.pending, (state) => {
        state.isUpdating = true;
        state.error = null;
      })
      .addCase(updateUser.fulfilled, (state, action) => {
        state.isUpdating = false;
        const index = state.users.findIndex(user => user.id === action.payload.id);
        if (index !== -1) {
          state.users[index] = action.payload;
        }
        if (state.currentViewUser?.id === action.payload.id) {
          state.currentViewUser = action.payload;
        }
      })
      .addCase(updateUser.rejected, (state, action) => {
        state.isUpdating = false;
        state.error = action.error.message || '更新用户失败';
      });
    
    // 删除用户
    builder
      .addCase(deleteUser.pending, (state) => {
        state.isDeleting = true;
        state.error = null;
      })
      .addCase(deleteUser.fulfilled, (state, action) => {
        state.isDeleting = false;
        state.users = state.users.filter(user => user.id !== action.payload);
        state.pagination.total -= 1;
        if (state.currentViewUser?.id === action.payload) {
          state.currentViewUser = null;
        }
      })
      .addCase(deleteUser.rejected, (state, action) => {
        state.isDeleting = false;
        state.error = action.error.message || '删除用户失败';
      });
    
    // 批量删除用户
    builder
      .addCase(batchDeleteUsers.pending, (state) => {
        state.isDeleting = true;
        state.error = null;
      })
      .addCase(batchDeleteUsers.fulfilled, (state, action) => {
        state.isDeleting = false;
        state.users = state.users.filter(user => !action.payload.includes(user.id));
        state.pagination.total -= action.payload.length;
      })
      .addCase(batchDeleteUsers.rejected, (state, action) => {
        state.isDeleting = false;
        state.error = action.error.message || '批量删除用户失败';
      });
    
    // 切换用户状态
    builder
      .addCase(toggleUserStatus.fulfilled, (state, action) => {
        const index = state.users.findIndex(user => user.id === action.payload.id);
        if (index !== -1) {
          state.users[index] = action.payload;
        }
        if (state.currentViewUser?.id === action.payload.id) {
          state.currentViewUser = action.payload;
        }
      });
  },
});

// 导出actions
export const {
  setSearchKeyword,
  setFilters,
  clearFilters,
  setPagination,
  clearError,
  clearCurrentViewUser,
} = userSlice.actions;

// 导出reducer
export default userSlice.reducer;

// 选择器
export const selectUsers = (state: { user: UserState }) => state.user.users;
export const selectCurrentViewUser = (state: { user: UserState }) => state.user.currentViewUser;
export const selectUserLoading = (state: { user: UserState }) => state.user.isLoading;
export const selectUserPagination = (state: { user: UserState }) => state.user.pagination;
export const selectUserFilters = (state: { user: UserState }) => state.user.filters;
export const selectUserError = (state: { user: UserState }) => state.user.error;
