// 自动驾驶开发加速系统 - 认证状态管理
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';

import { authAPI } from '@api/auth';
import { storage } from '@utils/storage';
import type { User, LoginRequest, LoginResponse } from '@types/auth';

// 认证状态接口
export interface AuthState {
  // 认证状态
  isAuthenticated: boolean;
  isInitialized: boolean;
  isLoading: boolean;
  
  // 用户信息
  user: User | null;
  
  // 令牌信息
  accessToken: string | null;
  refreshToken: string | null;
  tokenExpiry: number | null;
  
  // 错误信息
  error: string | null;
  
  // 权限信息
  permissions: string[];
  roles: string[];
}

// 初始状态
const initialState: AuthState = {
  isAuthenticated: false,
  isInitialized: false,
  isLoading: false,
  user: null,
  accessToken: null,
  refreshToken: null,
  tokenExpiry: null,
  error: null,
  permissions: [],
  roles: [],
};

// 异步actions

/**
 * 用户登录
 */
export const login = createAsyncThunk(
  'auth/login',
  async (credentials: LoginRequest, { rejectWithValue }) => {
    try {
      const response = await authAPI.login(credentials);
      
      // 保存令牌到本地存储
      storage.setToken(response.access_token);
      storage.setRefreshToken(response.refresh_token);
      
      return response;
    } catch (error: any) {
      return rejectWithValue(error.message || '登录失败');
    }
  }
);

/**
 * 用户登出
 */
export const logout = createAsyncThunk(
  'auth/logout',
  async (_, { getState, rejectWithValue }) => {
    try {
      const state = getState() as { auth: AuthState };
      const { accessToken } = state.auth;
      
      if (accessToken) {
        await authAPI.logout();
      }
      
      // 清除本地存储
      storage.removeToken();
      storage.removeRefreshToken();
      storage.removeUser();
      
      return null;
    } catch (error: any) {
      // 即使登出API失败，也要清除本地状态
      storage.removeToken();
      storage.removeRefreshToken();
      storage.removeUser();
      
      return rejectWithValue(error.message || '登出失败');
    }
  }
);

/**
 * 刷新令牌
 */
export const refreshToken = createAsyncThunk(
  'auth/refreshToken',
  async (_, { getState, rejectWithValue }) => {
    try {
      const state = getState() as { auth: AuthState };
      const { refreshToken: token } = state.auth;
      
      if (!token) {
        throw new Error('没有刷新令牌');
      }
      
      const response = await authAPI.refreshToken(token);
      
      // 更新本地存储
      storage.setToken(response.access_token);
      storage.setRefreshToken(response.refresh_token);
      
      return response;
    } catch (error: any) {
      // 刷新失败，清除所有认证信息
      storage.removeToken();
      storage.removeRefreshToken();
      storage.removeUser();
      
      return rejectWithValue(error.message || '令牌刷新失败');
    }
  }
);

/**
 * 获取当前用户信息
 */
export const getCurrentUser = createAsyncThunk(
  'auth/getCurrentUser',
  async (_, { rejectWithValue }) => {
    try {
      const user = await authAPI.getCurrentUser();
      
      // 保存用户信息到本地存储
      storage.setUser(user);
      
      return user;
    } catch (error: any) {
      return rejectWithValue(error.message || '获取用户信息失败');
    }
  }
);

/**
 * 初始化认证状态
 */
export const initializeAuth = createAsyncThunk(
  'auth/initialize',
  async (_, { dispatch, rejectWithValue }) => {
    try {
      const token = storage.getToken();
      const refreshTokenValue = storage.getRefreshToken();
      const user = storage.getUser();
      
      if (!token || !refreshTokenValue) {
        return { user: null, token: null, refreshToken: null };
      }
      
      // 检查令牌是否过期
      const tokenExpiry = storage.getTokenExpiry();
      const now = Date.now();
      
      if (tokenExpiry && now >= tokenExpiry) {
        // 令牌已过期，尝试刷新
        await dispatch(refreshToken()).unwrap();
        
        // 获取最新的用户信息
        const currentUser = await dispatch(getCurrentUser()).unwrap();
        
        return {
          user: currentUser,
          token: storage.getToken(),
          refreshToken: storage.getRefreshToken(),
        };
      }
      
      // 令牌有效，获取用户信息
      if (!user) {
        const currentUser = await dispatch(getCurrentUser()).unwrap();
        return {
          user: currentUser,
          token,
          refreshToken: refreshTokenValue,
        };
      }
      
      return {
        user,
        token,
        refreshToken: refreshTokenValue,
      };
    } catch (error: any) {
      // 初始化失败，清除所有认证信息
      storage.removeToken();
      storage.removeRefreshToken();
      storage.removeUser();
      
      return rejectWithValue(error.message || '认证初始化失败');
    }
  }
);

// 创建slice
const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    // 清除错误
    clearError: state => {
      state.error = null;
    },
    
    // 设置加载状态
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    
    // 更新用户信息
    updateUser: (state, action: PayloadAction<Partial<User>>) => {
      if (state.user) {
        state.user = { ...state.user, ...action.payload };
        storage.setUser(state.user);
      }
    },
    
    // 设置权限
    setPermissions: (state, action: PayloadAction<string[]>) => {
      state.permissions = action.payload;
    },
    
    // 设置角色
    setRoles: (state, action: PayloadAction<string[]>) => {
      state.roles = action.payload;
    },
  },
  extraReducers: builder => {
    // 登录
    builder
      .addCase(login.pending, state => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(login.fulfilled, (state, action) => {
        const { user, access_token, refresh_token, expires_in } = action.payload;
        
        state.isLoading = false;
        state.isAuthenticated = true;
        state.user = user;
        state.accessToken = access_token;
        state.refreshToken = refresh_token;
        state.tokenExpiry = Date.now() + expires_in * 1000;
        state.error = null;
        
        // 设置用户角色和权限
        state.roles = [user.role];
        // 这里可以根据角色设置权限
        state.permissions = [];
      })
      .addCase(login.rejected, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = false;
        state.user = null;
        state.accessToken = null;
        state.refreshToken = null;
        state.tokenExpiry = null;
        state.error = action.payload as string;
        state.permissions = [];
        state.roles = [];
      });
    
    // 登出
    builder
      .addCase(logout.pending, state => {
        state.isLoading = true;
      })
      .addCase(logout.fulfilled, state => {
        state.isLoading = false;
        state.isAuthenticated = false;
        state.user = null;
        state.accessToken = null;
        state.refreshToken = null;
        state.tokenExpiry = null;
        state.error = null;
        state.permissions = [];
        state.roles = [];
      })
      .addCase(logout.rejected, (state, action) => {
        // 即使登出失败，也要清除状态
        state.isLoading = false;
        state.isAuthenticated = false;
        state.user = null;
        state.accessToken = null;
        state.refreshToken = null;
        state.tokenExpiry = null;
        state.error = action.payload as string;
        state.permissions = [];
        state.roles = [];
      });
    
    // 刷新令牌
    builder
      .addCase(refreshToken.pending, state => {
        state.isLoading = true;
      })
      .addCase(refreshToken.fulfilled, (state, action) => {
        const { access_token, refresh_token, expires_in } = action.payload;
        
        state.isLoading = false;
        state.accessToken = access_token;
        state.refreshToken = refresh_token;
        state.tokenExpiry = Date.now() + expires_in * 1000;
        state.error = null;
      })
      .addCase(refreshToken.rejected, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = false;
        state.user = null;
        state.accessToken = null;
        state.refreshToken = null;
        state.tokenExpiry = null;
        state.error = action.payload as string;
        state.permissions = [];
        state.roles = [];
      });
    
    // 获取当前用户
    builder
      .addCase(getCurrentUser.fulfilled, (state, action) => {
        state.user = action.payload;
        state.roles = [action.payload.role];
      })
      .addCase(getCurrentUser.rejected, (state, action) => {
        state.error = action.payload as string;
      });
    
    // 初始化认证
    builder
      .addCase(initializeAuth.pending, state => {
        state.isLoading = true;
        state.isInitialized = false;
      })
      .addCase(initializeAuth.fulfilled, (state, action) => {
        const { user, token, refreshToken: refreshTokenValue } = action.payload;
        
        state.isLoading = false;
        state.isInitialized = true;
        
        if (user && token && refreshTokenValue) {
          state.isAuthenticated = true;
          state.user = user;
          state.accessToken = token;
          state.refreshToken = refreshTokenValue;
          state.roles = [user.role];
        } else {
          state.isAuthenticated = false;
          state.user = null;
          state.accessToken = null;
          state.refreshToken = null;
        }
        
        state.error = null;
      })
      .addCase(initializeAuth.rejected, (state, action) => {
        state.isLoading = false;
        state.isInitialized = true;
        state.isAuthenticated = false;
        state.user = null;
        state.accessToken = null;
        state.refreshToken = null;
        state.tokenExpiry = null;
        state.error = action.payload as string;
        state.permissions = [];
        state.roles = [];
      });
  },
});

// 导出actions
export const { clearError, setLoading, updateUser, setPermissions, setRoles } =
  authSlice.actions;

// 导出reducer
export default authSlice.reducer;

// 选择器
export const selectAuth = (state: { auth: AuthState }) => state.auth;
export const selectIsAuthenticated = (state: { auth: AuthState }) =>
  state.auth.isAuthenticated;
export const selectCurrentUser = (state: { auth: AuthState }) => state.auth.user;
export const selectUserRole = (state: { auth: AuthState }) => state.auth.user?.role;
export const selectUserPermissions = (state: { auth: AuthState }) =>
  state.auth.permissions;
export const selectIsLoading = (state: { auth: AuthState }) => state.auth.isLoading;
export const selectAuthError = (state: { auth: AuthState }) => state.auth.error;
