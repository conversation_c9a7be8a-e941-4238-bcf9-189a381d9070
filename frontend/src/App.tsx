// 自动驾驶开发加速系统 - 主应用组件
import React, { Suspense } from 'react';
import { BrowserRouter } from 'react-router-dom';
import { Provider } from 'react-redux';
import { QueryClient, QueryClientProvider } from 'react-query';
import { ConfigProvider, App as AntdApp } from 'antd';
import { HelmetProvider } from 'react-helmet-async';
import { ErrorBoundary } from 'react-error-boundary';
import zhCN from 'antd/locale/zh_CN';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';

import { store } from '@store/index';
import AppRouter from '@/router';
import GlobalLoading from '@components/common/GlobalLoading';
import ErrorFallback from '@components/common/ErrorFallback';
import { useTheme } from '@hooks/useTheme';
import { useAuth } from '@hooks/useAuth';

import '@styles/global.scss';

// 设置dayjs中文语言
dayjs.locale('zh-cn');

// 创建React Query客户端
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 3,
      retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
      staleTime: 5 * 60 * 1000, // 5分钟
      cacheTime: 10 * 60 * 1000, // 10分钟
      refetchOnWindowFocus: false,
      refetchOnReconnect: true,
    },
    mutations: {
      retry: 1,
    },
  },
});

/**
 * 应用内容组件
 * 包含主题配置和认证状态管理
 */
const AppContent: React.FC = () => {
  const { theme, isDarkMode } = useTheme();
  const { isInitialized } = useAuth();

  // Ant Design主题配置
  const antdTheme = {
    token: {
      colorPrimary: '#1890ff',
      colorSuccess: '#52c41a',
      colorWarning: '#faad14',
      colorError: '#f5222d',
      colorInfo: '#1890ff',
      borderRadius: 6,
      wireframe: false,
    },
    algorithm: isDarkMode ? 'darkAlgorithm' : 'defaultAlgorithm',
    components: {
      Layout: {
        headerBg: isDarkMode ? '#001529' : '#ffffff',
        siderBg: isDarkMode ? '#001529' : '#ffffff',
        bodyBg: isDarkMode ? '#141414' : '#f0f2f5',
      },
      Menu: {
        darkItemBg: '#001529',
        darkSubMenuItemBg: '#000c17',
        darkItemSelectedBg: '#1890ff',
      },
    },
  };

  // 如果认证状态未初始化，显示加载页面
  if (!isInitialized) {
    return <GlobalLoading tip='系统初始化中...' />;
  }

  return (
    <ConfigProvider locale={zhCN} theme={antdTheme}>
      <AntdApp>
        <div className={`app ${isDarkMode ? 'dark' : 'light'}`}>
          <Suspense fallback={<GlobalLoading tip='页面加载中...' />}>
            <AppRouter />
          </Suspense>
        </div>
      </AntdApp>
    </ConfigProvider>
  );
};

/**
 * 主应用组件
 * 提供全局状态管理、路由、错误边界等功能
 */
const App: React.FC = () => {
  return (
    <ErrorBoundary
      FallbackComponent={ErrorFallback}
      onError={(error, errorInfo) => {
        console.error('应用错误:', error, errorInfo);
        // 这里可以添加错误上报逻辑
      }}
      onReset={() => {
        // 重置应用状态
        window.location.reload();
      }}
    >
      <HelmetProvider>
        <Provider store={store}>
          <QueryClientProvider client={queryClient}>
            <BrowserRouter>
              <AppContent />
            </BrowserRouter>
          </QueryClientProvider>
        </Provider>
      </HelmetProvider>
    </ErrorBoundary>
  );
};

export default App;
