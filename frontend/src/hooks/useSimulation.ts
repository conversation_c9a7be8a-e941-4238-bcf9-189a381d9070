// 自动驾驶开发加速系统 - 仿真Hook
import { useState, useEffect, useCallback } from 'react';
import { message } from 'antd';

import { get, post, put, del } from '@api/client';

// 类型定义
interface Simulator {
  id: string;
  name: string;
  type: string;
  status: 'available' | 'busy' | 'offline';
  version: string;
  capabilities: string[];
}

interface Scenario {
  id: string;
  name: string;
  description: string;
  type: string;
  mapName: string;
  tags: string[];
  createdAt: string;
  modifiedAt: string;
}

interface SimulationConfig {
  simulator: string;
  scenario: string;
  speed?: number;
  synchronousMode?: boolean;
  recordData?: boolean;
  sensors?: string[];
}

interface SimulationInstance {
  id: string;
  simulatorId: string;
  scenarioId: string;
  status: 'starting' | 'running' | 'paused' | 'stopping' | 'stopped' | 'error';
  startTime: string;
  endTime?: string;
  config: SimulationConfig;
  vehicles: VehicleInfo[];
  sensors: SensorInfo[];
}

interface VehicleInfo {
  id: string;
  type: string;
  blueprintId: string;
  position: {
    x: number;
    y: number;
    z: number;
  };
  rotation: {
    pitch: number;
    yaw: number;
    roll: number;
  };
  velocity: {
    x: number;
    y: number;
    z: number;
  };
  isEgoVehicle: boolean;
}

interface SensorInfo {
  id: string;
  type: string;
  vehicleId: string;
  enabled: boolean;
  config: Record<string, any>;
}

interface SimulationStats {
  simulationTime: number;
  frameCount: number;
  fps: number;
  vehicleCount: number;
  sensorCount: number;
  dataPacketsReceived: number;
  memoryUsage: number;
  cpuUsage: number;
}

export const useSimulation = () => {
  // 状态
  const [simulators, setSimulators] = useState<Simulator[]>([]);
  const [scenarios, setScenarios] = useState<Scenario[]>([]);
  const [currentSimulation, setCurrentSimulation] = useState<SimulationInstance | null>(null);
  const [simulationStats, setSimulationStats] = useState<SimulationStats | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 获取可用仿真器列表
  const fetchSimulators = useCallback(async () => {
    try {
      const response = await get<Simulator[]>('/simulation/simulators');
      setSimulators(response);
    } catch (error: any) {
      console.error('获取仿真器列表失败:', error);
      setError(error.message);
    }
  }, []);

  // 获取场景列表
  const fetchScenarios = useCallback(async () => {
    try {
      const response = await get<{
        items: Scenario[];
        total: number;
      }>('/simulation/scenarios', {
        page: 1,
        pageSize: 100
      });
      setScenarios(response.items);
    } catch (error: any) {
      console.error('获取场景列表失败:', error);
      setError(error.message);
    }
  }, []);

  // 启动仿真
  const startSimulation = useCallback(async (config: SimulationConfig) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await post<SimulationInstance>('/simulation/start', config);
      setCurrentSimulation(response);
      message.success('仿真启动成功');
      return response;
    } catch (error: any) {
      setError(error.message);
      message.error(`仿真启动失败: ${error.message}`);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 停止仿真
  const stopSimulation = useCallback(async () => {
    if (!currentSimulation) {
      throw new Error('没有正在运行的仿真');
    }

    setIsLoading(true);
    setError(null);

    try {
      await post(`/simulation/${currentSimulation.id}/stop`);
      setCurrentSimulation(prev => prev ? { ...prev, status: 'stopped' } : null);
      message.success('仿真停止成功');
    } catch (error: any) {
      setError(error.message);
      message.error(`仿真停止失败: ${error.message}`);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [currentSimulation]);

  // 暂停仿真
  const pauseSimulation = useCallback(async () => {
    if (!currentSimulation) {
      throw new Error('没有正在运行的仿真');
    }

    try {
      await post(`/simulation/${currentSimulation.id}/pause`);
      setCurrentSimulation(prev => prev ? { ...prev, status: 'paused' } : null);
    } catch (error: any) {
      setError(error.message);
      throw error;
    }
  }, [currentSimulation]);

  // 恢复仿真
  const resumeSimulation = useCallback(async () => {
    if (!currentSimulation) {
      throw new Error('没有正在运行的仿真');
    }

    try {
      await post(`/simulation/${currentSimulation.id}/resume`);
      setCurrentSimulation(prev => prev ? { ...prev, status: 'running' } : null);
    } catch (error: any) {
      setError(error.message);
      throw error;
    }
  }, [currentSimulation]);

  // 重置仿真
  const resetSimulation = useCallback(async () => {
    if (!currentSimulation) {
      throw new Error('没有正在运行的仿真');
    }

    setIsLoading(true);
    setError(null);

    try {
      const response = await post<SimulationInstance>(`/simulation/${currentSimulation.id}/reset`);
      setCurrentSimulation(response);
      message.success('仿真重置成功');
    } catch (error: any) {
      setError(error.message);
      message.error(`仿真重置失败: ${error.message}`);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [currentSimulation]);

  // 加载场景
  const loadScenario = useCallback(async (scenarioId: string) => {
    setIsLoading(true);
    setError(null);

    try {
      await post('/simulation/load-scenario', { scenarioId });
      message.success('场景加载成功');
    } catch (error: any) {
      setError(error.message);
      message.error(`场景加载失败: ${error.message}`);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 获取仿真状态
  const getSimulationStatus = useCallback(async () => {
    if (!currentSimulation) return;

    try {
      const [simulationResponse, statsResponse] = await Promise.all([
        get<SimulationInstance>(`/simulation/${currentSimulation.id}`),
        get<SimulationStats>(`/simulation/${currentSimulation.id}/stats`)
      ]);

      setCurrentSimulation(simulationResponse);
      setSimulationStats(statsResponse);
    } catch (error: any) {
      console.error('获取仿真状态失败:', error);
      // 不显示错误消息，避免频繁提示
    }
  }, [currentSimulation]);

  // 设置仿真速度
  const setSimulationSpeed = useCallback(async (speed: number) => {
    if (!currentSimulation) {
      throw new Error('没有正在运行的仿真');
    }

    try {
      await put(`/simulation/${currentSimulation.id}/speed`, { speed });
    } catch (error: any) {
      setError(error.message);
      throw error;
    }
  }, [currentSimulation]);

  // 控制车辆
  const controlVehicle = useCallback(async (vehicleId: string, control: any) => {
    if (!currentSimulation) {
      throw new Error('没有正在运行的仿真');
    }

    try {
      await post(`/simulation/${currentSimulation.id}/vehicles/${vehicleId}/control`, control);
    } catch (error: any) {
      setError(error.message);
      throw error;
    }
  }, [currentSimulation]);

  // 切换传感器状态
  const toggleSensor = useCallback(async (sensorId: string, enabled: boolean) => {
    if (!currentSimulation) {
      throw new Error('没有正在运行的仿真');
    }

    try {
      await put(`/simulation/${currentSimulation.id}/sensors/${sensorId}`, { enabled });
      
      // 更新本地状态
      setCurrentSimulation(prev => {
        if (!prev) return prev;
        
        const updatedSensors = prev.sensors.map(sensor =>
          sensor.id === sensorId ? { ...sensor, enabled } : sensor
        );
        
        return { ...prev, sensors: updatedSensors };
      });
    } catch (error: any) {
      setError(error.message);
      throw error;
    }
  }, [currentSimulation]);

  // 开始数据记录
  const startDataRecording = useCallback(async (config: any) => {
    if (!currentSimulation) {
      throw new Error('没有正在运行的仿真');
    }

    try {
      await post(`/simulation/${currentSimulation.id}/recording/start`, config);
      message.success('数据记录已开始');
    } catch (error: any) {
      setError(error.message);
      message.error(`开始数据记录失败: ${error.message}`);
      throw error;
    }
  }, [currentSimulation]);

  // 停止数据记录
  const stopDataRecording = useCallback(async () => {
    if (!currentSimulation) {
      throw new Error('没有正在运行的仿真');
    }

    try {
      await post(`/simulation/${currentSimulation.id}/recording/stop`);
      message.success('数据记录已停止');
    } catch (error: any) {
      setError(error.message);
      message.error(`停止数据记录失败: ${error.message}`);
      throw error;
    }
  }, [currentSimulation]);

  // 获取仿真历史
  const getSimulationHistory = useCallback(async () => {
    try {
      const response = await get<{
        items: SimulationInstance[];
        total: number;
      }>('/simulation/history', {
        page: 1,
        pageSize: 50
      });
      return response.items;
    } catch (error: any) {
      setError(error.message);
      throw error;
    }
  }, []);

  // 清除错误
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // 初始化数据
  useEffect(() => {
    fetchSimulators();
    fetchScenarios();
  }, [fetchSimulators, fetchScenarios]);

  return {
    // 状态
    simulators,
    scenarios,
    currentSimulation,
    simulationStats,
    isLoading,
    error,

    // 操作方法
    startSimulation,
    stopSimulation,
    pauseSimulation,
    resumeSimulation,
    resetSimulation,
    loadScenario,
    getSimulationStatus,
    setSimulationSpeed,
    controlVehicle,
    toggleSensor,
    startDataRecording,
    stopDataRecording,
    getSimulationHistory,

    // 数据刷新
    fetchSimulators,
    fetchScenarios,
    clearError,
  };
};
