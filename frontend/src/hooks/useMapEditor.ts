// 自动驾驶开发加速系统 - 地图编辑器Hook
import { useState, useEffect, useCallback, useRef } from 'react';
import { message } from 'antd';

import { get, post, put, del } from '@api/client';

// 类型定义
interface Map {
  id: string;
  name: string;
  description?: string;
  format: string;
  status: string;
  coordinateSystem: string;
  boundingBox?: number[];
  centerPoint?: number[];
  zoomLevel?: number;
  version: string;
  createdAt: string;
  updatedAt: string;
  tags: string[];
  isPublic: boolean;
}

interface Layer {
  id: string;
  mapId: string;
  name: string;
  type: string;
  description?: string;
  displayOrder: number;
  visible: boolean;
  editable: boolean;
  styleConfig?: any;
  dataSource?: any;
}

interface Feature {
  id: string;
  layerId: string;
  type: string;
  geometry: any;
  properties: any;
  style?: any;
  createdAt: string;
  updatedAt: string;
}

interface HistoryEntry {
  id: string;
  operationType: string;
  targetType: string;
  targetId: string;
  beforeData?: any;
  afterData?: any;
  timestamp: string;
  userId: string;
  description?: string;
}

interface Collaborator {
  id: string;
  username: string;
  role: string;
  color: string;
  cursorPosition?: number[];
  selection?: any;
  lastSeen: string;
  isActive: boolean;
}

export const useMapEditor = (mapId?: string) => {
  // 状态
  const [currentMap, setCurrentMap] = useState<Map | null>(null);
  const [layers, setLayers] = useState<Layer[]>([]);
  const [features, setFeatures] = useState<Feature[]>([]);
  const [history, setHistory] = useState<HistoryEntry[]>([]);
  const [collaborators, setCollaborators] = useState<Collaborator[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 历史管理
  const [undoStack, setUndoStack] = useState<HistoryEntry[]>([]);
  const [redoStack, setRedoStack] = useState<HistoryEntry[]>([]);
  const canUndo = undoStack.length > 0;
  const canRedo = redoStack.length > 0;

  // 引用
  const saveTimeoutRef = useRef<NodeJS.Timeout>();

  // 加载地图
  const loadMap = useCallback(async (id: string) => {
    setIsLoading(true);
    setError(null);

    try {
      // 并行加载地图数据
      const [mapResponse, layersResponse, featuresResponse, historyResponse] = await Promise.all([
        get<Map>(`/map-editor/maps/${id}`),
        get<Layer[]>(`/map-editor/maps/${id}/layers`),
        get<Feature[]>(`/map-editor/maps/${id}/features`),
        get<HistoryEntry[]>(`/map-editor/maps/${id}/history?limit=100`)
      ]);

      setCurrentMap(mapResponse);
      setLayers(layersResponse);
      setFeatures(featuresResponse);
      setHistory(historyResponse);
      setHasUnsavedChanges(false);

      message.success('地图加载成功');
    } catch (error: any) {
      setError(error.message);
      message.error(`地图加载失败: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 保存地图
  const saveMap = useCallback(async () => {
    if (!currentMap || !hasUnsavedChanges) return;

    setIsSaving(true);
    setError(null);

    try {
      // 保存地图基本信息
      await put(`/map-editor/maps/${currentMap.id}`, {
        name: currentMap.name,
        description: currentMap.description,
        tags: currentMap.tags,
        isPublic: currentMap.isPublic
      });

      // 保存图层
      await Promise.all(
        layers.map(layer =>
          put(`/map-editor/layers/${layer.id}`, layer)
        )
      );

      // 保存要素
      await Promise.all(
        features.map(feature =>
          put(`/map-editor/features/${feature.id}`, feature)
        )
      );

      setHasUnsavedChanges(false);
      message.success('地图保存成功');
    } catch (error: any) {
      setError(error.message);
      message.error(`地图保存失败: ${error.message}`);
      throw error;
    } finally {
      setIsSaving(false);
    }
  }, [currentMap, layers, features, hasUnsavedChanges]);

  // 自动保存
  const autoSave = useCallback(() => {
    if (saveTimeoutRef.current) {
      clearTimeout(saveTimeoutRef.current);
    }

    saveTimeoutRef.current = setTimeout(() => {
      if (hasUnsavedChanges) {
        saveMap().catch(console.error);
      }
    }, 30000); // 30秒后自动保存
  }, [hasUnsavedChanges, saveMap]);

  // 创建图层
  const createLayer = useCallback(async (layerData: Partial<Layer>) => {
    if (!currentMap) return;

    try {
      const newLayer: Layer = {
        id: `layer_${Date.now()}`,
        mapId: currentMap.id,
        name: layerData.name || '新图层',
        type: layerData.type || 'custom',
        description: layerData.description,
        displayOrder: layers.length,
        visible: true,
        editable: true,
        styleConfig: layerData.styleConfig,
        dataSource: layerData.dataSource,
        ...layerData
      };

      const response = await post<Layer>('/map-editor/layers', newLayer);
      setLayers(prev => [...prev, response]);
      setHasUnsavedChanges(true);

      // 记录历史
      recordHistory('create', 'layer', response.id, null, response);

      message.success('图层创建成功');
      return response;
    } catch (error: any) {
      message.error(`图层创建失败: ${error.message}`);
      throw error;
    }
  }, [currentMap, layers]);

  // 更新图层
  const updateLayer = useCallback(async (layerId: string, updates: Partial<Layer>) => {
    try {
      const layerIndex = layers.findIndex(l => l.id === layerId);
      if (layerIndex === -1) {
        throw new Error('图层不存在');
      }

      const oldLayer = layers[layerIndex];
      const updatedLayer = { ...oldLayer, ...updates };

      const response = await put<Layer>(`/map-editor/layers/${layerId}`, updatedLayer);
      
      setLayers(prev => prev.map(l => l.id === layerId ? response : l));
      setHasUnsavedChanges(true);

      // 记录历史
      recordHistory('update', 'layer', layerId, oldLayer, response);

      return response;
    } catch (error: any) {
      message.error(`图层更新失败: ${error.message}`);
      throw error;
    }
  }, [layers]);

  // 删除图层
  const deleteLayer = useCallback(async (layerId: string) => {
    try {
      const layer = layers.find(l => l.id === layerId);
      if (!layer) {
        throw new Error('图层不存在');
      }

      await del(`/map-editor/layers/${layerId}`);
      
      setLayers(prev => prev.filter(l => l.id !== layerId));
      // 同时删除该图层的所有要素
      setFeatures(prev => prev.filter(f => f.layerId !== layerId));
      setHasUnsavedChanges(true);

      // 记录历史
      recordHistory('delete', 'layer', layerId, layer, null);

      message.success('图层删除成功');
    } catch (error: any) {
      message.error(`图层删除失败: ${error.message}`);
      throw error;
    }
  }, [layers]);

  // 创建要素
  const createFeature = useCallback(async (featureData: any) => {
    try {
      const newFeature: Feature = {
        id: featureData.properties?.id || `feature_${Date.now()}`,
        layerId: featureData.layerId || layers[0]?.id || '',
        type: featureData.properties?.type || 'custom',
        geometry: featureData.geometry,
        properties: featureData.properties || {},
        style: featureData.style,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        ...featureData
      };

      const response = await post<Feature>('/map-editor/features', newFeature);
      setFeatures(prev => [...prev, response]);
      setHasUnsavedChanges(true);

      // 记录历史
      recordHistory('create', 'feature', response.id, null, response);

      return response;
    } catch (error: any) {
      message.error(`要素创建失败: ${error.message}`);
      throw error;
    }
  }, [layers]);

  // 更新要素
  const updateFeature = useCallback(async (featureId: string, updates: Partial<Feature>) => {
    try {
      const featureIndex = features.findIndex(f => f.id === featureId);
      if (featureIndex === -1) {
        throw new Error('要素不存在');
      }

      const oldFeature = features[featureIndex];
      const updatedFeature = { 
        ...oldFeature, 
        ...updates,
        updatedAt: new Date().toISOString()
      };

      const response = await put<Feature>(`/map-editor/features/${featureId}`, updatedFeature);
      
      setFeatures(prev => prev.map(f => f.id === featureId ? response : f));
      setHasUnsavedChanges(true);

      // 记录历史
      recordHistory('update', 'feature', featureId, oldFeature, response);

      return response;
    } catch (error: any) {
      message.error(`要素更新失败: ${error.message}`);
      throw error;
    }
  }, [features]);

  // 删除要素
  const deleteFeature = useCallback(async (featureId: string) => {
    try {
      const feature = features.find(f => f.id === featureId);
      if (!feature) {
        throw new Error('要素不存在');
      }

      await del(`/map-editor/features/${featureId}`);
      
      setFeatures(prev => prev.filter(f => f.id !== featureId));
      setHasUnsavedChanges(true);

      // 记录历史
      recordHistory('delete', 'feature', featureId, feature, null);

      return true;
    } catch (error: any) {
      message.error(`要素删除失败: ${error.message}`);
      throw error;
    }
  }, [features]);

  // 记录历史
  const recordHistory = useCallback((
    operationType: string,
    targetType: string,
    targetId: string,
    beforeData: any,
    afterData: any
  ) => {
    const historyEntry: HistoryEntry = {
      id: `history_${Date.now()}`,
      operationType,
      targetType,
      targetId,
      beforeData,
      afterData,
      timestamp: new Date().toISOString(),
      userId: 'current-user-id', // 应该从认证状态获取
      description: `${operationType} ${targetType}`
    };

    setHistory(prev => [historyEntry, ...prev.slice(0, 99)]); // 保留最近100条
    setUndoStack(prev => [historyEntry, ...prev]);
    setRedoStack([]); // 清空重做栈
  }, []);

  // 撤销操作
  const undo = useCallback(async () => {
    if (undoStack.length === 0) return;

    const lastOperation = undoStack[0];
    
    try {
      // 根据操作类型执行撤销
      switch (lastOperation.operationType) {
        case 'create':
          if (lastOperation.targetType === 'layer') {
            await deleteLayer(lastOperation.targetId);
          } else if (lastOperation.targetType === 'feature') {
            await deleteFeature(lastOperation.targetId);
          }
          break;
        case 'update':
          if (lastOperation.targetType === 'layer') {
            await updateLayer(lastOperation.targetId, lastOperation.beforeData);
          } else if (lastOperation.targetType === 'feature') {
            await updateFeature(lastOperation.targetId, lastOperation.beforeData);
          }
          break;
        case 'delete':
          if (lastOperation.targetType === 'layer') {
            await createLayer(lastOperation.beforeData);
          } else if (lastOperation.targetType === 'feature') {
            await createFeature(lastOperation.beforeData);
          }
          break;
      }

      setUndoStack(prev => prev.slice(1));
      setRedoStack(prev => [lastOperation, ...prev]);
      
      message.success('撤销成功');
    } catch (error: any) {
      message.error(`撤销失败: ${error.message}`);
    }
  }, [undoStack, deleteLayer, deleteFeature, updateLayer, updateFeature, createLayer, createFeature]);

  // 重做操作
  const redo = useCallback(async () => {
    if (redoStack.length === 0) return;

    const nextOperation = redoStack[0];
    
    try {
      // 根据操作类型执行重做
      switch (nextOperation.operationType) {
        case 'create':
          if (nextOperation.targetType === 'layer') {
            await createLayer(nextOperation.afterData);
          } else if (nextOperation.targetType === 'feature') {
            await createFeature(nextOperation.afterData);
          }
          break;
        case 'update':
          if (nextOperation.targetType === 'layer') {
            await updateLayer(nextOperation.targetId, nextOperation.afterData);
          } else if (nextOperation.targetType === 'feature') {
            await updateFeature(nextOperation.targetId, nextOperation.afterData);
          }
          break;
        case 'delete':
          if (nextOperation.targetType === 'layer') {
            await deleteLayer(nextOperation.targetId);
          } else if (nextOperation.targetType === 'feature') {
            await deleteFeature(nextOperation.targetId);
          }
          break;
      }

      setRedoStack(prev => prev.slice(1));
      setUndoStack(prev => [nextOperation, ...prev]);
      
      message.success('重做成功');
    } catch (error: any) {
      message.error(`重做失败: ${error.message}`);
    }
  }, [redoStack, createLayer, createFeature, updateLayer, updateFeature, deleteLayer, deleteFeature]);

  // 导出地图
  const exportMap = useCallback(async (format: string = 'geojson') => {
    if (!currentMap) {
      throw new Error('没有加载的地图');
    }

    try {
      const response = await get(`/map-editor/maps/${currentMap.id}/export?format=${format}`);
      return response;
    } catch (error: any) {
      message.error(`地图导出失败: ${error.message}`);
      throw error;
    }
  }, [currentMap]);

  // 导入地图
  const importMap = useCallback(async (data: any) => {
    if (!currentMap) {
      throw new Error('没有加载的地图');
    }

    try {
      const response = await post(`/map-editor/maps/${currentMap.id}/import`, data);
      
      // 重新加载地图数据
      await loadMap(currentMap.id);
      
      message.success('地图导入成功');
      return response;
    } catch (error: any) {
      message.error(`地图导入失败: ${error.message}`);
      throw error;
    }
  }, [currentMap, loadMap]);

  // 清除错误
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // 自动保存效果
  useEffect(() => {
    if (hasUnsavedChanges) {
      autoSave();
    }

    return () => {
      if (saveTimeoutRef.current) {
        clearTimeout(saveTimeoutRef.current);
      }
    };
  }, [hasUnsavedChanges, autoSave]);

  return {
    // 状态
    currentMap,
    layers,
    features,
    history,
    collaborators,
    isLoading,
    isSaving,
    hasUnsavedChanges,
    error,

    // 历史管理
    canUndo,
    canRedo,

    // 操作方法
    loadMap,
    saveMap,
    createLayer,
    updateLayer,
    deleteLayer,
    createFeature,
    updateFeature,
    deleteFeature,
    undo,
    redo,
    exportMap,
    importMap,

    // 工具方法
    clearError,
  };
};
