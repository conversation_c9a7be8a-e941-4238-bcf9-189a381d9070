// 自动驾驶开发加速系统 - 开发工具Hook
import { useState, useEffect } from 'react';
import { message } from 'antd';
import { apiClient } from '@utils/apiClient';

interface Project {
  id: string;
  name: string;
  description: string;
  template: string;
  status: 'active' | 'building' | 'error' | 'stopped';
  createdAt: string;
  lastBuild: string;
  buildProgress?: number;
}

interface Template {
  id: string;
  name: string;
  description: string;
  category: string;
  language: string;
  framework: string;
  version: string;
  parameters: TemplateParameter[];
}

interface TemplateParameter {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'select';
  description: string;
  required: boolean;
  defaultValue?: any;
  options?: string[];
}

interface CreateProjectRequest {
  name: string;
  description: string;
  templateId: string;
  parameters: Record<string, any>;
}

interface BuildStatus {
  projectId: string;
  status: string;
  progress: number;
  logs: string[];
}

export const useDevelopmentTools = () => {
  // 状态管理
  const [projects, setProjects] = useState<Project[]>([]);
  const [templates, setTemplates] = useState<Template[]>([]);
  const [buildStatus, setBuildStatus] = useState<Record<string, BuildStatus>>({});
  const [isLoading, setIsLoading] = useState(false);

  // 获取项目列表
  const fetchProjects = async () => {
    try {
      setIsLoading(true);
      const response = await apiClient.get('/api/v1/development-tools/projects');
      setProjects(response.data.data.projects || []);
    } catch (error: any) {
      message.error(`获取项目列表失败: ${error.message}`);
    } finally {
      setIsLoading(false);
    }
  };

  // 获取模板列表
  const fetchTemplates = async () => {
    try {
      const response = await apiClient.get('/api/v1/development-tools/templates');
      setTemplates(response.data.data.templates || []);
    } catch (error: any) {
      message.error(`获取模板列表失败: ${error.message}`);
    }
  };

  // 创建项目
  const createProject = async (projectData: CreateProjectRequest): Promise<Project> => {
    try {
      setIsLoading(true);
      const response = await apiClient.post('/api/v1/development-tools/projects', projectData);
      const newProject = response.data.data;
      
      setProjects(prev => [...prev, newProject]);
      return newProject;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '创建项目失败');
    } finally {
      setIsLoading(false);
    }
  };

  // 更新项目
  const updateProject = async (projectId: string, updates: Partial<Project>): Promise<Project> => {
    try {
      const response = await apiClient.put(`/api/v1/development-tools/projects/${projectId}`, updates);
      const updatedProject = response.data.data;
      
      setProjects(prev => prev.map(p => p.id === projectId ? updatedProject : p));
      return updatedProject;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '更新项目失败');
    }
  };

  // 删除项目
  const deleteProject = async (projectId: string): Promise<void> => {
    try {
      await apiClient.delete(`/api/v1/development-tools/projects/${projectId}`);
      setProjects(prev => prev.filter(p => p.id !== projectId));
      message.success('项目删除成功');
    } catch (error: any) {
      message.error(`删除项目失败: ${error.message}`);
    }
  };

  // 构建项目
  const buildProject = async (projectId: string): Promise<void> => {
    try {
      await apiClient.post(`/api/v1/development-tools/projects/${projectId}/build`);
      
      // 更新项目状态
      setProjects(prev => prev.map(p => 
        p.id === projectId ? { ...p, status: 'building', buildProgress: 0 } : p
      ));
      
      // 初始化构建状态
      setBuildStatus(prev => ({
        ...prev,
        [projectId]: {
          projectId,
          status: 'building',
          progress: 0,
          logs: []
        }
      }));
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '启动构建失败');
    }
  };

  // 停止构建
  const stopBuild = async (projectId: string): Promise<void> => {
    try {
      await apiClient.post(`/api/v1/development-tools/projects/${projectId}/build/stop`);
      
      // 更新项目状态
      setProjects(prev => prev.map(p => 
        p.id === projectId ? { ...p, status: 'stopped', buildProgress: 0 } : p
      ));
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '停止构建失败');
    }
  };

  // 获取项目文件列表
  const getProjectFiles = async (projectId: string): Promise<any[]> => {
    try {
      const response = await apiClient.get(`/api/v1/development-tools/projects/${projectId}/files`);
      return response.data.data.files || [];
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取文件列表失败');
    }
  };

  // 获取文件内容
  const getFileContent = async (projectId: string, filePath: string): Promise<string> => {
    try {
      const response = await apiClient.get(
        `/api/v1/development-tools/projects/${projectId}/files/${encodeURIComponent(filePath)}`
      );
      return response.data.data.content || '';
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取文件内容失败');
    }
  };

  // 保存文件内容
  const saveFileContent = async (projectId: string, filePath: string, content: string): Promise<void> => {
    try {
      await apiClient.put(
        `/api/v1/development-tools/projects/${projectId}/files/${encodeURIComponent(filePath)}`,
        { content }
      );
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '保存文件失败');
    }
  };

  // 下载项目
  const downloadProject = async (projectId: string): Promise<void> => {
    try {
      const response = await apiClient.get(
        `/api/v1/development-tools/projects/${projectId}/download`,
        { responseType: 'blob' }
      );
      
      // 创建下载链接
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `project-${projectId}.zip`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
      
      message.success('项目下载成功');
    } catch (error: any) {
      message.error(`下载项目失败: ${error.message}`);
    }
  };

  // 获取构建日志
  const getBuildLogs = async (projectId: string): Promise<string[]> => {
    try {
      const response = await apiClient.get(`/api/v1/development-tools/projects/${projectId}/build/logs`);
      return response.data.data.logs || [];
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取构建日志失败');
    }
  };

  // 创建模板
  const createTemplate = async (templateData: Omit<Template, 'id'>): Promise<Template> => {
    try {
      const response = await apiClient.post('/api/v1/development-tools/templates', templateData);
      const newTemplate = response.data.data;
      
      setTemplates(prev => [...prev, newTemplate]);
      return newTemplate;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '创建模板失败');
    }
  };

  // 更新模板
  const updateTemplate = async (templateId: string, updates: Partial<Template>): Promise<Template> => {
    try {
      const response = await apiClient.put(`/api/v1/development-tools/templates/${templateId}`, updates);
      const updatedTemplate = response.data.data;
      
      setTemplates(prev => prev.map(t => t.id === templateId ? updatedTemplate : t));
      return updatedTemplate;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '更新模板失败');
    }
  };

  // 删除模板
  const deleteTemplate = async (templateId: string): Promise<void> => {
    try {
      await apiClient.delete(`/api/v1/development-tools/templates/${templateId}`);
      setTemplates(prev => prev.filter(t => t.id !== templateId));
      message.success('模板删除成功');
    } catch (error: any) {
      message.error(`删除模板失败: ${error.message}`);
    }
  };

  // 预览模板
  const previewTemplate = async (templateId: string, parameters: Record<string, any>): Promise<string> => {
    try {
      const response = await apiClient.post(
        `/api/v1/development-tools/templates/${templateId}/preview`,
        { parameters }
      );
      return response.data.data.preview || '';
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '预览模板失败');
    }
  };

  // 验证模板
  const validateTemplate = async (templateData: Partial<Template>): Promise<{ valid: boolean; errors: string[] }> => {
    try {
      const response = await apiClient.post('/api/v1/development-tools/templates/validate', templateData);
      return response.data.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '验证模板失败');
    }
  };

  // 获取项目统计信息
  const getProjectStats = async (): Promise<any> => {
    try {
      const response = await apiClient.get('/api/v1/development-tools/stats');
      return response.data.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取统计信息失败');
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchProjects();
    fetchTemplates();
  }, []);

  return {
    // 状态
    projects,
    templates,
    buildStatus,
    isLoading,
    
    // 项目操作
    createProject,
    updateProject,
    deleteProject,
    buildProject,
    stopBuild,
    downloadProject,
    
    // 文件操作
    getProjectFiles,
    getFileContent,
    saveFileContent,
    getBuildLogs,
    
    // 模板操作
    createTemplate,
    updateTemplate,
    deleteTemplate,
    previewTemplate,
    validateTemplate,
    
    // 统计信息
    getProjectStats,
    
    // 刷新数据
    refreshProjects: fetchProjects,
    refreshTemplates: fetchTemplates,
  };
};
