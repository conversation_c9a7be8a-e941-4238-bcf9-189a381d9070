// 自动驾驶开发加速系统 - 认证Hook
import { useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { message } from 'antd';

import { useAppDispatch, useAppSelector } from '@store/index';
import {
  login as loginAction,
  logout as logoutAction,
  getCurrentUser,
  initializeAuth,
  clearError,
  selectAuth,
  selectIsAuthenticated,
  selectCurrentUser,
  selectIsLoading,
  selectAuthError,
} from '@store/slices/authSlice';
import type { LoginRequest } from '@types/auth';

/**
 * 认证Hook
 * 提供认证相关的状态和操作方法
 */
export const useAuth = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  // 选择器
  const auth = useAppSelector(selectAuth);
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const currentUser = useAppSelector(selectCurrentUser);
  const isLoading = useAppSelector(selectIsLoading);
  const error = useAppSelector(selectAuthError);

  /**
   * 用户登录
   */
  const login = useCallback(
    async (credentials: LoginRequest) => {
      try {
        await dispatch(loginAction(credentials)).unwrap();
        message.success('登录成功');
        navigate('/dashboard');
      } catch (error: any) {
        message.error(error.message || '登录失败');
        throw error;
      }
    },
    [dispatch, navigate]
  );

  /**
   * 用户登出
   */
  const logout = useCallback(async () => {
    try {
      await dispatch(logoutAction()).unwrap();
      message.success('已安全退出');
      navigate('/login');
    } catch (error: any) {
      // 即使登出API失败，也要清除本地状态
      message.warning('退出时发生错误，但已清除本地登录状态');
      navigate('/login');
    }
  }, [dispatch, navigate]);

  /**
   * 刷新用户信息
   */
  const refreshUserInfo = useCallback(async () => {
    try {
      await dispatch(getCurrentUser()).unwrap();
    } catch (error: any) {
      console.error('刷新用户信息失败:', error);
    }
  }, [dispatch]);

  /**
   * 清除错误信息
   */
  const clearAuthError = useCallback(() => {
    dispatch(clearError());
  }, [dispatch]);

  /**
   * 检查用户权限
   */
  const hasPermission = useCallback(
    (permission: string): boolean => {
      if (!currentUser) return false;
      return auth.permissions.includes(permission);
    },
    [currentUser, auth.permissions]
  );

  /**
   * 检查用户角色
   */
  const hasRole = useCallback(
    (role: string): boolean => {
      if (!currentUser) return false;
      return auth.roles.includes(role);
    },
    [currentUser, auth.roles]
  );

  /**
   * 检查是否为管理员
   */
  const isAdmin = useCallback((): boolean => {
    return hasRole('admin');
  }, [hasRole]);

  /**
   * 检查是否为开发者
   */
  const isDeveloper = useCallback((): boolean => {
    return hasRole('developer') || hasRole('admin');
  }, [hasRole]);

  /**
   * 检查是否为测试人员
   */
  const isTester = useCallback((): boolean => {
    return hasRole('tester') || hasRole('admin');
  }, [hasRole]);

  /**
   * 初始化认证状态
   */
  useEffect(() => {
    if (!auth.isInitialized) {
      dispatch(initializeAuth());
    }
  }, [dispatch, auth.isInitialized]);

  /**
   * 监听认证错误
   */
  useEffect(() => {
    if (error) {
      // 自动清除错误信息
      const timer = setTimeout(() => {
        clearAuthError();
      }, 5000);

      return () => clearTimeout(timer);
    }
  }, [error, clearAuthError]);

  return {
    // 状态
    isAuthenticated,
    isInitialized: auth.isInitialized,
    isLoading,
    error,
    currentUser,
    permissions: auth.permissions,
    roles: auth.roles,

    // 操作方法
    login,
    logout,
    refreshUserInfo,
    clearAuthError,

    // 权限检查方法
    hasPermission,
    hasRole,
    isAdmin,
    isDeveloper,
    isTester,
  };
};
