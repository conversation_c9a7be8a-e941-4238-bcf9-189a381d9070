// 自动驾驶开发加速系统 - 路由配置
import React, { Suspense } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';

import { useAppSelector } from '@store/index';
import { selectIsAuthenticated } from '@store/slices/authSlice';
import GlobalLoading from '@components/common/GlobalLoading';
import Layout from '@components/layout/Layout';

// 懒加载页面组件
const Login = React.lazy(() => import('@pages/auth/Login'));
const Dashboard = React.lazy(() => import('@pages/dashboard/Dashboard'));
const ProjectList = React.lazy(() => import('@pages/project/ProjectList'));
const ProjectDetail = React.lazy(() => import('@pages/project/ProjectDetail'));
const TemplateList = React.lazy(() => import('@pages/template/TemplateList'));
const TemplateEditor = React.lazy(() => import('@pages/template/TemplateEditor'));
const SimulationList = React.lazy(() => import('@pages/simulation/SimulationList'));
const SimulationDetail = React.lazy(() => import('@pages/simulation/SimulationDetail'));
const MapEditor = React.lazy(() => import('@pages/map/MapEditor'));
const MapList = React.lazy(() => import('@pages/map/MapList'));
const DeploymentList = React.lazy(() => import('@pages/deployment/DeploymentList'));
const DeploymentDetail = React.lazy(() => import('@pages/deployment/DeploymentDetail'));
const UserManagement = React.lazy(() => import('@pages/admin/UserManagement'));
const SystemSettings = React.lazy(() => import('@pages/admin/SystemSettings'));
const Profile = React.lazy(() => import('@pages/user/Profile'));
const NotFound = React.lazy(() => import('@pages/error/NotFound'));

/**
 * 受保护的路由组件
 * 需要用户登录才能访问
 */
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }
  
  return <>{children}</>;
};

/**
 * 公开路由组件
 * 已登录用户访问时重定向到仪表板
 */
const PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  
  if (isAuthenticated) {
    return <Navigate to="/dashboard" replace />;
  }
  
  return <>{children}</>;
};

/**
 * 管理员路由组件
 * 需要管理员权限才能访问
 */
const AdminRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const isAuthenticated = useAppSelector(selectIsAuthenticated);
  const user = useAppSelector(state => state.auth.user);
  
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }
  
  if (user?.role !== 'admin') {
    return <Navigate to="/dashboard" replace />;
  }
  
  return <>{children}</>;
};

/**
 * 主路由组件
 */
const AppRouter: React.FC = () => {
  return (
    <Suspense fallback={<GlobalLoading tip="页面加载中..." />}>
      <Routes>
        {/* 公开路由 */}
        <Route
          path="/login"
          element={
            <PublicRoute>
              <Login />
            </PublicRoute>
          }
        />
        
        {/* 受保护的路由 */}
        <Route
          path="/"
          element={
            <ProtectedRoute>
              <Layout />
            </ProtectedRoute>
          }
        >
          {/* 仪表板 */}
          <Route index element={<Navigate to="/dashboard" replace />} />
          <Route path="dashboard" element={<Dashboard />} />
          
          {/* 项目管理 */}
          <Route path="projects">
            <Route index element={<ProjectList />} />
            <Route path=":id" element={<ProjectDetail />} />
          </Route>
          
          {/* 代码模板 */}
          <Route path="templates">
            <Route index element={<TemplateList />} />
            <Route path="new" element={<TemplateEditor />} />
            <Route path=":id/edit" element={<TemplateEditor />} />
          </Route>
          
          {/* 仿真管理 */}
          <Route path="simulation">
            <Route index element={<SimulationList />} />
            <Route path=":id" element={<SimulationDetail />} />
          </Route>
          
          {/* 地图编辑 */}
          <Route path="maps">
            <Route index element={<MapList />} />
            <Route path="editor" element={<MapEditor />} />
            <Route path=":id/editor" element={<MapEditor />} />
          </Route>
          
          {/* 部署运维 */}
          <Route path="deployment">
            <Route index element={<DeploymentList />} />
            <Route path=":id" element={<DeploymentDetail />} />
          </Route>
          
          {/* 用户管理（管理员） */}
          <Route
            path="admin/users"
            element={
              <AdminRoute>
                <UserManagement />
              </AdminRoute>
            }
          />
          
          {/* 系统设置（管理员） */}
          <Route
            path="admin/settings"
            element={
              <AdminRoute>
                <SystemSettings />
              </AdminRoute>
            }
          />
          
          {/* 个人资料 */}
          <Route path="profile" element={<Profile />} />
        </Route>
        
        {/* 404页面 */}
        <Route path="*" element={<NotFound />} />
      </Routes>
    </Suspense>
  );
};

export default AppRouter;
