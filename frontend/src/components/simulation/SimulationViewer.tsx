// 自动驾驶开发加速系统 - 3D仿真视图组件
import React, { useRef, useEffect, forwardRef, useImperativeHandle } from 'react';
import { Card, Spin, Alert } from 'antd';
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';

interface SimulationViewerProps {
  simulation?: any;
  onVehicleSelect?: (vehicleId: string) => void;
}

interface SimulationViewerRef {
  updateSensorData: (data: any) => void;
  updateVehicleState: (state: any) => void;
  resetView: () => void;
}

const SimulationViewer = forwardRef<SimulationViewerRef, SimulationViewerProps>(
  ({ simulation, onVehicleSelect }, ref) => {
    const containerRef = useRef<HTMLDivElement>(null);
    const sceneRef = useRef<THREE.Scene>();
    const rendererRef = useRef<THREE.WebGLRenderer>();
    const cameraRef = useRef<THREE.PerspectiveCamera>();
    const controlsRef = useRef<OrbitControls>();
    const vehiclesRef = useRef<Map<string, THREE.Object3D>>(new Map());
    const sensorsRef = useRef<Map<string, THREE.Object3D>>(new Map());
    const raycasterRef = useRef<THREE.Raycaster>();
    const mouseRef = useRef<THREE.Vector2>();
    const animationIdRef = useRef<number>();

    // 暴露给父组件的方法
    useImperativeHandle(ref, () => ({
      updateSensorData: (data: any) => {
        updateSensorVisualization(data);
      },
      updateVehicleState: (state: any) => {
        updateVehiclePosition(state);
      },
      resetView: () => {
        resetCamera();
      }
    }));

    // 初始化Three.js场景
    useEffect(() => {
      if (!containerRef.current) return;

      initializeScene();
      setupEventListeners();
      startRenderLoop();

      return () => {
        cleanup();
      };
    }, []);

    // 监听仿真状态变化
    useEffect(() => {
      if (simulation) {
        loadSimulationScene(simulation);
      }
    }, [simulation]);

    const initializeScene = () => {
      if (!containerRef.current) return;

      const container = containerRef.current;
      const width = container.clientWidth;
      const height = container.clientHeight;

      // 创建场景
      const scene = new THREE.Scene();
      scene.background = new THREE.Color(0x87CEEB); // 天空蓝
      sceneRef.current = scene;

      // 创建相机
      const camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);
      camera.position.set(0, 10, 20);
      cameraRef.current = camera;

      // 创建渲染器
      const renderer = new THREE.WebGLRenderer({ antialias: true });
      renderer.setSize(width, height);
      renderer.shadowMap.enabled = true;
      renderer.shadowMap.type = THREE.PCFSoftShadowMap;
      rendererRef.current = renderer;

      // 添加到DOM
      container.appendChild(renderer.domElement);

      // 创建控制器
      const controls = new OrbitControls(camera, renderer.domElement);
      controls.enableDamping = true;
      controls.dampingFactor = 0.05;
      controls.maxPolarAngle = Math.PI / 2;
      controlsRef.current = controls;

      // 创建射线投射器（用于鼠标交互）
      raycasterRef.current = new THREE.Raycaster();
      mouseRef.current = new THREE.Vector2();

      // 添加光照
      setupLighting(scene);

      // 添加地面
      createGround(scene);

      // 添加坐标轴辅助器
      const axesHelper = new THREE.AxesHelper(5);
      scene.add(axesHelper);
    };

    const setupLighting = (scene: THREE.Scene) => {
      // 环境光
      const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
      scene.add(ambientLight);

      // 方向光（太阳光）
      const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
      directionalLight.position.set(50, 50, 50);
      directionalLight.castShadow = true;
      directionalLight.shadow.mapSize.width = 2048;
      directionalLight.shadow.mapSize.height = 2048;
      directionalLight.shadow.camera.near = 0.5;
      directionalLight.shadow.camera.far = 500;
      directionalLight.shadow.camera.left = -50;
      directionalLight.shadow.camera.right = 50;
      directionalLight.shadow.camera.top = 50;
      directionalLight.shadow.camera.bottom = -50;
      scene.add(directionalLight);

      // 添加光照辅助器
      const lightHelper = new THREE.DirectionalLightHelper(directionalLight, 5);
      scene.add(lightHelper);
    };

    const createGround = (scene: THREE.Scene) => {
      // 创建地面
      const groundGeometry = new THREE.PlaneGeometry(200, 200);
      const groundMaterial = new THREE.MeshLambertMaterial({ 
        color: 0x555555,
        transparent: true,
        opacity: 0.8
      });
      const ground = new THREE.Mesh(groundGeometry, groundMaterial);
      ground.rotation.x = -Math.PI / 2;
      ground.receiveShadow = true;
      scene.add(ground);

      // 添加网格
      const gridHelper = new THREE.GridHelper(200, 50, 0x888888, 0x444444);
      scene.add(gridHelper);
    };

    const setupEventListeners = () => {
      if (!containerRef.current || !rendererRef.current) return;

      const container = containerRef.current;
      const renderer = rendererRef.current;

      // 窗口大小调整
      const handleResize = () => {
        if (!cameraRef.current || !rendererRef.current) return;

        const width = container.clientWidth;
        const height = container.clientHeight;

        cameraRef.current.aspect = width / height;
        cameraRef.current.updateProjectionMatrix();
        rendererRef.current.setSize(width, height);
      };

      // 鼠标点击事件
      const handleClick = (event: MouseEvent) => {
        if (!mouseRef.current || !raycasterRef.current || !cameraRef.current || !sceneRef.current) return;

        const rect = container.getBoundingClientRect();
        mouseRef.current.x = ((event.clientX - rect.left) / rect.width) * 2 - 1;
        mouseRef.current.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;

        raycasterRef.current.setFromCamera(mouseRef.current, cameraRef.current);
        const intersects = raycasterRef.current.intersectObjects(sceneRef.current.children, true);

        if (intersects.length > 0) {
          const object = intersects[0].object;
          // 查找车辆对象
          let vehicleId = findVehicleId(object);
          if (vehicleId && onVehicleSelect) {
            onVehicleSelect(vehicleId);
          }
        }
      };

      window.addEventListener('resize', handleResize);
      renderer.domElement.addEventListener('click', handleClick);

      return () => {
        window.removeEventListener('resize', handleResize);
        renderer.domElement.removeEventListener('click', handleClick);
      };
    };

    const findVehicleId = (object: THREE.Object3D): string | null => {
      let current = object;
      while (current) {
        if (current.userData.vehicleId) {
          return current.userData.vehicleId;
        }
        current = current.parent!;
      }
      return null;
    };

    const startRenderLoop = () => {
      const animate = () => {
        animationIdRef.current = requestAnimationFrame(animate);

        if (controlsRef.current) {
          controlsRef.current.update();
        }

        if (rendererRef.current && sceneRef.current && cameraRef.current) {
          rendererRef.current.render(sceneRef.current, cameraRef.current);
        }
      };

      animate();
    };

    const loadSimulationScene = async (simulation: any) => {
      if (!sceneRef.current) return;

      try {
        // 清除现有对象
        clearScene();

        // 加载地图
        if (simulation.mapName) {
          await loadMap(simulation.mapName);
        }

        // 加载车辆
        if (simulation.vehicles) {
          for (const vehicle of simulation.vehicles) {
            await loadVehicle(vehicle);
          }
        }

        // 加载传感器
        if (simulation.sensors) {
          for (const sensor of simulation.sensors) {
            loadSensor(sensor);
          }
        }

      } catch (error) {
        console.error('加载仿真场景失败:', error);
      }
    };

    const clearScene = () => {
      if (!sceneRef.current) return;

      // 清除车辆
      vehiclesRef.current.forEach((vehicle) => {
        sceneRef.current!.remove(vehicle);
      });
      vehiclesRef.current.clear();

      // 清除传感器
      sensorsRef.current.forEach((sensor) => {
        sceneRef.current!.remove(sensor);
      });
      sensorsRef.current.clear();
    };

    const loadMap = async (mapName: string) => {
      // 这里应该加载地图模型
      // 简化实现：创建一些道路标记
      createRoadMarkers();
    };

    const createRoadMarkers = () => {
      if (!sceneRef.current) return;

      // 创建简单的道路标记
      const roadGeometry = new THREE.BoxGeometry(100, 0.1, 4);
      const roadMaterial = new THREE.MeshLambertMaterial({ color: 0x333333 });
      
      for (let i = -2; i <= 2; i++) {
        const road = new THREE.Mesh(roadGeometry, roadMaterial);
        road.position.set(0, 0, i * 10);
        sceneRef.current.add(road);
      }
    };

    const loadVehicle = async (vehicleData: any) => {
      if (!sceneRef.current) return;

      try {
        // 简化实现：创建一个盒子代表车辆
        const vehicleGeometry = new THREE.BoxGeometry(4, 1.5, 2);
        const vehicleMaterial = new THREE.MeshLambertMaterial({ 
          color: vehicleData.color || 0x0066cc 
        });
        const vehicle = new THREE.Mesh(vehicleGeometry, vehicleMaterial);
        
        vehicle.position.set(
          vehicleData.position?.x || 0,
          vehicleData.position?.y || 0.75,
          vehicleData.position?.z || 0
        );
        
        vehicle.castShadow = true;
        vehicle.receiveShadow = true;
        vehicle.userData.vehicleId = vehicleData.id;
        
        sceneRef.current.add(vehicle);
        vehiclesRef.current.set(vehicleData.id, vehicle);

        // 添加车辆标签
        createVehicleLabel(vehicle, vehicleData.id);

      } catch (error) {
        console.error('加载车辆失败:', error);
      }
    };

    const createVehicleLabel = (vehicle: THREE.Object3D, vehicleId: string) => {
      // 创建文本标签（简化实现）
      const canvas = document.createElement('canvas');
      const context = canvas.getContext('2d')!;
      canvas.width = 256;
      canvas.height = 64;
      
      context.fillStyle = 'rgba(0, 0, 0, 0.8)';
      context.fillRect(0, 0, canvas.width, canvas.height);
      
      context.fillStyle = 'white';
      context.font = '20px Arial';
      context.textAlign = 'center';
      context.fillText(vehicleId, canvas.width / 2, canvas.height / 2 + 7);
      
      const texture = new THREE.CanvasTexture(canvas);
      const material = new THREE.SpriteMaterial({ map: texture });
      const sprite = new THREE.Sprite(material);
      sprite.position.set(0, 3, 0);
      sprite.scale.set(4, 1, 1);
      
      vehicle.add(sprite);
    };

    const loadSensor = (sensorData: any) => {
      if (!sceneRef.current) return;

      // 创建传感器可视化
      const sensorGeometry = new THREE.SphereGeometry(0.2);
      const sensorMaterial = new THREE.MeshLambertMaterial({ 
        color: getSensorColor(sensorData.type) 
      });
      const sensor = new THREE.Mesh(sensorGeometry, sensorMaterial);
      
      sensor.position.set(
        sensorData.position?.x || 0,
        sensorData.position?.y || 2,
        sensorData.position?.z || 0
      );
      
      sceneRef.current.add(sensor);
      sensorsRef.current.set(sensorData.id, sensor);
    };

    const getSensorColor = (sensorType: string): number => {
      switch (sensorType) {
        case 'camera': return 0xff0000;
        case 'lidar': return 0x00ff00;
        case 'radar': return 0x0000ff;
        case 'imu': return 0xffff00;
        case 'gps': return 0xff00ff;
        default: return 0x888888;
      }
    };

    const updateSensorVisualization = (data: any) => {
      // 更新传感器数据可视化
      if (data.type === 'lidar' && data.points) {
        updateLidarVisualization(data);
      }
    };

    const updateLidarVisualization = (lidarData: any) => {
      if (!sceneRef.current) return;

      // 移除旧的点云
      const oldPointCloud = sceneRef.current.getObjectByName('lidar_points');
      if (oldPointCloud) {
        sceneRef.current.remove(oldPointCloud);
      }

      // 创建新的点云
      const geometry = new THREE.BufferGeometry();
      const positions = new Float32Array(lidarData.points.length * 3);
      const colors = new Float32Array(lidarData.points.length * 3);

      for (let i = 0; i < lidarData.points.length; i++) {
        const point = lidarData.points[i];
        positions[i * 3] = point.x;
        positions[i * 3 + 1] = point.y;
        positions[i * 3 + 2] = point.z;

        // 根据强度设置颜色
        const intensity = point.intensity || 0.5;
        colors[i * 3] = intensity;
        colors[i * 3 + 1] = intensity;
        colors[i * 3 + 2] = intensity;
      }

      geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
      geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));

      const material = new THREE.PointsMaterial({ 
        size: 0.1, 
        vertexColors: true 
      });
      const pointCloud = new THREE.Points(geometry, material);
      pointCloud.name = 'lidar_points';

      sceneRef.current.add(pointCloud);
    };

    const updateVehiclePosition = (vehicleState: any) => {
      const vehicle = vehiclesRef.current.get(vehicleState.vehicleId);
      if (vehicle) {
        vehicle.position.set(
          vehicleState.position.x,
          vehicleState.position.y,
          vehicleState.position.z
        );
        
        vehicle.rotation.set(
          vehicleState.rotation.pitch,
          vehicleState.rotation.yaw,
          vehicleState.rotation.roll
        );
      }
    };

    const resetCamera = () => {
      if (cameraRef.current && controlsRef.current) {
        cameraRef.current.position.set(0, 10, 20);
        controlsRef.current.reset();
      }
    };

    const cleanup = () => {
      if (animationIdRef.current) {
        cancelAnimationFrame(animationIdRef.current);
      }

      if (rendererRef.current && containerRef.current) {
        containerRef.current.removeChild(rendererRef.current.domElement);
        rendererRef.current.dispose();
      }

      // 清理几何体和材质
      vehiclesRef.current.forEach((vehicle) => {
        vehicle.traverse((child) => {
          if (child instanceof THREE.Mesh) {
            child.geometry.dispose();
            if (Array.isArray(child.material)) {
              child.material.forEach(material => material.dispose());
            } else {
              child.material.dispose();
            }
          }
        });
      });

      sensorsRef.current.forEach((sensor) => {
        sensor.traverse((child) => {
          if (child instanceof THREE.Mesh) {
            child.geometry.dispose();
            if (Array.isArray(child.material)) {
              child.material.forEach(material => material.dispose());
            } else {
              child.material.dispose();
            }
          }
        });
      });
    };

    return (
      <Card 
        title="3D仿真视图" 
        style={{ height: '100%' }}
        bodyStyle={{ padding: 0, height: 'calc(100% - 57px)' }}
      >
        <div 
          ref={containerRef} 
          style={{ 
            width: '100%', 
            height: '100%',
            position: 'relative'
          }}
        >
          {!simulation && (
            <div style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              textAlign: 'center',
              zIndex: 1
            }}>
              <Alert
                message="未加载仿真"
                description="请先启动仿真或加载场景"
                type="info"
                showIcon
              />
            </div>
          )}
        </div>
      </Card>
    );
  }
);

SimulationViewer.displayName = 'SimulationViewer';

export default SimulationViewer;
