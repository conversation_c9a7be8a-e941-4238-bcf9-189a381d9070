// 自动驾驶开发加速系统 - 地图画布组件
import React, { useRef, useEffect, forwardRef, useImperativeHandle } from 'react';
import { Spin } from 'antd';
import mapboxgl from 'mapbox-gl';
import 'mapbox-gl/dist/mapbox-gl.css';

// 设置Mapbox访问令牌
mapboxgl.accessToken = process.env.REACT_APP_MAPBOX_TOKEN || 'your-mapbox-token';

interface MapCanvasProps {
  map?: any;
  layers: any[];
  features: any[];
  selectedFeatures: string[];
  selectedTool: string;
  center: number[];
  zoom: number;
  isCollaborationMode: boolean;
  collaborators: any[];
  onFeatureSelect: (featureIds: string[]) => void;
  onFeatureCreate: (featureData: any) => void;
  onFeatureUpdate: (featureId: string, updates: any) => void;
  onViewChange: (center: number[], zoom: number) => void;
  onCursorMove: (position: number[]) => void;
}

interface MapCanvasRef {
  zoomIn: () => void;
  zoomOut: () => void;
  setActiveTool: (tool: string) => void;
  updateCollaboratorCursor: (data: any) => void;
  fitBounds: (bounds: number[][]) => void;
}

const MapCanvas = forwardRef<MapCanvasRef, MapCanvasProps>(
  ({
    map,
    layers,
    features,
    selectedFeatures,
    selectedTool,
    center,
    zoom,
    isCollaborationMode,
    collaborators,
    onFeatureSelect,
    onFeatureCreate,
    onFeatureUpdate,
    onViewChange,
    onCursorMove
  }, ref) => {
    const mapContainerRef = useRef<HTMLDivElement>(null);
    const mapRef = useRef<mapboxgl.Map | null>(null);
    const drawRef = useRef<any>(null);
    const collaboratorCursorsRef = useRef<Map<string, mapboxgl.Marker>>(new Map());
    const isInitializedRef = useRef(false);

    // 暴露给父组件的方法
    useImperativeHandle(ref, () => ({
      zoomIn: () => {
        if (mapRef.current) {
          mapRef.current.zoomIn();
        }
      },
      zoomOut: () => {
        if (mapRef.current) {
          mapRef.current.zoomOut();
        }
      },
      setActiveTool: (tool: string) => {
        setActiveTool(tool);
      },
      updateCollaboratorCursor: (data: any) => {
        updateCollaboratorCursor(data);
      },
      fitBounds: (bounds: number[][]) => {
        if (mapRef.current) {
          mapRef.current.fitBounds(bounds as mapboxgl.LngLatBoundsLike);
        }
      }
    }));

    // 初始化地图
    useEffect(() => {
      if (!mapContainerRef.current || isInitializedRef.current) return;

      const mapInstance = new mapboxgl.Map({
        container: mapContainerRef.current,
        style: 'mapbox://styles/mapbox/streets-v11',
        center: center as [number, number],
        zoom: zoom,
        antialias: true
      });

      mapRef.current = mapInstance;
      isInitializedRef.current = true;

      // 添加导航控件
      mapInstance.addControl(new mapboxgl.NavigationControl(), 'top-right');

      // 添加比例尺
      mapInstance.addControl(new mapboxgl.ScaleControl(), 'bottom-left');

      // 地图加载完成后的初始化
      mapInstance.on('load', () => {
        initializeMapSources();
        initializeMapLayers();
        setupEventListeners();
      });

      // 视图变化事件
      mapInstance.on('moveend', () => {
        const newCenter = mapInstance.getCenter();
        const newZoom = mapInstance.getZoom();
        onViewChange([newCenter.lng, newCenter.lat], newZoom);
      });

      // 鼠标移动事件（用于协作光标）
      mapInstance.on('mousemove', (e) => {
        if (isCollaborationMode) {
          onCursorMove([e.lngLat.lng, e.lngLat.lat]);
        }
      });

      return () => {
        if (mapRef.current) {
          mapRef.current.remove();
          mapRef.current = null;
          isInitializedRef.current = false;
        }
      };
    }, []);

    // 初始化地图数据源
    const initializeMapSources = () => {
      if (!mapRef.current) return;

      const mapInstance = mapRef.current;

      // 添加要素数据源
      mapInstance.addSource('features', {
        type: 'geojson',
        data: {
          type: 'FeatureCollection',
          features: []
        }
      });

      // 添加选中要素数据源
      mapInstance.addSource('selected-features', {
        type: 'geojson',
        data: {
          type: 'FeatureCollection',
          features: []
        }
      });

      // 添加协作者光标数据源
      mapInstance.addSource('collaborator-cursors', {
        type: 'geojson',
        data: {
          type: 'FeatureCollection',
          features: []
        }
      });
    };

    // 初始化地图图层
    const initializeMapLayers = () => {
      if (!mapRef.current) return;

      const mapInstance = mapRef.current;

      // 道路图层
      mapInstance.addLayer({
        id: 'roads',
        type: 'line',
        source: 'features',
        filter: ['==', ['get', 'type'], 'road'],
        paint: {
          'line-color': '#666666',
          'line-width': 3
        }
      });

      // 车道图层
      mapInstance.addLayer({
        id: 'lanes',
        type: 'line',
        source: 'features',
        filter: ['==', ['get', 'type'], 'lane'],
        paint: {
          'line-color': '#888888',
          'line-width': 2,
          'line-dasharray': [2, 2]
        }
      });

      // 交叉口图层
      mapInstance.addLayer({
        id: 'junctions',
        type: 'fill',
        source: 'features',
        filter: ['==', ['get', 'type'], 'junction'],
        paint: {
          'fill-color': '#ffeb3b',
          'fill-opacity': 0.3
        }
      });

      // 交通信号灯图层
      mapInstance.addLayer({
        id: 'traffic-lights',
        type: 'circle',
        source: 'features',
        filter: ['==', ['get', 'type'], 'traffic_light'],
        paint: {
          'circle-color': '#f44336',
          'circle-radius': 6
        }
      });

      // 交通标志图层
      mapInstance.addLayer({
        id: 'traffic-signs',
        type: 'circle',
        source: 'features',
        filter: ['==', ['get', 'type'], 'traffic_sign'],
        paint: {
          'circle-color': '#2196f3',
          'circle-radius': 5
        }
      });

      // 选中要素高亮图层
      mapInstance.addLayer({
        id: 'selected-features-highlight',
        type: 'line',
        source: 'selected-features',
        paint: {
          'line-color': '#ff4081',
          'line-width': 4
        }
      });

      // 协作者光标图层
      mapInstance.addLayer({
        id: 'collaborator-cursors',
        type: 'circle',
        source: 'collaborator-cursors',
        paint: {
          'circle-color': ['get', 'color'],
          'circle-radius': 8,
          'circle-stroke-color': '#ffffff',
          'circle-stroke-width': 2
        }
      });
    };

    // 设置事件监听器
    const setupEventListeners = () => {
      if (!mapRef.current) return;

      const mapInstance = mapRef.current;

      // 要素点击事件
      const layerIds = ['roads', 'lanes', 'junctions', 'traffic-lights', 'traffic-signs'];
      
      layerIds.forEach(layerId => {
        mapInstance.on('click', layerId, (e) => {
          if (selectedTool === 'select') {
            const features = e.features || [];
            const featureIds = features.map(f => f.properties?.id).filter(Boolean);
            onFeatureSelect(featureIds);
          }
        });

        // 鼠标悬停效果
        mapInstance.on('mouseenter', layerId, () => {
          mapInstance.getCanvas().style.cursor = 'pointer';
        });

        mapInstance.on('mouseleave', layerId, () => {
          mapInstance.getCanvas().style.cursor = '';
        });
      });

      // 地图点击事件（用于创建要素）
      mapInstance.on('click', (e) => {
        if (selectedTool !== 'select' && selectedTool !== 'pan') {
          handleFeatureCreation(e);
        }
      });
    };

    // 处理要素创建
    const handleFeatureCreation = (e: mapboxgl.MapMouseEvent) => {
      const lngLat = e.lngLat;
      
      let featureData: any = {
        type: 'Feature',
        geometry: {
          type: 'Point',
          coordinates: [lngLat.lng, lngLat.lat]
        },
        properties: {
          id: `feature_${Date.now()}`,
          type: selectedTool,
          created_at: new Date().toISOString()
        }
      };

      // 根据工具类型调整几何类型
      switch (selectedTool) {
        case 'road':
        case 'lane':
          featureData.geometry.type = 'LineString';
          featureData.geometry.coordinates = [
            [lngLat.lng, lngLat.lat],
            [lngLat.lng + 0.001, lngLat.lat] // 临时终点
          ];
          break;
        case 'junction':
          featureData.geometry.type = 'Polygon';
          featureData.geometry.coordinates = [[
            [lngLat.lng - 0.0005, lngLat.lat - 0.0005],
            [lngLat.lng + 0.0005, lngLat.lat - 0.0005],
            [lngLat.lng + 0.0005, lngLat.lat + 0.0005],
            [lngLat.lng - 0.0005, lngLat.lat + 0.0005],
            [lngLat.lng - 0.0005, lngLat.lat - 0.0005]
          ]];
          break;
      }

      onFeatureCreate(featureData);
    };

    // 设置活动工具
    const setActiveTool = (tool: string) => {
      if (!mapRef.current) return;

      const mapInstance = mapRef.current;
      
      // 更新鼠标样式
      switch (tool) {
        case 'select':
          mapInstance.getCanvas().style.cursor = '';
          break;
        case 'pan':
          mapInstance.getCanvas().style.cursor = 'grab';
          break;
        default:
          mapInstance.getCanvas().style.cursor = 'crosshair';
          break;
      }

      // 启用/禁用地图交互
      if (tool === 'pan') {
        mapInstance.dragPan.enable();
        mapInstance.scrollZoom.enable();
      } else {
        mapInstance.dragPan.enable();
        mapInstance.scrollZoom.enable();
      }
    };

    // 更新协作者光标
    const updateCollaboratorCursor = (data: any) => {
      if (!mapRef.current || !isCollaborationMode) return;

      const { userId, position, username, color } = data;
      const cursors = collaboratorCursorsRef.current;

      // 移除旧光标
      if (cursors.has(userId)) {
        cursors.get(userId)?.remove();
      }

      // 创建新光标
      const cursorElement = document.createElement('div');
      cursorElement.style.width = '16px';
      cursorElement.style.height = '16px';
      cursorElement.style.borderRadius = '50%';
      cursorElement.style.backgroundColor = color || '#ff4081';
      cursorElement.style.border = '2px solid white';
      cursorElement.style.boxShadow = '0 2px 4px rgba(0,0,0,0.3)';
      cursorElement.title = username;

      const marker = new mapboxgl.Marker(cursorElement)
        .setLngLat(position)
        .addTo(mapRef.current);

      cursors.set(userId, marker);

      // 5秒后自动移除光标
      setTimeout(() => {
        if (cursors.has(userId)) {
          cursors.get(userId)?.remove();
          cursors.delete(userId);
        }
      }, 5000);
    };

    // 更新要素数据
    useEffect(() => {
      if (!mapRef.current) return;

      const mapInstance = mapRef.current;
      const source = mapInstance.getSource('features') as mapboxgl.GeoJSONSource;
      
      if (source) {
        const featureCollection = {
          type: 'FeatureCollection' as const,
          features: features.map(feature => ({
            ...feature,
            id: feature.properties?.id
          }))
        };
        
        source.setData(featureCollection);
      }
    }, [features]);

    // 更新选中要素
    useEffect(() => {
      if (!mapRef.current) return;

      const mapInstance = mapRef.current;
      const source = mapInstance.getSource('selected-features') as mapboxgl.GeoJSONSource;
      
      if (source) {
        const selectedFeatureData = features.filter(feature => 
          selectedFeatures.includes(feature.properties?.id)
        );
        
        const featureCollection = {
          type: 'FeatureCollection' as const,
          features: selectedFeatureData
        };
        
        source.setData(featureCollection);
      }
    }, [selectedFeatures, features]);

    // 更新图层可见性
    useEffect(() => {
      if (!mapRef.current) return;

      const mapInstance = mapRef.current;
      
      layers.forEach(layer => {
        const visibility = layer.visible ? 'visible' : 'none';
        
        // 根据图层类型设置对应的地图图层可见性
        switch (layer.type) {
          case 'road':
            mapInstance.setLayoutProperty('roads', 'visibility', visibility);
            break;
          case 'lane':
            mapInstance.setLayoutProperty('lanes', 'visibility', visibility);
            break;
          case 'junction':
            mapInstance.setLayoutProperty('junctions', 'visibility', visibility);
            break;
          case 'traffic_light':
            mapInstance.setLayoutProperty('traffic-lights', 'visibility', visibility);
            break;
          case 'traffic_sign':
            mapInstance.setLayoutProperty('traffic-signs', 'visibility', visibility);
            break;
        }
      });
    }, [layers]);

    // 更新地图中心和缩放
    useEffect(() => {
      if (!mapRef.current) return;

      const mapInstance = mapRef.current;
      const currentCenter = mapInstance.getCenter();
      const currentZoom = mapInstance.getZoom();

      // 只有当差异较大时才更新，避免无限循环
      const centerDiff = Math.abs(currentCenter.lng - center[0]) + Math.abs(currentCenter.lat - center[1]);
      const zoomDiff = Math.abs(currentZoom - zoom);

      if (centerDiff > 0.001 || zoomDiff > 0.1) {
        mapInstance.setCenter(center as [number, number]);
        mapInstance.setZoom(zoom);
      }
    }, [center, zoom]);

    return (
      <div style={{ position: 'relative', width: '100%', height: '100%' }}>
        <div
          ref={mapContainerRef}
          style={{ width: '100%', height: '100%' }}
        />
        
        {!mapRef.current && (
          <div style={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            zIndex: 1000
          }}>
            <Spin size="large" tip="加载地图中..." />
          </div>
        )}
      </div>
    );
  }
);

MapCanvas.displayName = 'MapCanvas';

export default MapCanvas;
