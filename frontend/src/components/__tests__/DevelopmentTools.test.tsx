// 自动驾驶开发加速系统 - 开发工具组件单元测试
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { configureStore } from '@reduxjs/toolkit';
import '@testing-library/jest-dom';

import DevelopmentTools from '@pages/development-tools/DevelopmentTools';
import { useDevelopmentTools } from '@hooks/useDevelopmentTools';
import { useWebSocket } from '@hooks/useWebSocket';

// Mock hooks
jest.mock('@hooks/useDevelopmentTools');
jest.mock('@hooks/useWebSocket');

const mockUseDevelopmentTools = useDevelopmentTools as jest.MockedFunction<typeof useDevelopmentTools>;
const mockUseWebSocket = useWebSocket as jest.MockedFunction<typeof useWebSocket>;

// Mock store
const mockStore = configureStore({
  reducer: {
    auth: (state = { user: null, token: null }) => state,
    ui: (state = { loading: false }) => state,
  },
});

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <Provider store={mockStore}>
    <BrowserRouter>
      {children}
    </BrowserRouter>
  </Provider>
);

// Mock data
const mockProjects = [
  {
    id: '1',
    name: '测试项目1',
    description: '这是一个测试项目',
    template: 'python-fastapi',
    status: 'active' as const,
    createdAt: '2024-01-15T10:00:00Z',
    lastBuild: '2024-01-15T11:00:00Z',
  },
  {
    id: '2',
    name: '测试项目2',
    description: '另一个测试项目',
    template: 'react-typescript',
    status: 'building' as const,
    createdAt: '2024-01-15T09:00:00Z',
    lastBuild: '2024-01-15T10:30:00Z',
    buildProgress: 65,
  },
];

const mockTemplates = [
  {
    id: 'python-fastapi',
    name: 'Python FastAPI',
    description: 'FastAPI后端服务模板',
    category: 'backend',
    language: 'python',
    framework: 'fastapi',
    version: '1.0.0',
    parameters: [
      {
        name: 'project_name',
        type: 'string' as const,
        description: '项目名称',
        required: true,
        defaultValue: 'my-api',
      },
      {
        name: 'enable_auth',
        type: 'boolean' as const,
        description: '启用身份认证',
        required: false,
        defaultValue: true,
      },
    ],
  },
];

describe('DevelopmentTools Component', () => {
  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Setup default mock implementations
    mockUseDevelopmentTools.mockReturnValue({
      projects: mockProjects,
      templates: mockTemplates,
      buildStatus: {},
      isLoading: false,
      createProject: jest.fn(),
      updateProject: jest.fn(),
      deleteProject: jest.fn(),
      buildProject: jest.fn(),
      stopBuild: jest.fn(),
      getProjectFiles: jest.fn(),
      getFileContent: jest.fn(),
      saveFileContent: jest.fn(),
      downloadProject: jest.fn(),
      getBuildLogs: jest.fn(),
      createTemplate: jest.fn(),
      updateTemplate: jest.fn(),
      deleteTemplate: jest.fn(),
      previewTemplate: jest.fn(),
      validateTemplate: jest.fn(),
      getProjectStats: jest.fn(),
      refreshProjects: jest.fn(),
      refreshTemplates: jest.fn(),
    });

    mockUseWebSocket.mockReturnValue({
      isConnected: true,
      lastMessage: null,
      sendMessage: jest.fn(),
      connectionStatus: 'Connected',
    });
  });

  describe('项目列表渲染', () => {
    it('应该正确渲染项目列表', () => {
      render(
        <TestWrapper>
          <DevelopmentTools />
        </TestWrapper>
      );

      // 检查项目名称是否显示
      expect(screen.getByText('测试项目1')).toBeInTheDocument();
      expect(screen.getByText('测试项目2')).toBeInTheDocument();
      
      // 检查项目描述是否显示
      expect(screen.getByText('这是一个测试项目')).toBeInTheDocument();
      expect(screen.getByText('另一个测试项目')).toBeInTheDocument();
    });

    it('应该显示正确的项目状态', () => {
      render(
        <TestWrapper>
          <DevelopmentTools />
        </TestWrapper>
      );

      // 检查状态标签
      expect(screen.getByText('运行中')).toBeInTheDocument();
      expect(screen.getByText('构建中')).toBeInTheDocument();
    });

    it('应该显示构建进度', () => {
      render(
        <TestWrapper>
          <DevelopmentTools />
        </TestWrapper>
      );

      // 检查构建进度条（通过role查找）
      const progressBars = screen.getAllByRole('progressbar');
      expect(progressBars.length).toBeGreaterThan(0);
    });
  });

  describe('项目操作', () => {
    it('应该能够启动项目构建', async () => {
      const mockBuildProject = jest.fn();
      mockUseDevelopmentTools.mockReturnValue({
        ...mockUseDevelopmentTools(),
        buildProject: mockBuildProject,
      });

      render(
        <TestWrapper>
          <DevelopmentTools />
        </TestWrapper>
      );

      // 点击构建按钮
      const buildButtons = screen.getAllByText('构建');
      fireEvent.click(buildButtons[0]);

      await waitFor(() => {
        expect(mockBuildProject).toHaveBeenCalledWith('1');
      });
    });

    it('应该能够停止项目构建', async () => {
      const mockStopBuild = jest.fn();
      mockUseDevelopmentTools.mockReturnValue({
        ...mockUseDevelopmentTools(),
        stopBuild: mockStopBuild,
      });

      render(
        <TestWrapper>
          <DevelopmentTools />
        </TestWrapper>
      );

      // 点击停止按钮
      const stopButton = screen.getByText('停止');
      fireEvent.click(stopButton);

      await waitFor(() => {
        expect(mockStopBuild).toHaveBeenCalledWith('2');
      });
    });

    it('应该能够下载项目', async () => {
      const mockDownloadProject = jest.fn();
      mockUseDevelopmentTools.mockReturnValue({
        ...mockUseDevelopmentTools(),
        downloadProject: mockDownloadProject,
      });

      render(
        <TestWrapper>
          <DevelopmentTools />
        </TestWrapper>
      );

      // 点击下载按钮
      const downloadButtons = screen.getAllByText('下载');
      fireEvent.click(downloadButtons[0]);

      await waitFor(() => {
        expect(mockDownloadProject).toHaveBeenCalledWith('1');
      });
    });
  });

  describe('模板管理', () => {
    it('应该正确渲染模板库', () => {
      render(
        <TestWrapper>
          <DevelopmentTools />
        </TestWrapper>
      );

      // 切换到模板库标签
      fireEvent.click(screen.getByText('模板库'));

      // 检查模板是否显示
      expect(screen.getByText('Python FastAPI')).toBeInTheDocument();
      expect(screen.getByText('FastAPI后端服务模板')).toBeInTheDocument();
    });

    it('应该能够使用模板创建项目', async () => {
      const mockCreateProject = jest.fn();
      mockUseDevelopmentTools.mockReturnValue({
        ...mockUseDevelopmentTools(),
        createProject: mockCreateProject,
      });

      render(
        <TestWrapper>
          <DevelopmentTools />
        </TestWrapper>
      );

      // 切换到模板库标签
      fireEvent.click(screen.getByText('模板库'));

      // 点击使用模板按钮
      const useButtons = screen.getAllByRole('button');
      const useButton = useButtons.find(button => 
        button.getAttribute('aria-label')?.includes('use')
      );
      
      if (useButton) {
        fireEvent.click(useButton);
      }

      // 应该打开创建项目模态框
      await waitFor(() => {
        expect(screen.getByText('创建项目')).toBeInTheDocument();
      });
    });
  });

  describe('代码编辑器', () => {
    it('应该在选择项目后显示代码编辑器', () => {
      render(
        <TestWrapper>
          <DevelopmentTools />
        </TestWrapper>
      );

      // 切换到代码编辑标签
      fireEvent.click(screen.getByText('代码编辑'));

      // 应该显示选择项目的提示
      expect(screen.getByText('请选择一个项目开始编辑')).toBeInTheDocument();
    });

    it('应该能够保存文件', async () => {
      const mockSaveFileContent = jest.fn();
      mockUseDevelopmentTools.mockReturnValue({
        ...mockUseDevelopmentTools(),
        saveFileContent: mockSaveFileContent,
      });

      render(
        <TestWrapper>
          <DevelopmentTools />
        </TestWrapper>
      );

      // 切换到代码编辑标签
      fireEvent.click(screen.getByText('代码编辑'));

      // 选择项目
      fireEvent.click(screen.getByText('测试项目1'));

      // 等待编辑器加载
      await waitFor(() => {
        const saveButton = screen.getByText('保存');
        expect(saveButton).toBeInTheDocument();
      });
    });
  });

  describe('WebSocket连接', () => {
    it('应该显示连接状态', () => {
      render(
        <TestWrapper>
          <DevelopmentTools />
        </TestWrapper>
      );

      // WebSocket连接状态应该在某处显示
      // 这里可能需要根据实际实现调整
    });

    it('应该处理构建进度更新', () => {
      const mockSendMessage = jest.fn();
      mockUseWebSocket.mockReturnValue({
        isConnected: true,
        lastMessage: {
          data: JSON.stringify({
            type: 'build_progress',
            projectId: '1',
            progress: 50
          })
        },
        sendMessage: mockSendMessage,
        connectionStatus: 'Connected',
      });

      render(
        <TestWrapper>
          <DevelopmentTools />
        </TestWrapper>
      );

      // 验证消息处理逻辑
      // 这里需要根据实际的消息处理实现来验证
    });
  });

  describe('错误处理', () => {
    it('应该处理加载错误', () => {
      mockUseDevelopmentTools.mockReturnValue({
        ...mockUseDevelopmentTools(),
        isLoading: false,
        projects: [],
        templates: [],
      });

      render(
        <TestWrapper>
          <DevelopmentTools />
        </TestWrapper>
      );

      // 应该显示空状态或错误信息
      expect(screen.getByText('项目列表')).toBeInTheDocument();
    });

    it('应该处理网络连接错误', () => {
      mockUseWebSocket.mockReturnValue({
        isConnected: false,
        lastMessage: null,
        sendMessage: jest.fn(),
        connectionStatus: 'Disconnected',
      });

      render(
        <TestWrapper>
          <DevelopmentTools />
        </TestWrapper>
      );

      // 应该显示连接错误状态
      // 这里需要根据实际的错误处理实现来验证
    });
  });

  describe('表单验证', () => {
    it('应该验证创建项目表单', async () => {
      render(
        <TestWrapper>
          <DevelopmentTools />
        </TestWrapper>
      );

      // 打开创建项目模态框
      fireEvent.click(screen.getByText('创建项目'));

      // 尝试提交空表单
      const createButton = screen.getByRole('button', { name: '创建项目' });
      fireEvent.click(createButton);

      // 应该显示验证错误
      await waitFor(() => {
        expect(screen.getByText('请输入项目名称')).toBeInTheDocument();
      });
    });

    it('应该验证模板参数', async () => {
      render(
        <TestWrapper>
          <DevelopmentTools />
        </TestWrapper>
      );

      // 打开创建项目模态框并选择模板
      fireEvent.click(screen.getByText('创建项目'));
      
      // 填写项目名称
      const nameInput = screen.getByPlaceholderText('输入项目名称');
      fireEvent.change(nameInput, { target: { value: '测试项目' } });

      // 选择模板
      const templateSelect = screen.getByPlaceholderText('选择项目模板');
      fireEvent.change(templateSelect, { target: { value: 'python-fastapi' } });

      // 验证模板参数是否显示
      await waitFor(() => {
        expect(screen.getByText('模板配置: Python FastAPI')).toBeInTheDocument();
      });
    });
  });

  describe('性能测试', () => {
    it('应该在合理时间内渲染大量项目', () => {
      const manyProjects = Array.from({ length: 100 }, (_, i) => ({
        id: `project-${i}`,
        name: `项目 ${i}`,
        description: `项目 ${i} 的描述`,
        template: 'python-fastapi',
        status: 'active' as const,
        createdAt: '2024-01-15T10:00:00Z',
        lastBuild: '2024-01-15T11:00:00Z',
      }));

      mockUseDevelopmentTools.mockReturnValue({
        ...mockUseDevelopmentTools(),
        projects: manyProjects,
      });

      const startTime = performance.now();
      
      render(
        <TestWrapper>
          <DevelopmentTools />
        </TestWrapper>
      );

      const endTime = performance.now();
      const renderTime = endTime - startTime;

      // 渲染时间应该少于1秒
      expect(renderTime).toBeLessThan(1000);
    });
  });
});
