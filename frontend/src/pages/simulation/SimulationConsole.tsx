// 自动驾驶开发加速系统 - 仿真控制台
import React, { useState, useEffect, useRef } from 'react';
import {
  Layout,
  Card,
  Button,
  Select,
  Slider,
  Switch,
  Progress,
  Statistic,
  Row,
  Col,
  Space,
  Tabs,
  Table,
  Tag,
  message,
  Modal,
  Form,
  Input,
  Upload,
  Divider
} from 'antd';
import {
  PlayCircleOutlined,
  PauseOutlined,
  StopOutlined,
  ReloadOutlined,
  SettingOutlined,
  MonitorOutlined,
  CameraOutlined,
  CarOutlined,
  EnvironmentOutlined,
  CloudOutlined,
  UploadOutlined,
  DownloadOutlined
} from '@ant-design/icons';

import { useSimulation } from '@hooks/useSimulation';
import { useWebSocket } from '@hooks/useWebSocket';
import SimulationViewer from '@components/simulation/SimulationViewer';
import SensorPanel from '@components/simulation/SensorPanel';
import VehicleControlPanel from '@components/simulation/VehicleControlPanel';
import ScenarioEditor from '@components/simulation/ScenarioEditor';
import DataRecordingPanel from '@components/simulation/DataRecordingPanel';

const { Content, Sider } = Layout;
const { TabPane } = Tabs;
const { Option } = Select;

interface SimulationConsoleProps {}

const SimulationConsole: React.FC<SimulationConsoleProps> = () => {
  // 状态管理
  const [activeTab, setActiveTab] = useState('control');
  const [isSimulationRunning, setIsSimulationRunning] = useState(false);
  const [simulationSpeed, setSimulationSpeed] = useState(1.0);
  const [selectedSimulator, setSelectedSimulator] = useState<string>('carla');
  const [selectedScenario, setSelectedScenario] = useState<string>('');
  const [showScenarioEditor, setShowScenarioEditor] = useState(false);
  const [showSettings, setShowSettings] = useState(false);

  // 自定义Hooks
  const {
    simulators,
    scenarios,
    currentSimulation,
    simulationStats,
    startSimulation,
    stopSimulation,
    pauseSimulation,
    resumeSimulation,
    resetSimulation,
    loadScenario,
    getSimulationStatus,
    isLoading
  } = useSimulation();

  const {
    isConnected,
    lastMessage,
    sendMessage
  } = useWebSocket('/api/v1/simulation/ws');

  // 引用
  const viewerRef = useRef<any>(null);

  // 效果钩子
  useEffect(() => {
    // 定期更新仿真状态
    const interval = setInterval(() => {
      if (isSimulationRunning) {
        getSimulationStatus();
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [isSimulationRunning, getSimulationStatus]);

  useEffect(() => {
    // 处理WebSocket消息
    if (lastMessage) {
      try {
        const data = JSON.parse(lastMessage.data);
        handleWebSocketMessage(data);
      } catch (error) {
        console.error('解析WebSocket消息失败:', error);
      }
    }
  }, [lastMessage]);

  // 事件处理函数
  const handleWebSocketMessage = (data: any) => {
    switch (data.type) {
      case 'simulation_status':
        setIsSimulationRunning(data.payload.status === 'running');
        break;
      case 'sensor_data':
        // 更新传感器数据显示
        if (viewerRef.current) {
          viewerRef.current.updateSensorData(data.payload);
        }
        break;
      case 'vehicle_state':
        // 更新车辆状态
        if (viewerRef.current) {
          viewerRef.current.updateVehicleState(data.payload);
        }
        break;
      case 'error':
        message.error(data.payload.message);
        break;
      default:
        console.log('未知消息类型:', data.type);
    }
  };

  const handleStartSimulation = async () => {
    if (!selectedScenario) {
      message.warning('请先选择场景');
      return;
    }

    try {
      await startSimulation({
        simulator: selectedSimulator,
        scenario: selectedScenario,
        speed: simulationSpeed
      });
      message.success('仿真启动成功');
    } catch (error: any) {
      message.error(`仿真启动失败: ${error.message}`);
    }
  };

  const handleStopSimulation = async () => {
    try {
      await stopSimulation();
      message.success('仿真停止成功');
    } catch (error: any) {
      message.error(`仿真停止失败: ${error.message}`);
    }
  };

  const handlePauseSimulation = async () => {
    try {
      if (isSimulationRunning) {
        await pauseSimulation();
        message.success('仿真已暂停');
      } else {
        await resumeSimulation();
        message.success('仿真已恢复');
      }
    } catch (error: any) {
      message.error(`操作失败: ${error.message}`);
    }
  };

  const handleResetSimulation = async () => {
    try {
      await resetSimulation();
      message.success('仿真已重置');
    } catch (error: any) {
      message.error(`重置失败: ${error.message}`);
    }
  };

  const handleLoadScenario = async (scenarioId: string) => {
    try {
      await loadScenario(scenarioId);
      setSelectedScenario(scenarioId);
      message.success('场景加载成功');
    } catch (error: any) {
      message.error(`场景加载失败: ${error.message}`);
    }
  };

  const handleSpeedChange = (value: number) => {
    setSimulationSpeed(value);
    if (isSimulationRunning) {
      sendMessage({
        type: 'set_simulation_speed',
        payload: { speed: value }
      });
    }
  };

  // 渲染控制面板
  const renderControlPanel = () => (
    <Card title="仿真控制" size="small">
      <Space direction="vertical" style={{ width: '100%' }}>
        {/* 仿真器选择 */}
        <div>
          <label>仿真器:</label>
          <Select
            value={selectedSimulator}
            onChange={setSelectedSimulator}
            style={{ width: '100%', marginTop: 4 }}
            disabled={isSimulationRunning}
          >
            {simulators.map(sim => (
              <Option key={sim.id} value={sim.id}>
                {sim.name} ({sim.status})
              </Option>
            ))}
          </Select>
        </div>

        {/* 场景选择 */}
        <div>
          <label>场景:</label>
          <Select
            value={selectedScenario}
            onChange={handleLoadScenario}
            style={{ width: '100%', marginTop: 4 }}
            disabled={isSimulationRunning}
            placeholder="选择场景"
          >
            {scenarios.map(scenario => (
              <Option key={scenario.id} value={scenario.id}>
                {scenario.name}
              </Option>
            ))}
          </Select>
        </div>

        {/* 仿真速度 */}
        <div>
          <label>仿真速度: {simulationSpeed}x</label>
          <Slider
            min={0.1}
            max={5.0}
            step={0.1}
            value={simulationSpeed}
            onChange={handleSpeedChange}
            style={{ marginTop: 4 }}
          />
        </div>

        <Divider />

        {/* 控制按钮 */}
        <Row gutter={8}>
          <Col span={12}>
            <Button
              type="primary"
              icon={<PlayCircleOutlined />}
              onClick={handleStartSimulation}
              disabled={isSimulationRunning || isLoading}
              block
            >
              启动
            </Button>
          </Col>
          <Col span={12}>
            <Button
              icon={isSimulationRunning ? <PauseOutlined /> : <PlayCircleOutlined />}
              onClick={handlePauseSimulation}
              disabled={!currentSimulation}
              block
            >
              {isSimulationRunning ? '暂停' : '恢复'}
            </Button>
          </Col>
        </Row>

        <Row gutter={8}>
          <Col span={12}>
            <Button
              icon={<StopOutlined />}
              onClick={handleStopSimulation}
              disabled={!currentSimulation}
              block
            >
              停止
            </Button>
          </Col>
          <Col span={12}>
            <Button
              icon={<ReloadOutlined />}
              onClick={handleResetSimulation}
              disabled={!currentSimulation}
              block
            >
              重置
            </Button>
          </Col>
        </Row>
      </Space>
    </Card>
  );

  // 渲染状态面板
  const renderStatusPanel = () => (
    <Card title="仿真状态" size="small">
      <Row gutter={[16, 16]}>
        <Col span={12}>
          <Statistic
            title="仿真时间"
            value={simulationStats?.simulationTime || 0}
            suffix="s"
            precision={2}
          />
        </Col>
        <Col span={12}>
          <Statistic
            title="帧数"
            value={simulationStats?.frameCount || 0}
          />
        </Col>
        <Col span={12}>
          <Statistic
            title="FPS"
            value={simulationStats?.fps || 0}
            precision={1}
          />
        </Col>
        <Col span={12}>
          <Statistic
            title="车辆数"
            value={simulationStats?.vehicleCount || 0}
          />
        </Col>
      </Row>

      <Divider />

      <div>
        <div style={{ marginBottom: 8 }}>
          <span>连接状态: </span>
          <Tag color={isConnected ? 'green' : 'red'}>
            {isConnected ? '已连接' : '未连接'}
          </Tag>
        </div>
        <div>
          <span>仿真状态: </span>
          <Tag color={isSimulationRunning ? 'blue' : 'default'}>
            {isSimulationRunning ? '运行中' : '已停止'}
          </Tag>
        </div>
      </div>
    </Card>
  );

  // 渲染工具栏
  const renderToolbar = () => (
    <div style={{ marginBottom: 16 }}>
      <Space>
        <Button
          icon={<SettingOutlined />}
          onClick={() => setShowSettings(true)}
        >
          设置
        </Button>
        <Button
          icon={<EnvironmentOutlined />}
          onClick={() => setShowScenarioEditor(true)}
        >
          场景编辑器
        </Button>
        <Button icon={<DownloadOutlined />}>
          导出数据
        </Button>
        <Button icon={<UploadOutlined />}>
          导入场景
        </Button>
      </Space>
    </div>
  );

  return (
    <Layout style={{ height: '100vh' }}>
      {/* 左侧控制面板 */}
      <Sider width={300} theme="light" style={{ padding: 16, overflow: 'auto' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          {renderControlPanel()}
          {renderStatusPanel()}
        </Space>
      </Sider>

      {/* 主内容区域 */}
      <Layout>
        <Content style={{ padding: 16 }}>
          {renderToolbar()}

          <Tabs activeKey={activeTab} onChange={setActiveTab}>
            <TabPane
              tab={
                <span>
                  <MonitorOutlined />
                  3D视图
                </span>
              }
              key="viewer"
            >
              <SimulationViewer
                ref={viewerRef}
                simulation={currentSimulation}
                onVehicleSelect={(vehicleId) => console.log('选中车辆:', vehicleId)}
              />
            </TabPane>

            <TabPane
              tab={
                <span>
                  <CameraOutlined />
                  传感器
                </span>
              }
              key="sensors"
            >
              <SensorPanel
                simulation={currentSimulation}
                onSensorToggle={(sensorId, enabled) => {
                  sendMessage({
                    type: 'toggle_sensor',
                    payload: { sensorId, enabled }
                  });
                }}
              />
            </TabPane>

            <TabPane
              tab={
                <span>
                  <CarOutlined />
                  车辆控制
                </span>
              }
              key="vehicle"
            >
              <VehicleControlPanel
                simulation={currentSimulation}
                onControlChange={(vehicleId, control) => {
                  sendMessage({
                    type: 'vehicle_control',
                    payload: { vehicleId, control }
                  });
                }}
              />
            </TabPane>

            <TabPane
              tab={
                <span>
                  <CloudOutlined />
                  数据记录
                </span>
              }
              key="recording"
            >
              <DataRecordingPanel
                simulation={currentSimulation}
                onStartRecording={(config) => {
                  sendMessage({
                    type: 'start_recording',
                    payload: config
                  });
                }}
                onStopRecording={() => {
                  sendMessage({
                    type: 'stop_recording',
                    payload: {}
                  });
                }}
              />
            </TabPane>
          </Tabs>
        </Content>
      </Layout>

      {/* 场景编辑器模态框 */}
      <Modal
        title="场景编辑器"
        open={showScenarioEditor}
        onCancel={() => setShowScenarioEditor(false)}
        width={1200}
        footer={null}
      >
        <ScenarioEditor
          onSave={(scenario) => {
            console.log('保存场景:', scenario);
            setShowScenarioEditor(false);
          }}
          onCancel={() => setShowScenarioEditor(false)}
        />
      </Modal>

      {/* 设置模态框 */}
      <Modal
        title="仿真设置"
        open={showSettings}
        onCancel={() => setShowSettings(false)}
        onOk={() => setShowSettings(false)}
      >
        <Form layout="vertical">
          <Form.Item label="默认仿真器">
            <Select defaultValue="carla">
              <Option value="carla">CARLA</Option>
              <Option value="airsim">AirSim</Option>
            </Select>
          </Form.Item>
          <Form.Item label="自动保存间隔(分钟)">
            <Slider min={1} max={60} defaultValue={5} />
          </Form.Item>
          <Form.Item label="启用实时数据流">
            <Switch defaultChecked />
          </Form.Item>
        </Form>
      </Modal>
    </Layout>
  );
};

export default SimulationConsole;
