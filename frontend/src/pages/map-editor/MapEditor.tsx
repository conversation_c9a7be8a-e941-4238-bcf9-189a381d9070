// 自动驾驶开发加速系统 - 地图编辑器
import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Layout,
  Card,
  Button,
  Select,
  Slider,
  Switch,
  Progress,
  Statistic,
  Row,
  Col,
  Space,
  Tabs,
  Table,
  Tag,
  message,
  Modal,
  Form,
  Input,
  Upload,
  Divider,
  Tooltip,
  Badge,
  Avatar,
  List,
  Popover,
  Dropdown,
  Menu
} from 'antd';
import {
  SaveOutlined,
  UndoOutlined,
  RedoOutlined,
  ZoomInOutlined,
  ZoomOutOutlined,
  FullscreenOutlined,
  LayersOutlined,
  EditOutlined,
  DeleteOutlined,
  CopyOutlined,
  ShareAltOutlined,
  SettingOutlined,
  UserOutlined,
  TeamOutlined,
  HistoryOutlined,
  DownloadOutlined,
  UploadOutlined,
  EyeOutlined,
  EyeInvisibleOutlined
} from '@ant-design/icons';

import { useMapEditor } from '@hooks/useMapEditor';
import { useWebSocket } from '@hooks/useWebSocket';
import MapCanvas from '@components/map-editor/MapCanvas';
import LayerPanel from '@components/map-editor/LayerPanel';
import PropertyPanel from '@components/map-editor/PropertyPanel';
import ToolboxPanel from '@components/map-editor/ToolboxPanel';
import CollaborationPanel from '@components/map-editor/CollaborationPanel';
import HistoryPanel from '@components/map-editor/HistoryPanel';

const { Content, Sider } = Layout;
const { TabPane } = Tabs;
const { Option } = Select;

interface MapEditorProps {
  mapId?: string;
}

const MapEditor: React.FC<MapEditorProps> = ({ mapId }) => {
  // 状态管理
  const [activeTab, setActiveTab] = useState('layers');
  const [selectedTool, setSelectedTool] = useState('select');
  const [selectedFeatures, setSelectedFeatures] = useState<string[]>([]);
  const [isCollaborationMode, setIsCollaborationMode] = useState(false);
  const [showPropertyPanel, setShowPropertyPanel] = useState(true);
  const [showLayerPanel, setShowLayerPanel] = useState(true);
  const [showToolbox, setShowToolbox] = useState(true);
  const [zoomLevel, setZoomLevel] = useState(10);
  const [mapCenter, setMapCenter] = useState([116.4074, 39.9042]); // 北京坐标
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [showShareDialog, setShowShareDialog] = useState(false);

  // 自定义Hooks
  const {
    currentMap,
    layers,
    features,
    history,
    collaborators,
    isLoading,
    isSaving,
    hasUnsavedChanges,
    loadMap,
    saveMap,
    createLayer,
    updateLayer,
    deleteLayer,
    createFeature,
    updateFeature,
    deleteFeature,
    undo,
    redo,
    canUndo,
    canRedo,
    exportMap,
    importMap
  } = useMapEditor(mapId);

  const {
    isConnected,
    lastMessage,
    sendMessage,
    connectionStatus
  } = useWebSocket(`/api/v1/map-editor/ws?mapId=${mapId}`);

  // 引用
  const mapCanvasRef = useRef<any>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 效果钩子
  useEffect(() => {
    if (mapId) {
      loadMap(mapId);
    }
  }, [mapId, loadMap]);

  useEffect(() => {
    // 处理WebSocket消息
    if (lastMessage) {
      try {
        const data = JSON.parse(lastMessage.data);
        handleWebSocketMessage(data);
      } catch (error) {
        console.error('解析WebSocket消息失败:', error);
      }
    }
  }, [lastMessage]);

  useEffect(() => {
    // 键盘快捷键
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case 's':
            event.preventDefault();
            handleSave();
            break;
          case 'z':
            event.preventDefault();
            if (event.shiftKey) {
              handleRedo();
            } else {
              handleUndo();
            }
            break;
          case 'c':
            event.preventDefault();
            handleCopy();
            break;
          case 'v':
            event.preventDefault();
            handlePaste();
            break;
          case 'Delete':
          case 'Backspace':
            event.preventDefault();
            handleDelete();
            break;
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  // 事件处理函数
  const handleWebSocketMessage = (data: any) => {
    switch (data.type) {
      case 'feature_updated':
        // 处理要素更新
        console.log('要素更新:', data.payload);
        break;
      case 'collaborator_joined':
        message.info(`${data.payload.username} 加入了协作`);
        break;
      case 'collaborator_left':
        message.info(`${data.payload.username} 离开了协作`);
        break;
      case 'cursor_update':
        // 更新协作者光标位置
        if (mapCanvasRef.current) {
          mapCanvasRef.current.updateCollaboratorCursor(data.payload);
        }
        break;
      case 'conflict_detected':
        message.warning('检测到编辑冲突，请检查冲突面板');
        break;
      default:
        console.log('未知消息类型:', data.type);
    }
  };

  const handleSave = useCallback(async () => {
    try {
      await saveMap();
      message.success('地图保存成功');
    } catch (error: any) {
      message.error(`保存失败: ${error.message}`);
    }
  }, [saveMap]);

  const handleUndo = useCallback(() => {
    if (canUndo) {
      undo();
    }
  }, [canUndo, undo]);

  const handleRedo = useCallback(() => {
    if (canRedo) {
      redo();
    }
  }, [canRedo, redo]);

  const handleCopy = useCallback(() => {
    if (selectedFeatures.length > 0) {
      // 复制选中的要素
      console.log('复制要素:', selectedFeatures);
      message.success(`已复制 ${selectedFeatures.length} 个要素`);
    }
  }, [selectedFeatures]);

  const handlePaste = useCallback(() => {
    // 粘贴要素
    console.log('粘贴要素');
  }, []);

  const handleDelete = useCallback(() => {
    if (selectedFeatures.length > 0) {
      Modal.confirm({
        title: '确认删除',
        content: `确定要删除选中的 ${selectedFeatures.length} 个要素吗？`,
        onOk: async () => {
          try {
            for (const featureId of selectedFeatures) {
              await deleteFeature(featureId);
            }
            setSelectedFeatures([]);
            message.success('删除成功');
          } catch (error: any) {
            message.error(`删除失败: ${error.message}`);
          }
        }
      });
    }
  }, [selectedFeatures, deleteFeature]);

  const handleToolChange = (tool: string) => {
    setSelectedTool(tool);
    if (mapCanvasRef.current) {
      mapCanvasRef.current.setActiveTool(tool);
    }
  };

  const handleFeatureSelect = (featureIds: string[]) => {
    setSelectedFeatures(featureIds);
  };

  const handleFeatureCreate = async (featureData: any) => {
    try {
      await createFeature(featureData);
      message.success('要素创建成功');
    } catch (error: any) {
      message.error(`创建失败: ${error.message}`);
    }
  };

  const handleFeatureUpdate = async (featureId: string, updates: any) => {
    try {
      await updateFeature(featureId, updates);
      
      // 发送协作更新
      if (isCollaborationMode) {
        sendMessage({
          type: 'feature_update',
          payload: {
            featureId,
            updates,
            timestamp: Date.now()
          }
        });
      }
    } catch (error: any) {
      message.error(`更新失败: ${error.message}`);
    }
  };

  const handleMapViewChange = (center: number[], zoom: number) => {
    setMapCenter(center);
    setZoomLevel(zoom);
  };

  const handleExport = async () => {
    try {
      const data = await exportMap('geojson');
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${currentMap?.name || 'map'}.geojson`;
      a.click();
      URL.revokeObjectURL(url);
      message.success('地图导出成功');
    } catch (error: any) {
      message.error(`导出失败: ${error.message}`);
    }
  };

  const handleImport = (file: File) => {
    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        const data = JSON.parse(e.target?.result as string);
        await importMap(data);
        message.success('地图导入成功');
      } catch (error: any) {
        message.error(`导入失败: ${error.message}`);
      }
    };
    reader.readAsText(file);
  };

  const handleCollaborationToggle = (enabled: boolean) => {
    setIsCollaborationMode(enabled);
    if (enabled) {
      // 加入协作会话
      sendMessage({
        type: 'join_collaboration',
        payload: {
          mapId,
          userId: 'current-user-id', // 应该从认证状态获取
          username: 'Current User'
        }
      });
    } else {
      // 离开协作会话
      sendMessage({
        type: 'leave_collaboration',
        payload: {
          mapId,
          userId: 'current-user-id'
        }
      });
    }
  };

  // 渲染工具栏
  const renderToolbar = () => (
    <div style={{ 
      padding: '8px 16px', 
      borderBottom: '1px solid #f0f0f0',
      background: '#fff',
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center'
    }}>
      <Space>
        <Button
          icon={<SaveOutlined />}
          type="primary"
          onClick={handleSave}
          loading={isSaving}
          disabled={!hasUnsavedChanges}
        >
          保存
        </Button>
        
        <Divider type="vertical" />
        
        <Button
          icon={<UndoOutlined />}
          onClick={handleUndo}
          disabled={!canUndo}
          title="撤销 (Ctrl+Z)"
        />
        <Button
          icon={<RedoOutlined />}
          onClick={handleRedo}
          disabled={!canRedo}
          title="重做 (Ctrl+Shift+Z)"
        />
        
        <Divider type="vertical" />
        
        <Button
          icon={<CopyOutlined />}
          onClick={handleCopy}
          disabled={selectedFeatures.length === 0}
          title="复制 (Ctrl+C)"
        />
        <Button
          icon={<DeleteOutlined />}
          onClick={handleDelete}
          disabled={selectedFeatures.length === 0}
          title="删除 (Delete)"
        />
        
        <Divider type="vertical" />
        
        <Button
          icon={<ZoomInOutlined />}
          onClick={() => mapCanvasRef.current?.zoomIn()}
        />
        <Button
          icon={<ZoomOutOutlined />}
          onClick={() => mapCanvasRef.current?.zoomOut()}
        />
        <Button
          icon={<FullscreenOutlined />}
          onClick={() => setIsFullscreen(!isFullscreen)}
        />
      </Space>

      <Space>
        <Tooltip title="协作模式">
          <Switch
            checked={isCollaborationMode}
            onChange={handleCollaborationToggle}
            checkedChildren={<TeamOutlined />}
            unCheckedChildren={<UserOutlined />}
          />
        </Tooltip>
        
        {isCollaborationMode && (
          <Badge count={collaborators.length} showZero>
            <Avatar.Group maxCount={3}>
              {collaborators.map(collaborator => (
                <Tooltip key={collaborator.id} title={collaborator.username}>
                  <Avatar style={{ backgroundColor: collaborator.color }}>
                    {collaborator.username.charAt(0).toUpperCase()}
                  </Avatar>
                </Tooltip>
              ))}
            </Avatar.Group>
          </Badge>
        )}
        
        <Tooltip title={`连接状态: ${isConnected ? '已连接' : '未连接'}`}>
          <Badge status={isConnected ? 'success' : 'error'} />
        </Tooltip>
        
        <Dropdown
          overlay={
            <Menu>
              <Menu.Item key="export" icon={<DownloadOutlined />} onClick={handleExport}>
                导出地图
              </Menu.Item>
              <Menu.Item key="import" icon={<UploadOutlined />}>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".json,.geojson"
                  style={{ display: 'none' }}
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) {
                      handleImport(file);
                    }
                  }}
                />
                <span onClick={() => fileInputRef.current?.click()}>
                  导入地图
                </span>
              </Menu.Item>
              <Menu.Divider />
              <Menu.Item key="share" icon={<ShareAltOutlined />} onClick={() => setShowShareDialog(true)}>
                分享地图
              </Menu.Item>
              <Menu.Item key="settings" icon={<SettingOutlined />}>
                设置
              </Menu.Item>
            </Menu>
          }
        >
          <Button icon={<SettingOutlined />} />
        </Dropdown>
      </Space>
    </div>
  );

  // 渲染状态栏
  const renderStatusBar = () => (
    <div style={{
      padding: '4px 16px',
      borderTop: '1px solid #f0f0f0',
      background: '#fafafa',
      display: 'flex',
      justifyContent: 'space-between',
      alignItems: 'center',
      fontSize: '12px',
      color: '#666'
    }}>
      <Space>
        <span>坐标: {mapCenter[0].toFixed(6)}, {mapCenter[1].toFixed(6)}</span>
        <span>缩放: {zoomLevel}</span>
        <span>选中: {selectedFeatures.length} 个要素</span>
      </Space>
      
      <Space>
        {hasUnsavedChanges && <span style={{ color: '#ff4d4f' }}>● 未保存</span>}
        <span>图层: {layers.length}</span>
        <span>要素: {features.length}</span>
      </Space>
    </div>
  );

  return (
    <Layout style={{ height: '100vh' }}>
      {/* 工具栏 */}
      {renderToolbar()}
      
      <Layout style={{ flex: 1 }}>
        {/* 左侧面板 */}
        {(showLayerPanel || showToolbox) && (
          <Sider width={300} theme="light" style={{ borderRight: '1px solid #f0f0f0' }}>
            <Tabs activeKey={activeTab} onChange={setActiveTab} size="small">
              {showLayerPanel && (
                <TabPane
                  tab={
                    <span>
                      <LayersOutlined />
                      图层
                    </span>
                  }
                  key="layers"
                >
                  <LayerPanel
                    layers={layers}
                    onLayerCreate={createLayer}
                    onLayerUpdate={updateLayer}
                    onLayerDelete={deleteLayer}
                    onLayerVisibilityChange={(layerId, visible) => {
                      updateLayer(layerId, { visible });
                    }}
                  />
                </TabPane>
              )}
              
              {showToolbox && (
                <TabPane
                  tab={
                    <span>
                      <EditOutlined />
                      工具
                    </span>
                  }
                  key="tools"
                >
                  <ToolboxPanel
                    selectedTool={selectedTool}
                    onToolChange={handleToolChange}
                  />
                </TabPane>
              )}
              
              {isCollaborationMode && (
                <TabPane
                  tab={
                    <span>
                      <TeamOutlined />
                      协作
                    </span>
                  }
                  key="collaboration"
                >
                  <CollaborationPanel
                    collaborators={collaborators}
                    isConnected={isConnected}
                  />
                </TabPane>
              )}
              
              <TabPane
                tab={
                  <span>
                    <HistoryOutlined />
                    历史
                  </span>
                }
                key="history"
              >
                <HistoryPanel
                  history={history}
                  onHistorySelect={(historyId) => {
                    console.log('选择历史记录:', historyId);
                  }}
                />
              </TabPane>
            </Tabs>
          </Sider>
        )}

        {/* 主编辑区域 */}
        <Content style={{ position: 'relative' }}>
          <MapCanvas
            ref={mapCanvasRef}
            map={currentMap}
            layers={layers}
            features={features}
            selectedFeatures={selectedFeatures}
            selectedTool={selectedTool}
            center={mapCenter}
            zoom={zoomLevel}
            isCollaborationMode={isCollaborationMode}
            collaborators={collaborators}
            onFeatureSelect={handleFeatureSelect}
            onFeatureCreate={handleFeatureCreate}
            onFeatureUpdate={handleFeatureUpdate}
            onViewChange={handleMapViewChange}
            onCursorMove={(position) => {
              if (isCollaborationMode) {
                sendMessage({
                  type: 'cursor_update',
                  payload: {
                    position,
                    userId: 'current-user-id'
                  }
                });
              }
            }}
          />
        </Content>

        {/* 右侧属性面板 */}
        {showPropertyPanel && selectedFeatures.length > 0 && (
          <Sider width={300} theme="light" style={{ borderLeft: '1px solid #f0f0f0' }}>
            <PropertyPanel
              selectedFeatures={selectedFeatures}
              features={features}
              onFeatureUpdate={handleFeatureUpdate}
            />
          </Sider>
        )}
      </Layout>

      {/* 状态栏 */}
      {renderStatusBar()}

      {/* 分享对话框 */}
      <Modal
        title="分享地图"
        open={showShareDialog}
        onCancel={() => setShowShareDialog(false)}
        footer={null}
      >
        <div>
          <p>分享链接:</p>
          <Input.Group compact>
            <Input
              style={{ width: 'calc(100% - 80px)' }}
              value={`${window.location.origin}/map-editor/${mapId}`}
              readOnly
            />
            <Button
              onClick={() => {
                navigator.clipboard.writeText(`${window.location.origin}/map-editor/${mapId}`);
                message.success('链接已复制到剪贴板');
              }}
            >
              复制
            </Button>
          </Input.Group>
        </div>
      </Modal>
    </Layout>
  );
};

export default MapEditor;
