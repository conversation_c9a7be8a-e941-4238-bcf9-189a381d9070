// 自动驾驶开发加速系统 - 开发工具界面
import React, { useState, useEffect } from 'react';
import {
  Layout,
  Card,
  Button,
  Table,
  Modal,
  Form,
  Input,
  Select,
  Upload,
  Progress,
  Tag,
  Space,
  Tabs,
  Tree,
  message,
  Spin,
  Row,
  Col,
  Statistic,
  Timeline,
  Descriptions,
  Divider,
  Alert
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  DownloadOutlined,
  UploadOutlined,
  PlayCircleOutlined,
  StopOutlined,
  ReloadOutlined,
  FolderOutlined,
  FileOutlined,
  CodeOutlined,
  BugOutlined,
  RocketOutlined,
  SettingOutlined,
  EyeOutlined,
  CopyOutlined
} from '@ant-design/icons';
import MonacoEditor from '@monaco-editor/react';

import { useDevelopmentTools } from '@hooks/useDevelopmentTools';
import { useWebSocket } from '@hooks/useWebSocket';

const { Content, Sider } = Layout;
const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;
const { DirectoryTree } = Tree;

interface Project {
  id: string;
  name: string;
  description: string;
  template: string;
  status: 'active' | 'building' | 'error' | 'stopped';
  createdAt: string;
  lastBuild: string;
  buildProgress?: number;
}

interface Template {
  id: string;
  name: string;
  description: string;
  category: string;
  language: string;
  framework: string;
  version: string;
  parameters: TemplateParameter[];
}

interface TemplateParameter {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'select';
  description: string;
  required: boolean;
  defaultValue?: any;
  options?: string[];
}

const DevelopmentTools: React.FC = () => {
  // 状态管理
  const [activeTab, setActiveTab] = useState('projects');
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showTemplateModal, setShowTemplateModal] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<Template | null>(null);
  const [editorContent, setEditorContent] = useState('');
  const [selectedFile, setSelectedFile] = useState<string>('');
  const [buildLogs, setBuildLogs] = useState<string[]>([]);

  // 自定义Hooks
  const {
    projects,
    templates,
    buildStatus,
    isLoading,
    createProject,
    updateProject,
    deleteProject,
    buildProject,
    stopBuild,
    getProjectFiles,
    getFileContent,
    saveFileContent,
    downloadProject
  } = useDevelopmentTools();

  const {
    isConnected,
    lastMessage,
    sendMessage
  } = useWebSocket('/api/v1/development-tools/ws');

  // 效果钩子
  useEffect(() => {
    if (lastMessage) {
      try {
        const data = JSON.parse(lastMessage.data);
        handleWebSocketMessage(data);
      } catch (error) {
        console.error('解析WebSocket消息失败:', error);
      }
    }
  }, [lastMessage]);

  // WebSocket消息处理
  const handleWebSocketMessage = (data: any) => {
    switch (data.type) {
      case 'build_progress':
        // 更新构建进度
        if (selectedProject && data.projectId === selectedProject.id) {
          setSelectedProject(prev => prev ? {
            ...prev,
            buildProgress: data.progress,
            status: data.progress === 100 ? 'active' : 'building'
          } : null);
        }
        break;
      case 'build_log':
        // 添加构建日志
        setBuildLogs(prev => [...prev, data.message]);
        break;
      case 'build_complete':
        message.success('项目构建完成');
        break;
      case 'build_error':
        message.error(`构建失败: ${data.error}`);
        break;
      default:
        console.log('未知消息类型:', data.type);
    }
  };

  // 事件处理函数
  const handleCreateProject = async (values: any) => {
    try {
      const project = await createProject({
        name: values.name,
        description: values.description,
        templateId: selectedTemplate?.id || '',
        parameters: values.parameters || {}
      });
      
      setShowCreateModal(false);
      setSelectedTemplate(null);
      message.success('项目创建成功');
    } catch (error: any) {
      message.error(`项目创建失败: ${error.message}`);
    }
  };

  const handleBuildProject = async (project: Project) => {
    try {
      setBuildLogs([]);
      await buildProject(project.id);
      
      // 发送WebSocket消息开始监听构建状态
      sendMessage({
        type: 'subscribe_build',
        projectId: project.id
      });
      
      message.info('开始构建项目');
    } catch (error: any) {
      message.error(`构建失败: ${error.message}`);
    }
  };

  const handleStopBuild = async (project: Project) => {
    try {
      await stopBuild(project.id);
      message.info('构建已停止');
    } catch (error: any) {
      message.error(`停止构建失败: ${error.message}`);
    }
  };

  const handleFileSelect = async (selectedKeys: React.Key[], info: any) => {
    if (selectedKeys.length > 0 && info.node.isLeaf) {
      const filePath = selectedKeys[0] as string;
      setSelectedFile(filePath);
      
      try {
        const content = await getFileContent(selectedProject?.id || '', filePath);
        setEditorContent(content);
      } catch (error: any) {
        message.error(`读取文件失败: ${error.message}`);
      }
    }
  };

  const handleSaveFile = async () => {
    if (!selectedProject || !selectedFile) return;
    
    try {
      await saveFileContent(selectedProject.id, selectedFile, editorContent);
      message.success('文件保存成功');
    } catch (error: any) {
      message.error(`文件保存失败: ${error.message}`);
    }
  };

  // 渲染项目列表
  const renderProjectList = () => {
    const columns = [
      {
        title: '项目名称',
        dataIndex: 'name',
        key: 'name',
        render: (text: string, record: Project) => (
          <Space>
            <FolderOutlined />
            <a onClick={() => setSelectedProject(record)}>{text}</a>
          </Space>
        ),
      },
      {
        title: '描述',
        dataIndex: 'description',
        key: 'description',
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        render: (status: string, record: Project) => {
          const statusConfig = {
            active: { color: 'green', text: '运行中' },
            building: { color: 'blue', text: '构建中' },
            error: { color: 'red', text: '错误' },
            stopped: { color: 'default', text: '已停止' },
          };
          
          return (
            <Space direction="vertical" size="small">
              <Tag color={statusConfig[status as keyof typeof statusConfig].color}>
                {statusConfig[status as keyof typeof statusConfig].text}
              </Tag>
              {status === 'building' && record.buildProgress && (
                <Progress percent={record.buildProgress} size="small" />
              )}
            </Space>
          );
        },
      },
      {
        title: '最后构建',
        dataIndex: 'lastBuild',
        key: 'lastBuild',
      },
      {
        title: '操作',
        key: 'actions',
        render: (_, record: Project) => (
          <Space>
            <Button
              type="primary"
              icon={<PlayCircleOutlined />}
              size="small"
              onClick={() => handleBuildProject(record)}
              disabled={record.status === 'building'}
            >
              构建
            </Button>
            {record.status === 'building' && (
              <Button
                icon={<StopOutlined />}
                size="small"
                onClick={() => handleStopBuild(record)}
              >
                停止
              </Button>
            )}
            <Button
              icon={<DownloadOutlined />}
              size="small"
              onClick={() => downloadProject(record.id)}
            >
              下载
            </Button>
            <Button
              icon={<EditOutlined />}
              size="small"
              onClick={() => setSelectedProject(record)}
            >
              编辑
            </Button>
            <Button
              icon={<DeleteOutlined />}
              size="small"
              danger
              onClick={() => {
                Modal.confirm({
                  title: '确认删除',
                  content: `确定要删除项目 "${record.name}" 吗？`,
                  onOk: () => deleteProject(record.id),
                });
              }}
            >
              删除
            </Button>
          </Space>
        ),
      },
    ];

    return (
      <Card
        title="项目列表"
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setShowCreateModal(true)}
          >
            创建项目
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={projects}
          rowKey="id"
          loading={isLoading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
          }}
        />
      </Card>
    );
  };

  // 渲染模板库
  const renderTemplateLibrary = () => {
    return (
      <Card title="模板库">
        <Row gutter={[16, 16]}>
          {templates.map(template => (
            <Col xs={24} sm={12} md={8} lg={6} key={template.id}>
              <Card
                size="small"
                hoverable
                actions={[
                  <EyeOutlined key="preview" onClick={() => setSelectedTemplate(template)} />,
                  <CopyOutlined key="use" onClick={() => {
                    setSelectedTemplate(template);
                    setShowCreateModal(true);
                  }} />,
                ]}
              >
                <Card.Meta
                  title={template.name}
                  description={
                    <Space direction="vertical" size="small">
                      <div>{template.description}</div>
                      <Space>
                        <Tag color="blue">{template.language}</Tag>
                        <Tag color="green">{template.framework}</Tag>
                      </Space>
                    </Space>
                  }
                />
              </Card>
            </Col>
          ))}
        </Row>
      </Card>
    );
  };

  // 渲染代码编辑器
  const renderCodeEditor = () => {
    if (!selectedProject) {
      return (
        <Card>
          <div style={{ textAlign: 'center', padding: '50px' }}>
            <CodeOutlined style={{ fontSize: '48px', color: '#ccc' }} />
            <p style={{ marginTop: '16px', color: '#999' }}>请选择一个项目开始编辑</p>
          </div>
        </Card>
      );
    }

    return (
      <Layout style={{ height: '600px' }}>
        <Sider width={300} theme="light" style={{ borderRight: '1px solid #f0f0f0' }}>
          <div style={{ padding: '16px', borderBottom: '1px solid #f0f0f0' }}>
            <h4>{selectedProject.name}</h4>
            <p style={{ margin: 0, color: '#666' }}>{selectedProject.description}</p>
          </div>
          <div style={{ padding: '8px' }}>
            <DirectoryTree
              onSelect={handleFileSelect}
              treeData={[
                {
                  title: 'src',
                  key: 'src',
                  icon: <FolderOutlined />,
                  children: [
                    {
                      title: 'main.py',
                      key: 'src/main.py',
                      icon: <FileOutlined />,
                      isLeaf: true,
                    },
                    {
                      title: 'utils.py',
                      key: 'src/utils.py',
                      icon: <FileOutlined />,
                      isLeaf: true,
                    },
                  ],
                },
                {
                  title: 'tests',
                  key: 'tests',
                  icon: <FolderOutlined />,
                  children: [
                    {
                      title: 'test_main.py',
                      key: 'tests/test_main.py',
                      icon: <FileOutlined />,
                      isLeaf: true,
                    },
                  ],
                },
                {
                  title: 'README.md',
                  key: 'README.md',
                  icon: <FileOutlined />,
                  isLeaf: true,
                },
              ]}
            />
          </div>
        </Sider>
        <Content>
          <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
            <div style={{ padding: '8px 16px', borderBottom: '1px solid #f0f0f0', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <span>{selectedFile || '未选择文件'}</span>
              <Space>
                <Button size="small" onClick={handleSaveFile} disabled={!selectedFile}>
                  保存
                </Button>
                <Button size="small" icon={<ReloadOutlined />}>
                  刷新
                </Button>
              </Space>
            </div>
            <div style={{ flex: 1 }}>
              <MonacoEditor
                height="100%"
                language="python"
                theme="vs-dark"
                value={editorContent}
                onChange={(value) => setEditorContent(value || '')}
                options={{
                  selectOnLineNumbers: true,
                  automaticLayout: true,
                  minimap: { enabled: false },
                }}
              />
            </div>
          </div>
        </Content>
      </Layout>
    );
  };

  // 渲染构建状态
  const renderBuildStatus = () => {
    return (
      <Card title="构建状态">
        {selectedProject ? (
          <Space direction="vertical" style={{ width: '100%' }}>
            <Descriptions column={2}>
              <Descriptions.Item label="项目名称">{selectedProject.name}</Descriptions.Item>
              <Descriptions.Item label="状态">
                <Tag color={selectedProject.status === 'building' ? 'blue' : 'green'}>
                  {selectedProject.status === 'building' ? '构建中' : '就绪'}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="最后构建">{selectedProject.lastBuild}</Descriptions.Item>
              <Descriptions.Item label="进度">
                {selectedProject.buildProgress ? `${selectedProject.buildProgress}%` : '0%'}
              </Descriptions.Item>
            </Descriptions>
            
            {selectedProject.status === 'building' && (
              <Progress percent={selectedProject.buildProgress || 0} />
            )}
            
            <Divider>构建日志</Divider>
            <div style={{ 
              height: '300px', 
              overflow: 'auto', 
              backgroundColor: '#000', 
              color: '#fff', 
              padding: '8px',
              fontFamily: 'monospace',
              fontSize: '12px'
            }}>
              {buildLogs.map((log, index) => (
                <div key={index}>{log}</div>
              ))}
            </div>
          </Space>
        ) : (
          <div style={{ textAlign: 'center', padding: '50px' }}>
            <BugOutlined style={{ fontSize: '48px', color: '#ccc' }} />
            <p style={{ marginTop: '16px', color: '#999' }}>请选择一个项目查看构建状态</p>
          </div>
        )}
      </Card>
    );
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Content style={{ padding: '24px' }}>
        <div style={{ marginBottom: '16px' }}>
          <h2>开发工具</h2>
          <p>统一的开发环境和工具链管理平台</p>
        </div>

        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="项目管理" key="projects">
            {renderProjectList()}
          </TabPane>
          <TabPane tab="模板库" key="templates">
            {renderTemplateLibrary()}
          </TabPane>
          <TabPane tab="代码编辑" key="editor">
            {renderCodeEditor()}
          </TabPane>
          <TabPane tab="构建状态" key="build">
            {renderBuildStatus()}
          </TabPane>
        </Tabs>

        {/* 创建项目模态框 */}
        <Modal
          title="创建项目"
          open={showCreateModal}
          onCancel={() => {
            setShowCreateModal(false);
            setSelectedTemplate(null);
          }}
          footer={null}
          width={800}
        >
          <Form onFinish={handleCreateProject} layout="vertical">
            <Form.Item
              name="name"
              label="项目名称"
              rules={[{ required: true, message: '请输入项目名称' }]}
            >
              <Input placeholder="输入项目名称" />
            </Form.Item>
            
            <Form.Item
              name="description"
              label="项目描述"
            >
              <TextArea rows={3} placeholder="输入项目描述" />
            </Form.Item>
            
            {!selectedTemplate && (
              <Form.Item
                name="template"
                label="选择模板"
                rules={[{ required: true, message: '请选择模板' }]}
              >
                <Select placeholder="选择项目模板" onChange={(value) => {
                  const template = templates.find(t => t.id === value);
                  setSelectedTemplate(template || null);
                }}>
                  {templates.map(template => (
                    <Option key={template.id} value={template.id}>
                      {template.name} - {template.description}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            )}
            
            {selectedTemplate && (
              <Card title={`模板配置: ${selectedTemplate.name}`} size="small">
                {selectedTemplate.parameters.map(param => (
                  <Form.Item
                    key={param.name}
                    name={['parameters', param.name]}
                    label={param.description}
                    rules={[{ required: param.required, message: `请输入${param.description}` }]}
                    initialValue={param.defaultValue}
                  >
                    {param.type === 'select' ? (
                      <Select placeholder={`选择${param.description}`}>
                        {param.options?.map(option => (
                          <Option key={option} value={option}>{option}</Option>
                        ))}
                      </Select>
                    ) : param.type === 'boolean' ? (
                      <Select placeholder={`选择${param.description}`}>
                        <Option value={true}>是</Option>
                        <Option value={false}>否</Option>
                      </Select>
                    ) : (
                      <Input placeholder={`输入${param.description}`} />
                    )}
                  </Form.Item>
                ))}
              </Card>
            )}
            
            <Form.Item style={{ marginTop: '24px', textAlign: 'right' }}>
              <Space>
                <Button onClick={() => {
                  setShowCreateModal(false);
                  setSelectedTemplate(null);
                }}>
                  取消
                </Button>
                <Button type="primary" htmlType="submit">
                  创建项目
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Modal>

        {/* 模板详情模态框 */}
        <Modal
          title="模板详情"
          open={showTemplateModal}
          onCancel={() => setShowTemplateModal(false)}
          footer={[
            <Button key="cancel" onClick={() => setShowTemplateModal(false)}>
              关闭
            </Button>,
            <Button key="use" type="primary" onClick={() => {
              setShowTemplateModal(false);
              setShowCreateModal(true);
            }}>
              使用模板
            </Button>,
          ]}
        >
          {selectedTemplate && (
            <Descriptions column={1}>
              <Descriptions.Item label="模板名称">{selectedTemplate.name}</Descriptions.Item>
              <Descriptions.Item label="描述">{selectedTemplate.description}</Descriptions.Item>
              <Descriptions.Item label="分类">{selectedTemplate.category}</Descriptions.Item>
              <Descriptions.Item label="编程语言">{selectedTemplate.language}</Descriptions.Item>
              <Descriptions.Item label="框架">{selectedTemplate.framework}</Descriptions.Item>
              <Descriptions.Item label="版本">{selectedTemplate.version}</Descriptions.Item>
              <Descriptions.Item label="参数">
                {selectedTemplate.parameters.map(param => (
                  <Tag key={param.name} color={param.required ? 'red' : 'blue'}>
                    {param.name}: {param.type}
                  </Tag>
                ))}
              </Descriptions.Item>
            </Descriptions>
          )}
        </Modal>
      </Content>
    </Layout>
  );
};

export default DevelopmentTools;
