{"name": "autodriving-frontend", "version": "1.0.0", "description": "自动驾驶开发加速系统前端应用", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,css,md}\"", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.14.2", "@reduxjs/toolkit": "^1.9.5", "react-redux": "^8.1.2", "antd": "^5.8.2", "@ant-design/icons": "^5.2.5", "three": "^0.154.0", "@types/three": "^0.154.0", "mapbox-gl": "^2.15.0", "@types/mapbox-gl": "^2.7.13", "monaco-editor": "^0.41.0", "@monaco-editor/react": "^4.5.1", "axios": "^1.4.0", "dayjs": "^1.11.9", "lodash-es": "^4.17.21", "@types/lodash-es": "^4.17.8", "recharts": "^2.7.2", "react-query": "^3.39.3", "socket.io-client": "^4.7.2", "react-helmet-async": "^1.3.0", "react-hot-toast": "^2.4.1", "framer-motion": "^10.16.1", "react-beautiful-dnd": "^13.1.1", "@types/react-beautiful-dnd": "^13.1.4", "react-virtualized": "^9.22.5", "@types/react-virtualized": "^9.21.21", "react-window": "^1.8.8", "@types/react-window": "^1.8.5"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "prettier": "^3.0.0", "typescript": "^5.0.2", "vite": "^4.4.5", "vitest": "^0.34.1", "@vitest/ui": "^0.34.1", "c8": "^8.0.1", "jsdom": "^22.1.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/user-event": "^14.4.3", "autoprefixer": "^10.4.14", "postcss": "^8.4.27", "tailwindcss": "^3.3.3", "sass": "^1.64.2", "less": "^4.1.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "keywords": ["自动驾驶", "开发工具", "仿真", "地图编辑", "React", "TypeScript", "Vite"], "author": "自动驾驶开发团队", "license": "MIT", "repository": {"type": "git", "url": "https://git.atjog.com/aier/ai-autonomous-driving.git"}, "bugs": {"url": "https://git.atjog.com/aier/ai-autonomous-driving/issues"}, "homepage": "https://git.atjog.com/aier/ai-autonomous-driving#readme"}