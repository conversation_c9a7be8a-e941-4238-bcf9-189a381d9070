// 自动驾驶开发加速系统 - 性能负载测试
import http from 'k6/http';
import ws from 'k6/ws';
import { check, group, sleep } from 'k6';
import { Rate, Trend, Counter } from 'k6/metrics';

// 自定义指标
const errorRate = new Rate('error_rate');
const responseTime = new Trend('response_time');
const wsConnections = new Counter('ws_connections');
const wsMessages = new Counter('ws_messages');

// 测试配置
export const options = {
  stages: [
    // 预热阶段
    { duration: '2m', target: 10 },
    // 负载增长阶段
    { duration: '5m', target: 50 },
    // 高负载维持阶段
    { duration: '10m', target: 100 },
    // 峰值负载测试
    { duration: '2m', target: 200 },
    // 负载下降阶段
    { duration: '5m', target: 50 },
    // 冷却阶段
    { duration: '2m', target: 0 },
  ],
  thresholds: {
    // HTTP请求成功率应该大于95%
    http_req_failed: ['rate<0.05'],
    // 95%的请求响应时间应该小于2秒
    http_req_duration: ['p(95)<2000'],
    // 平均响应时间应该小于1秒
    response_time: ['avg<1000'],
    // 错误率应该小于5%
    error_rate: ['rate<0.05'],
    // WebSocket连接成功率
    ws_connecting: ['rate>0.95'],
  },
};

// 环境配置
const BASE_URL = __ENV.BASE_URL || 'http://localhost:8080';
const WS_URL = __ENV.WS_URL || 'ws://localhost:8080';

// 测试数据
const TEST_USERS = [
  { username: 'load_test_user_1', password: 'password123' },
  { username: 'load_test_user_2', password: 'password123' },
  { username: 'load_test_user_3', password: 'password123' },
];

const TEST_PROJECTS = [
  {
    name: 'LoadTest项目1',
    description: '负载测试项目1',
    template: 'python-fastapi'
  },
  {
    name: 'LoadTest项目2',
    description: '负载测试项目2',
    template: 'react-typescript'
  },
];

// 认证令牌缓存
let authTokens = {};

// 获取认证令牌
function getAuthToken(username, password) {
  if (authTokens[username]) {
    return authTokens[username];
  }

  const loginResponse = http.post(`${BASE_URL}/api/v1/auth/login`, {
    username: username,
    password: password,
  }, {
    headers: { 'Content-Type': 'application/json' },
  });

  if (loginResponse.status === 200) {
    const token = JSON.parse(loginResponse.body).data.token;
    authTokens[username] = token;
    return token;
  }

  return null;
}

// 主测试函数
export default function () {
  // 随机选择测试用户
  const user = TEST_USERS[Math.floor(Math.random() * TEST_USERS.length)];
  const token = getAuthToken(user.username, user.password);

  if (!token) {
    errorRate.add(1);
    return;
  }

  const headers = {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json',
  };

  // 测试场景1：系统管理API
  group('系统管理API测试', () => {
    // 获取用户信息
    group('获取用户信息', () => {
      const start = Date.now();
      const response = http.get(`${BASE_URL}/api/v1/users/profile`, { headers });
      const duration = Date.now() - start;
      
      responseTime.add(duration);
      
      const success = check(response, {
        '状态码为200': (r) => r.status === 200,
        '响应时间小于1秒': (r) => duration < 1000,
        '返回用户数据': (r) => JSON.parse(r.body).data.username === user.username,
      });
      
      if (!success) errorRate.add(1);
    });

    // 获取用户列表
    group('获取用户列表', () => {
      const start = Date.now();
      const response = http.get(`${BASE_URL}/api/v1/users?page=1&pageSize=20`, { headers });
      const duration = Date.now() - start;
      
      responseTime.add(duration);
      
      const success = check(response, {
        '状态码为200': (r) => r.status === 200,
        '响应时间小于2秒': (r) => duration < 2000,
        '返回用户列表': (r) => JSON.parse(r.body).data.users.length > 0,
      });
      
      if (!success) errorRate.add(1);
    });

    sleep(1);
  });

  // 测试场景2：开发工具API
  group('开发工具API测试', () => {
    // 获取项目列表
    group('获取项目列表', () => {
      const start = Date.now();
      const response = http.get(`${BASE_URL}/api/v1/development-tools/projects`, { headers });
      const duration = Date.now() - start;
      
      responseTime.add(duration);
      
      const success = check(response, {
        '状态码为200': (r) => r.status === 200,
        '响应时间小于1.5秒': (r) => duration < 1500,
        '返回项目数据': (r) => JSON.parse(r.body).success === true,
      });
      
      if (!success) errorRate.add(1);
    });

    // 获取模板列表
    group('获取模板列表', () => {
      const start = Date.now();
      const response = http.get(`${BASE_URL}/api/v1/development-tools/templates`, { headers });
      const duration = Date.now() - start;
      
      responseTime.add(duration);
      
      const success = check(response, {
        '状态码为200': (r) => r.status === 200,
        '响应时间小于1秒': (r) => duration < 1000,
        '返回模板数据': (r) => JSON.parse(r.body).data.templates.length > 0,
      });
      
      if (!success) errorRate.add(1);
    });

    // 创建项目（概率性执行）
    if (Math.random() < 0.1) { // 10%的概率创建项目
      group('创建项目', () => {
        const project = TEST_PROJECTS[Math.floor(Math.random() * TEST_PROJECTS.length)];
        const projectData = {
          ...project,
          name: `${project.name}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        };

        const start = Date.now();
        const response = http.post(
          `${BASE_URL}/api/v1/development-tools/projects`,
          JSON.stringify(projectData),
          { headers }
        );
        const duration = Date.now() - start;
        
        responseTime.add(duration);
        
        const success = check(response, {
          '状态码为201': (r) => r.status === 201,
          '响应时间小于3秒': (r) => duration < 3000,
          '返回项目ID': (r) => JSON.parse(r.body).data.id !== undefined,
        });
        
        if (!success) errorRate.add(1);
      });
    }

    sleep(1);
  });

  // 测试场景3：仿真集成API
  group('仿真集成API测试', () => {
    // 获取仿真会话列表
    group('获取仿真会话', () => {
      const start = Date.now();
      const response = http.get(`${BASE_URL}/api/v1/simulation/sessions`, { headers });
      const duration = Date.now() - start;
      
      responseTime.add(duration);
      
      const success = check(response, {
        '状态码为200': (r) => r.status === 200,
        '响应时间小于1.5秒': (r) => duration < 1500,
      });
      
      if (!success) errorRate.add(1);
    });

    // 获取场景列表
    group('获取场景列表', () => {
      const start = Date.now();
      const response = http.get(`${BASE_URL}/api/v1/simulation/scenarios`, { headers });
      const duration = Date.now() - start;
      
      responseTime.add(duration);
      
      const success = check(response, {
        '状态码为200': (r) => r.status === 200,
        '响应时间小于1秒': (r) => duration < 1000,
      });
      
      if (!success) errorRate.add(1);
    });

    sleep(1);
  });

  // 测试场景4：地图编辑API
  group('地图编辑API测试', () => {
    // 获取地图列表
    group('获取地图列表', () => {
      const start = Date.now();
      const response = http.get(`${BASE_URL}/api/v1/map-editor/maps`, { headers });
      const duration = Date.now() - start;
      
      responseTime.add(duration);
      
      const success = check(response, {
        '状态码为200': (r) => r.status === 200,
        '响应时间小于2秒': (r) => duration < 2000,
      });
      
      if (!success) errorRate.add(1);
    });

    sleep(1);
  });

  // 测试场景5：WebSocket连接（概率性执行）
  if (Math.random() < 0.2) { // 20%的概率测试WebSocket
    group('WebSocket连接测试', () => {
      const wsUrl = `${WS_URL}/api/v1/development-tools/ws?token=${token}`;
      
      const response = ws.connect(wsUrl, {}, function (socket) {
        wsConnections.add(1);
        
        socket.on('open', () => {
          // 发送订阅消息
          socket.send(JSON.stringify({
            type: 'subscribe_build',
            projectId: 'test-project-1'
          }));
          wsMessages.add(1);
        });

        socket.on('message', (data) => {
          wsMessages.add(1);
          const message = JSON.parse(data);
          
          check(message, {
            '消息格式正确': (msg) => msg.type !== undefined,
          });
        });

        socket.on('error', (e) => {
          errorRate.add(1);
        });

        // 保持连接5秒
        sleep(5);
      });

      check(response, {
        'WebSocket连接成功': (r) => r && r.status === 101,
      });
    });
  }

  sleep(Math.random() * 2 + 1); // 随机等待1-3秒
}

// 测试设置阶段
export function setup() {
  console.log('开始负载测试设置...');
  
  // 验证服务可用性
  const healthCheck = http.get(`${BASE_URL}/health`);
  if (healthCheck.status !== 200) {
    throw new Error('服务不可用，无法进行负载测试');
  }
  
  console.log('服务健康检查通过');
  return { timestamp: Date.now() };
}

// 测试清理阶段
export function teardown(data) {
  console.log(`负载测试完成，开始时间: ${new Date(data.timestamp)}`);
  console.log(`测试持续时间: ${(Date.now() - data.timestamp) / 1000}秒`);
}

// 处理摘要数据
export function handleSummary(data) {
  return {
    'test-results/load-test-summary.json': JSON.stringify(data, null, 2),
    'test-results/load-test-summary.html': generateHTMLReport(data),
    stdout: generateConsoleReport(data),
  };
}

// 生成HTML报告
function generateHTMLReport(data) {
  const metrics = data.metrics;
  
  return `
<!DOCTYPE html>
<html>
<head>
    <title>自动驾驶开发加速系统 - 负载测试报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .metric { margin: 10px 0; padding: 10px; border: 1px solid #ddd; }
        .pass { background-color: #d4edda; }
        .fail { background-color: #f8d7da; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>负载测试报告</h1>
    <h2>测试概览</h2>
    <p>测试时间: ${new Date().toISOString()}</p>
    <p>虚拟用户数: ${data.options.stages.map(s => s.target).join(' → ')}</p>
    
    <h2>关键指标</h2>
    <table>
        <tr><th>指标</th><th>值</th><th>阈值</th><th>状态</th></tr>
        <tr class="${metrics.http_req_failed.rate < 0.05 ? 'pass' : 'fail'}">
            <td>HTTP请求失败率</td>
            <td>${(metrics.http_req_failed.rate * 100).toFixed(2)}%</td>
            <td>&lt; 5%</td>
            <td>${metrics.http_req_failed.rate < 0.05 ? '通过' : '失败'}</td>
        </tr>
        <tr class="${metrics.http_req_duration['p(95)'] < 2000 ? 'pass' : 'fail'}">
            <td>95%响应时间</td>
            <td>${metrics.http_req_duration['p(95)'].toFixed(2)}ms</td>
            <td>&lt; 2000ms</td>
            <td>${metrics.http_req_duration['p(95)'] < 2000 ? '通过' : '失败'}</td>
        </tr>
        <tr class="${metrics.response_time.avg < 1000 ? 'pass' : 'fail'}">
            <td>平均响应时间</td>
            <td>${metrics.response_time.avg.toFixed(2)}ms</td>
            <td>&lt; 1000ms</td>
            <td>${metrics.response_time.avg < 1000 ? '通过' : '失败'}</td>
        </tr>
    </table>
    
    <h2>详细指标</h2>
    <pre>${JSON.stringify(metrics, null, 2)}</pre>
</body>
</html>
  `;
}

// 生成控制台报告
function generateConsoleReport(data) {
  const metrics = data.metrics;
  
  return `
========================================
自动驾驶开发加速系统 - 负载测试报告
========================================

测试概览:
- 测试时间: ${new Date().toISOString()}
- 总请求数: ${metrics.http_reqs.count}
- 失败请求数: ${metrics.http_req_failed.count}
- 失败率: ${(metrics.http_req_failed.rate * 100).toFixed(2)}%

响应时间指标:
- 平均响应时间: ${metrics.http_req_duration.avg.toFixed(2)}ms
- 95%响应时间: ${metrics.http_req_duration['p(95)'].toFixed(2)}ms
- 最大响应时间: ${metrics.http_req_duration.max.toFixed(2)}ms

WebSocket指标:
- 连接数: ${metrics.ws_connections ? metrics.ws_connections.count : 0}
- 消息数: ${metrics.ws_messages ? metrics.ws_messages.count : 0}

阈值检查:
- HTTP失败率 < 5%: ${metrics.http_req_failed.rate < 0.05 ? '✓ 通过' : '✗ 失败'}
- 95%响应时间 < 2s: ${metrics.http_req_duration['p(95)'] < 2000 ? '✓ 通过' : '✗ 失败'}
- 平均响应时间 < 1s: ${metrics.response_time.avg < 1000 ? '✓ 通过' : '✗ 失败'}

========================================
  `;
}
