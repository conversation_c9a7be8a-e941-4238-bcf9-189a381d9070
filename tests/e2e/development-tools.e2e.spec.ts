// 自动驾驶开发加速系统 - 开发工具端到端测试
import { test, expect, Page, BrowserContext } from '@playwright/test';

// 测试配置
const BASE_URL = process.env.E2E_BASE_URL || 'http://localhost:3000';
const API_BASE_URL = process.env.E2E_API_BASE_URL || 'http://localhost:8080';

// 测试用户数据
const TEST_USER = {
  username: 'e2e_test_user',
  email: '<EMAIL>',
  password: 'TestPassword123!',
  fullName: 'E2E Test User'
};

// 测试项目数据
const TEST_PROJECT = {
  name: 'E2E测试项目',
  description: '这是一个端到端测试项目',
  template: 'python-fastapi'
};

// 页面对象模型
class LoginPage {
  constructor(private page: Page) {}

  async goto() {
    await this.page.goto(`${BASE_URL}/login`);
  }

  async login(username: string, password: string) {
    await this.page.fill('[data-testid="username-input"]', username);
    await this.page.fill('[data-testid="password-input"]', password);
    await this.page.click('[data-testid="login-button"]');
  }

  async waitForLogin() {
    await this.page.waitForURL(`${BASE_URL}/dashboard`);
  }
}

class DevelopmentToolsPage {
  constructor(private page: Page) {}

  async goto() {
    await this.page.goto(`${BASE_URL}/development-tools`);
  }

  async createProject(projectData: typeof TEST_PROJECT) {
    // 点击创建项目按钮
    await this.page.click('[data-testid="create-project-button"]');
    
    // 等待模态框出现
    await this.page.waitForSelector('[data-testid="create-project-modal"]');
    
    // 填写项目信息
    await this.page.fill('[data-testid="project-name-input"]', projectData.name);
    await this.page.fill('[data-testid="project-description-input"]', projectData.description);
    
    // 选择模板
    await this.page.click('[data-testid="template-select"]');
    await this.page.click(`[data-testid="template-option-${projectData.template}"]`);
    
    // 提交表单
    await this.page.click('[data-testid="create-project-submit"]');
    
    // 等待项目创建成功
    await this.page.waitForSelector('[data-testid="success-message"]');
  }

  async getProjectList() {
    await this.page.waitForSelector('[data-testid="project-table"]');
    return await this.page.$$eval('[data-testid="project-row"]', rows => 
      rows.map(row => ({
        name: row.querySelector('[data-testid="project-name"]')?.textContent,
        status: row.querySelector('[data-testid="project-status"]')?.textContent,
        description: row.querySelector('[data-testid="project-description"]')?.textContent
      }))
    );
  }

  async buildProject(projectName: string) {
    // 找到项目行
    const projectRow = await this.page.locator(`[data-testid="project-row"]:has-text("${projectName}")`);
    
    // 点击构建按钮
    await projectRow.locator('[data-testid="build-button"]').click();
    
    // 等待构建开始
    await this.page.waitForSelector('[data-testid="build-progress"]');
  }

  async waitForBuildComplete(projectName: string, timeout = 60000) {
    const projectRow = await this.page.locator(`[data-testid="project-row"]:has-text("${projectName}")`);
    
    // 等待构建完成（状态变为运行中）
    await expect(projectRow.locator('[data-testid="project-status"]')).toHaveText('运行中', { timeout });
  }

  async openCodeEditor(projectName: string) {
    // 切换到代码编辑标签
    await this.page.click('[data-testid="code-editor-tab"]');
    
    // 选择项目
    await this.page.click(`[data-testid="project-link"]:has-text("${projectName}")`);
    
    // 等待编辑器加载
    await this.page.waitForSelector('[data-testid="monaco-editor"]');
  }

  async editFile(fileName: string, content: string) {
    // 选择文件
    await this.page.click(`[data-testid="file-tree-item"]:has-text("${fileName}")`);
    
    // 等待文件内容加载
    await this.page.waitForSelector('[data-testid="monaco-editor"]');
    
    // 清空编辑器并输入新内容
    await this.page.keyboard.press('Control+A');
    await this.page.keyboard.type(content);
    
    // 保存文件
    await this.page.click('[data-testid="save-file-button"]');
    
    // 等待保存成功
    await this.page.waitForSelector('[data-testid="save-success-message"]');
  }

  async downloadProject(projectName: string) {
    const projectRow = await this.page.locator(`[data-testid="project-row"]:has-text("${projectName}")`);
    
    // 监听下载事件
    const downloadPromise = this.page.waitForEvent('download');
    
    // 点击下载按钮
    await projectRow.locator('[data-testid="download-button"]').click();
    
    // 等待下载完成
    const download = await downloadPromise;
    return download;
  }

  async deleteProject(projectName: string) {
    const projectRow = await this.page.locator(`[data-testid="project-row"]:has-text("${projectName}")`);
    
    // 点击删除按钮
    await projectRow.locator('[data-testid="delete-button"]').click();
    
    // 确认删除
    await this.page.click('[data-testid="confirm-delete-button"]');
    
    // 等待删除成功
    await this.page.waitForSelector('[data-testid="delete-success-message"]');
  }
}

// 测试套件
test.describe('开发工具端到端测试', () => {
  let context: BrowserContext;
  let page: Page;
  let loginPage: LoginPage;
  let developmentToolsPage: DevelopmentToolsPage;

  test.beforeAll(async ({ browser }) => {
    // 创建浏览器上下文
    context = await browser.newContext({
      viewport: { width: 1920, height: 1080 },
      // 启用视频录制
      recordVideo: {
        dir: 'test-results/videos/',
        size: { width: 1920, height: 1080 }
      }
    });

    page = await context.newPage();
    loginPage = new LoginPage(page);
    developmentToolsPage = new DevelopmentToolsPage(page);

    // 设置请求拦截器用于API测试
    await page.route(`${API_BASE_URL}/**`, async route => {
      const response = await route.fetch();
      await route.fulfill({ response });
    });
  });

  test.afterAll(async () => {
    await context.close();
  });

  test.beforeEach(async () => {
    // 每个测试前都重新登录
    await loginPage.goto();
    await loginPage.login(TEST_USER.username, TEST_USER.password);
    await loginPage.waitForLogin();
  });

  test('应该能够创建新项目', async () => {
    await developmentToolsPage.goto();
    
    // 创建项目
    await developmentToolsPage.createProject(TEST_PROJECT);
    
    // 验证项目出现在列表中
    const projects = await developmentToolsPage.getProjectList();
    const createdProject = projects.find(p => p.name === TEST_PROJECT.name);
    
    expect(createdProject).toBeDefined();
    expect(createdProject?.description).toBe(TEST_PROJECT.description);
    expect(createdProject?.status).toBe('已停止');
  });

  test('应该能够构建项目', async () => {
    await developmentToolsPage.goto();
    
    // 构建项目
    await developmentToolsPage.buildProject(TEST_PROJECT.name);
    
    // 等待构建完成
    await developmentToolsPage.waitForBuildComplete(TEST_PROJECT.name);
    
    // 验证项目状态
    const projects = await developmentToolsPage.getProjectList();
    const builtProject = projects.find(p => p.name === TEST_PROJECT.name);
    
    expect(builtProject?.status).toBe('运行中');
  });

  test('应该能够编辑项目代码', async () => {
    await developmentToolsPage.goto();
    
    // 打开代码编辑器
    await developmentToolsPage.openCodeEditor(TEST_PROJECT.name);
    
    // 编辑文件
    const newContent = `
# 这是修改后的代码
from fastapi import FastAPI

app = FastAPI(title="Modified API")

@app.get("/")
def read_root():
    return {"message": "Hello from modified API"}

@app.get("/test")
def test_endpoint():
    return {"status": "ok", "modified": True}
`;
    
    await developmentToolsPage.editFile('main.py', newContent);
    
    // 验证文件保存成功
    await expect(page.locator('[data-testid="save-success-message"]')).toBeVisible();
  });

  test('应该能够查看构建日志', async () => {
    await developmentToolsPage.goto();
    
    // 切换到构建状态标签
    await page.click('[data-testid="build-status-tab"]');
    
    // 选择项目
    await page.click(`[data-testid="project-link"]:has-text("${TEST_PROJECT.name}")`);
    
    // 验证构建日志显示
    await expect(page.locator('[data-testid="build-logs"]')).toBeVisible();
    
    // 验证日志内容不为空
    const logContent = await page.locator('[data-testid="build-logs"]').textContent();
    expect(logContent).toBeTruthy();
    expect(logContent?.length).toBeGreaterThan(0);
  });

  test('应该能够下载项目', async () => {
    await developmentToolsPage.goto();
    
    // 下载项目
    const download = await developmentToolsPage.downloadProject(TEST_PROJECT.name);
    
    // 验证下载文件
    expect(download.suggestedFilename()).toMatch(/.*\.zip$/);
    
    // 保存下载文件用于验证
    const downloadPath = `test-results/downloads/${download.suggestedFilename()}`;
    await download.saveAs(downloadPath);
    
    // 验证文件存在
    const fs = require('fs');
    expect(fs.existsSync(downloadPath)).toBeTruthy();
  });

  test('应该能够使用模板创建项目', async () => {
    await developmentToolsPage.goto();
    
    // 切换到模板库标签
    await page.click('[data-testid="template-library-tab"]');
    
    // 选择模板
    await page.click('[data-testid="template-card-python-fastapi"]');
    
    // 点击使用模板按钮
    await page.click('[data-testid="use-template-button"]');
    
    // 填写项目信息
    const templateProject = {
      name: '模板测试项目',
      description: '使用模板创建的项目'
    };
    
    await page.fill('[data-testid="project-name-input"]', templateProject.name);
    await page.fill('[data-testid="project-description-input"]', templateProject.description);
    
    // 配置模板参数
    await page.fill('[data-testid="param-project_name"]', 'template-test-api');
    await page.check('[data-testid="param-enable_auth"]');
    await page.selectOption('[data-testid="param-database_type"]', 'postgresql');
    
    // 创建项目
    await page.click('[data-testid="create-project-submit"]');
    
    // 验证项目创建成功
    await expect(page.locator('[data-testid="success-message"]')).toBeVisible();
    
    // 验证项目出现在列表中
    const projects = await developmentToolsPage.getProjectList();
    const templateProject = projects.find(p => p.name === templateProject.name);
    expect(templateProject).toBeDefined();
  });

  test('应该能够实时监控构建进度', async () => {
    await developmentToolsPage.goto();
    
    // 开始构建
    await developmentToolsPage.buildProject(TEST_PROJECT.name);
    
    // 验证进度条出现
    await expect(page.locator('[data-testid="build-progress"]')).toBeVisible();
    
    // 验证进度更新
    let previousProgress = 0;
    let progressUpdated = false;
    
    // 监控进度变化
    for (let i = 0; i < 10; i++) {
      await page.waitForTimeout(1000);
      
      const progressElement = page.locator('[data-testid="build-progress"] .ant-progress-text');
      const progressText = await progressElement.textContent();
      const currentProgress = parseInt(progressText?.replace('%', '') || '0');
      
      if (currentProgress > previousProgress) {
        progressUpdated = true;
        break;
      }
      
      previousProgress = currentProgress;
    }
    
    expect(progressUpdated).toBeTruthy();
  });

  test('应该能够处理网络错误', async () => {
    await developmentToolsPage.goto();
    
    // 模拟网络错误
    await page.route(`${API_BASE_URL}/api/v1/development-tools/**`, route => {
      route.abort('failed');
    });
    
    // 尝试创建项目
    await page.click('[data-testid="create-project-button"]');
    await page.fill('[data-testid="project-name-input"]', '错误测试项目');
    await page.click('[data-testid="create-project-submit"]');
    
    // 验证错误消息显示
    await expect(page.locator('[data-testid="error-message"]')).toBeVisible();
    
    // 恢复网络
    await page.unroute(`${API_BASE_URL}/api/v1/development-tools/**`);
  });

  test('应该能够删除项目', async () => {
    await developmentToolsPage.goto();
    
    // 删除项目
    await developmentToolsPage.deleteProject(TEST_PROJECT.name);
    
    // 验证项目从列表中消失
    const projects = await developmentToolsPage.getProjectList();
    const deletedProject = projects.find(p => p.name === TEST_PROJECT.name);
    
    expect(deletedProject).toBeUndefined();
  });

  test('性能测试：大量项目加载', async () => {
    await developmentToolsPage.goto();
    
    // 记录加载时间
    const startTime = Date.now();
    
    // 等待项目列表加载完成
    await page.waitForSelector('[data-testid="project-table"]');
    
    const loadTime = Date.now() - startTime;
    
    // 验证加载时间在合理范围内（5秒内）
    expect(loadTime).toBeLessThan(5000);
    
    // 验证页面响应性
    const navigationStart = await page.evaluate(() => performance.timing.navigationStart);
    const loadComplete = await page.evaluate(() => performance.timing.loadEventEnd);
    const pageLoadTime = loadComplete - navigationStart;
    
    expect(pageLoadTime).toBeLessThan(3000);
  });

  test('可访问性测试', async () => {
    await developmentToolsPage.goto();
    
    // 检查页面标题
    await expect(page).toHaveTitle(/开发工具/);
    
    // 检查主要元素的可访问性属性
    await expect(page.locator('[data-testid="create-project-button"]')).toHaveAttribute('aria-label');
    
    // 检查表格的可访问性
    await expect(page.locator('[data-testid="project-table"]')).toHaveAttribute('role', 'table');
    
    // 检查键盘导航
    await page.keyboard.press('Tab');
    const focusedElement = await page.evaluate(() => document.activeElement?.getAttribute('data-testid'));
    expect(focusedElement).toBeTruthy();
  });

  test('响应式设计测试', async () => {
    // 测试移动端视图
    await page.setViewportSize({ width: 375, height: 667 });
    await developmentToolsPage.goto();
    
    // 验证移动端布局
    await expect(page.locator('[data-testid="mobile-menu-button"]')).toBeVisible();
    
    // 测试平板视图
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.reload();
    
    // 验证平板布局
    await expect(page.locator('[data-testid="project-table"]')).toBeVisible();
    
    // 恢复桌面视图
    await page.setViewportSize({ width: 1920, height: 1080 });
  });
});
