// 自动驾驶开发加速系统 - 用户认证E2E测试
describe('用户认证功能测试', () => {
  beforeEach(() => {
    // 清理测试数据
    cy.task('clearDatabase');
    cy.visit('/login');
  });

  describe('用户登录', () => {
    it('应该能够成功登录', () => {
      // 输入用户名和密码
      cy.get('[data-testid=username-input]')
        .type(Cypress.env('testUser').username);
      
      cy.get('[data-testid=password-input]')
        .type(Cypress.env('testUser').password);
      
      // 点击登录按钮
      cy.get('[data-testid=login-button]').click();
      
      // 验证登录成功
      cy.url().should('include', '/dashboard');
      cy.get('[data-testid=user-avatar]').should('be.visible');
      cy.get('[data-testid=welcome-message]')
        .should('contain', '欢迎回来');
    });

    it('应该显示错误信息当密码错误时', () => {
      // 输入错误密码
      cy.get('[data-testid=username-input]')
        .type(Cypress.env('testUser').username);
      
      cy.get('[data-testid=password-input]')
        .type('错误密码');
      
      cy.get('[data-testid=login-button]').click();
      
      // 验证错误信息
      cy.get('[data-testid=error-message]')
        .should('be.visible')
        .and('contain', '用户名或密码错误');
    });

    it('应该验证必填字段', () => {
      // 不输入任何信息直接点击登录
      cy.get('[data-testid=login-button]').click();
      
      // 验证表单验证
      cy.get('[data-testid=username-error]')
        .should('be.visible')
        .and('contain', '请输入用户名');
      
      cy.get('[data-testid=password-error]')
        .should('be.visible')
        .and('contain', '请输入密码');
    });

    it('应该支持记住登录状态', () => {
      // 勾选记住我
      cy.get('[data-testid=remember-checkbox]').check();
      
      // 登录
      cy.get('[data-testid=username-input]')
        .type(Cypress.env('testUser').username);
      
      cy.get('[data-testid=password-input]')
        .type(Cypress.env('testUser').password);
      
      cy.get('[data-testid=login-button]').click();
      
      // 验证登录成功
      cy.url().should('include', '/dashboard');
      
      // 刷新页面验证状态保持
      cy.reload();
      cy.url().should('include', '/dashboard');
    });
  });

  describe('用户注册', () => {
    beforeEach(() => {
      cy.visit('/register');
    });

    it('应该能够成功注册新用户', () => {
      const newUser = {
        username: `test_${Date.now()}@autonomous-driving.com`,
        password: 'NewUser123456!',
        confirmPassword: 'NewUser123456!',
        fullName: '测试用户',
        phone: '13800138000'
      };

      // 填写注册表单
      cy.get('[data-testid=username-input]').type(newUser.username);
      cy.get('[data-testid=password-input]').type(newUser.password);
      cy.get('[data-testid=confirm-password-input]').type(newUser.confirmPassword);
      cy.get('[data-testid=fullname-input]').type(newUser.fullName);
      cy.get('[data-testid=phone-input]').type(newUser.phone);
      
      // 同意服务条款
      cy.get('[data-testid=terms-checkbox]').check();
      
      // 提交注册
      cy.get('[data-testid=register-button]').click();
      
      // 验证注册成功
      cy.get('[data-testid=success-message]')
        .should('be.visible')
        .and('contain', '注册成功');
      
      // 应该跳转到登录页面
      cy.url().should('include', '/login');
    });

    it('应该验证密码强度', () => {
      // 输入弱密码
      cy.get('[data-testid=password-input]').type('123');
      
      // 验证密码强度提示
      cy.get('[data-testid=password-strength]')
        .should('be.visible')
        .and('contain', '密码强度：弱');
      
      cy.get('[data-testid=password-requirements]')
        .should('be.visible')
        .and('contain', '密码至少8位');
    });

    it('应该验证密码确认', () => {
      // 输入不匹配的确认密码
      cy.get('[data-testid=password-input]').type('Password123!');
      cy.get('[data-testid=confirm-password-input]').type('DifferentPassword123!');
      
      cy.get('[data-testid=register-button]').click();
      
      // 验证错误信息
      cy.get('[data-testid=confirm-password-error]')
        .should('be.visible')
        .and('contain', '两次输入的密码不一致');
    });
  });

  describe('密码重置', () => {
    beforeEach(() => {
      cy.visit('/forgot-password');
    });

    it('应该能够发送重置密码邮件', () => {
      // 输入邮箱
      cy.get('[data-testid=email-input]')
        .type(Cypress.env('testUser').username);
      
      // 点击发送重置邮件
      cy.get('[data-testid=send-reset-button]').click();
      
      // 验证成功信息
      cy.get('[data-testid=success-message]')
        .should('be.visible')
        .and('contain', '重置密码邮件已发送');
    });

    it('应该验证邮箱格式', () => {
      // 输入无效邮箱
      cy.get('[data-testid=email-input]').type('invalid-email');
      
      cy.get('[data-testid=send-reset-button]').click();
      
      // 验证错误信息
      cy.get('[data-testid=email-error]')
        .should('be.visible')
        .and('contain', '请输入有效的邮箱地址');
    });
  });

  describe('用户登出', () => {
    beforeEach(() => {
      // 先登录
      cy.login(Cypress.env('testUser').username, Cypress.env('testUser').password);
    });

    it('应该能够成功登出', () => {
      // 点击用户头像
      cy.get('[data-testid=user-avatar]').click();
      
      // 点击登出
      cy.get('[data-testid=logout-button]').click();
      
      // 验证登出成功
      cy.url().should('include', '/login');
      cy.get('[data-testid=login-form]').should('be.visible');
    });

    it('应该清除用户会话', () => {
      // 登出
      cy.get('[data-testid=user-avatar]').click();
      cy.get('[data-testid=logout-button]').click();
      
      // 尝试访问受保护页面
      cy.visit('/dashboard');
      
      // 应该重定向到登录页面
      cy.url().should('include', '/login');
    });
  });

  describe('会话管理', () => {
    it('应该在会话过期时重定向到登录页面', () => {
      // 登录
      cy.login(Cypress.env('testUser').username, Cypress.env('testUser').password);
      
      // 模拟会话过期
      cy.window().then((win) => {
        win.localStorage.removeItem('token');
      });
      
      // 尝试访问API
      cy.request({
        url: `${Cypress.env('apiUrl')}/users/profile`,
        failOnStatusCode: false
      }).then((response) => {
        expect(response.status).to.eq(401);
      });
      
      // 刷新页面应该重定向到登录
      cy.reload();
      cy.url().should('include', '/login');
    });

    it('应该在多个标签页中同步登出状态', () => {
      // 登录
      cy.login(Cypress.env('testUser').username, Cypress.env('testUser').password);
      
      // 在新窗口中打开同一页面
      cy.window().then((win) => {
        win.open('/dashboard', '_blank');
      });
      
      // 在原窗口登出
      cy.get('[data-testid=user-avatar]').click();
      cy.get('[data-testid=logout-button]').click();
      
      // 验证登出成功
      cy.url().should('include', '/login');
    });
  });

  describe('权限验证', () => {
    it('普通用户不应该访问管理员页面', () => {
      // 以普通用户登录
      cy.login(Cypress.env('testUser').username, Cypress.env('testUser').password);
      
      // 尝试访问管理员页面
      cy.visit('/admin', { failOnStatusCode: false });
      
      // 应该显示权限不足页面
      cy.get('[data-testid=access-denied]')
        .should('be.visible')
        .and('contain', '权限不足');
    });

    it('管理员应该能够访问管理员页面', () => {
      // 以管理员登录
      cy.login(Cypress.env('adminUser').username, Cypress.env('adminUser').password);
      
      // 访问管理员页面
      cy.visit('/admin');
      
      // 验证页面加载成功
      cy.get('[data-testid=admin-dashboard]').should('be.visible');
      cy.get('[data-testid=admin-menu]').should('be.visible');
    });
  });
});
