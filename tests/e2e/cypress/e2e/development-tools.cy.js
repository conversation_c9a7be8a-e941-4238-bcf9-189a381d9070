// 自动驾驶开发加速系统 - 开发工具E2E测试
describe('开发工具功能测试', () => {
  beforeEach(() => {
    // 登录系统
    cy.login(Cypress.env('testUser').username, Cypress.env('testUser').password);
    cy.visit('/development-tools');
  });

  describe('项目管理', () => {
    it('应该能够创建新项目', () => {
      // 点击创建项目按钮
      cy.get('[data-testid=create-project-button]').click();
      
      // 填写项目信息
      cy.get('[data-testid=project-name-input]')
        .type(Cypress.env('testProject').name);
      
      cy.get('[data-testid=project-description-input]')
        .type(Cypress.env('testProject').description);
      
      // 选择项目模板
      cy.get('[data-testid=template-select]').click();
      cy.get(`[data-value="${Cypress.env('testProject').template}"]`).click();
      
      // 选择技术栈
      cy.get('[data-testid=tech-stack-python]').check();
      cy.get('[data-testid=tech-stack-fastapi]').check();
      
      // 提交创建
      cy.get('[data-testid=create-project-submit]').click();
      
      // 验证项目创建成功
      cy.get('[data-testid=success-notification]')
        .should('be.visible')
        .and('contain', '项目创建成功');
      
      // 验证项目出现在列表中
      cy.get('[data-testid=project-list]')
        .should('contain', Cypress.env('testProject').name);
    });

    it('应该能够查看项目详情', () => {
      // 创建测试项目
      cy.createTestProject();
      
      // 点击项目卡片
      cy.get('[data-testid=project-card]').first().click();
      
      // 验证项目详情页面
      cy.url().should('include', '/development-tools/projects/');
      cy.get('[data-testid=project-title]')
        .should('contain', Cypress.env('testProject').name);
      
      // 验证项目信息
      cy.get('[data-testid=project-description]')
        .should('contain', Cypress.env('testProject').description);
      
      cy.get('[data-testid=project-template]')
        .should('contain', Cypress.env('testProject').template);
    });

    it('应该能够编辑项目信息', () => {
      // 创建测试项目
      cy.createTestProject();
      
      // 进入项目详情
      cy.get('[data-testid=project-card]').first().click();
      
      // 点击编辑按钮
      cy.get('[data-testid=edit-project-button]').click();
      
      // 修改项目信息
      const newDescription = '更新后的项目描述';
      cy.get('[data-testid=project-description-input]')
        .clear()
        .type(newDescription);
      
      // 保存修改
      cy.get('[data-testid=save-project-button]').click();
      
      // 验证修改成功
      cy.get('[data-testid=success-notification]')
        .should('be.visible')
        .and('contain', '项目更新成功');
      
      cy.get('[data-testid=project-description]')
        .should('contain', newDescription);
    });

    it('应该能够删除项目', () => {
      // 创建测试项目
      cy.createTestProject();
      
      // 进入项目详情
      cy.get('[data-testid=project-card]').first().click();
      
      // 点击删除按钮
      cy.get('[data-testid=delete-project-button]').click();
      
      // 确认删除
      cy.get('[data-testid=confirm-delete-button]').click();
      
      // 验证删除成功
      cy.get('[data-testid=success-notification]')
        .should('be.visible')
        .and('contain', '项目删除成功');
      
      // 验证重定向到项目列表
      cy.url().should('include', '/development-tools');
      
      // 验证项目不再出现在列表中
      cy.get('[data-testid=project-list]')
        .should('not.contain', Cypress.env('testProject').name);
    });
  });

  describe('代码生成', () => {
    beforeEach(() => {
      // 创建测试项目并进入
      cy.createTestProject();
      cy.get('[data-testid=project-card]').first().click();
    });

    it('应该能够生成代码', () => {
      // 点击代码生成按钮
      cy.get('[data-testid=generate-code-button]').click();
      
      // 选择代码模板
      cy.get('[data-testid=template-perception]').click();
      
      // 配置模板参数
      cy.get('[data-testid=module-name-input]').type('TestPerceptionModule');
      cy.get('[data-testid=sensor-type-select]').select('camera');
      
      // 生成代码
      cy.get('[data-testid=generate-button]').click();
      
      // 验证代码生成成功
      cy.get('[data-testid=success-notification]')
        .should('be.visible')
        .and('contain', '代码生成成功');
      
      // 验证代码编辑器显示生成的代码
      cy.get('[data-testid=code-editor]').should('be.visible');
      cy.get('.monaco-editor').should('be.visible');
    });

    it('应该能够预览生成的代码', () => {
      // 生成代码
      cy.get('[data-testid=generate-code-button]').click();
      cy.get('[data-testid=template-planning]').click();
      cy.get('[data-testid=module-name-input]').type('TestPlanningModule');
      
      // 点击预览按钮
      cy.get('[data-testid=preview-button]').click();
      
      // 验证预览窗口
      cy.get('[data-testid=code-preview-modal]').should('be.visible');
      cy.get('[data-testid=preview-content]').should('contain', 'TestPlanningModule');
      
      // 关闭预览
      cy.get('[data-testid=close-preview-button]').click();
      cy.get('[data-testid=code-preview-modal]').should('not.exist');
    });

    it('应该能够下载生成的代码', () => {
      // 生成代码
      cy.get('[data-testid=generate-code-button]').click();
      cy.get('[data-testid=template-control]').click();
      cy.get('[data-testid=module-name-input]').type('TestControlModule');
      cy.get('[data-testid=generate-button]').click();
      
      // 等待代码生成完成
      cy.get('[data-testid=success-notification]').should('be.visible');
      
      // 点击下载按钮
      cy.get('[data-testid=download-code-button]').click();
      
      // 验证下载开始
      cy.get('[data-testid=download-notification]')
        .should('be.visible')
        .and('contain', '开始下载');
    });
  });

  describe('版本控制', () => {
    beforeEach(() => {
      // 创建测试项目并进入
      cy.createTestProject();
      cy.get('[data-testid=project-card]').first().click();
    });

    it('应该能够初始化Git仓库', () => {
      // 点击版本控制标签
      cy.get('[data-testid=version-control-tab]').click();
      
      // 点击初始化Git按钮
      cy.get('[data-testid=init-git-button]').click();
      
      // 配置Git信息
      cy.get('[data-testid=git-username-input]').type('Test User');
      cy.get('[data-testid=git-email-input]').type('<EMAIL>');
      
      // 确认初始化
      cy.get('[data-testid=confirm-init-button]').click();
      
      // 验证初始化成功
      cy.get('[data-testid=success-notification]')
        .should('be.visible')
        .and('contain', 'Git仓库初始化成功');
      
      // 验证Git状态显示
      cy.get('[data-testid=git-status]').should('be.visible');
      cy.get('[data-testid=current-branch]').should('contain', 'main');
    });

    it('应该能够提交代码', () => {
      // 初始化Git仓库
      cy.initGitRepository();
      
      // 生成一些代码
      cy.generateTestCode();
      
      // 查看Git状态
      cy.get('[data-testid=version-control-tab]').click();
      cy.get('[data-testid=git-status-list]').should('be.visible');
      
      // 暂存文件
      cy.get('[data-testid=stage-all-button]').click();
      
      // 输入提交信息
      cy.get('[data-testid=commit-message-input]')
        .type('feat: 添加测试模块代码');
      
      // 提交代码
      cy.get('[data-testid=commit-button]').click();
      
      // 验证提交成功
      cy.get('[data-testid=success-notification]')
        .should('be.visible')
        .and('contain', '代码提交成功');
      
      // 验证提交历史
      cy.get('[data-testid=commit-history]').should('be.visible');
      cy.get('[data-testid=latest-commit]')
        .should('contain', 'feat: 添加测试模块代码');
    });

    it('应该能够创建和切换分支', () => {
      // 初始化Git仓库
      cy.initGitRepository();
      
      // 创建新分支
      cy.get('[data-testid=version-control-tab]').click();
      cy.get('[data-testid=create-branch-button]').click();
      
      // 输入分支名称
      cy.get('[data-testid=branch-name-input]').type('feature/test-feature');
      
      // 确认创建
      cy.get('[data-testid=confirm-create-branch]').click();
      
      // 验证分支创建成功
      cy.get('[data-testid=success-notification]')
        .should('be.visible')
        .and('contain', '分支创建成功');
      
      // 验证当前分支
      cy.get('[data-testid=current-branch]')
        .should('contain', 'feature/test-feature');
      
      // 切换回主分支
      cy.get('[data-testid=branch-select]').click();
      cy.get('[data-value="main"]').click();
      
      // 验证分支切换
      cy.get('[data-testid=current-branch]').should('contain', 'main');
    });
  });

  describe('构建和部署', () => {
    beforeEach(() => {
      // 创建测试项目并进入
      cy.createTestProject();
      cy.get('[data-testid=project-card]').first().click();
    });

    it('应该能够构建项目', () => {
      // 生成代码
      cy.generateTestCode();
      
      // 点击构建标签
      cy.get('[data-testid=build-tab]').click();
      
      // 点击构建按钮
      cy.get('[data-testid=build-button]').click();
      
      // 验证构建开始
      cy.get('[data-testid=build-status]')
        .should('be.visible')
        .and('contain', '构建中');
      
      // 等待构建完成
      cy.get('[data-testid=build-status]', { timeout: 60000 })
        .should('contain', '构建成功');
      
      // 验证构建日志
      cy.get('[data-testid=build-log]').should('be.visible');
      cy.get('[data-testid=build-log]').should('contain', 'Build completed');
    });

    it('应该能够查看构建历史', () => {
      // 执行构建
      cy.buildProject();
      
      // 查看构建历史
      cy.get('[data-testid=build-tab]').click();
      cy.get('[data-testid=build-history-button]').click();
      
      // 验证构建历史列表
      cy.get('[data-testid=build-history-list]').should('be.visible');
      cy.get('[data-testid=build-item]').should('have.length.at.least', 1);
      
      // 点击查看构建详情
      cy.get('[data-testid=build-item]').first().click();
      
      // 验证构建详情
      cy.get('[data-testid=build-details]').should('be.visible');
      cy.get('[data-testid=build-log-detail]').should('be.visible');
    });

    it('应该能够部署到测试环境', () => {
      // 构建项目
      cy.buildProject();
      
      // 点击部署按钮
      cy.get('[data-testid=deploy-button]').click();
      
      // 选择部署环境
      cy.get('[data-testid=environment-select]').select('testing');
      
      // 确认部署
      cy.get('[data-testid=confirm-deploy-button]').click();
      
      // 验证部署开始
      cy.get('[data-testid=deploy-status]')
        .should('be.visible')
        .and('contain', '部署中');
      
      // 等待部署完成
      cy.get('[data-testid=deploy-status]', { timeout: 120000 })
        .should('contain', '部署成功');
      
      // 验证部署信息
      cy.get('[data-testid=deployment-info]').should('be.visible');
      cy.get('[data-testid=deployment-url]').should('be.visible');
    });
  });
});
