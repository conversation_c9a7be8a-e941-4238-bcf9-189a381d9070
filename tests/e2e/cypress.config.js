// 自动驾驶开发加速系统 - Cypress E2E测试配置
const { defineConfig } = require('cypress');

module.exports = defineConfig({
  e2e: {
    // 基础配置
    baseUrl: 'http://localhost:3000',
    viewportWidth: 1280,
    viewportHeight: 720,
    
    // 测试文件配置
    specPattern: 'cypress/e2e/**/*.cy.{js,jsx,ts,tsx}',
    supportFile: 'cypress/support/e2e.js',
    fixturesFolder: 'cypress/fixtures',
    screenshotsFolder: 'cypress/screenshots',
    videosFolder: 'cypress/videos',
    
    // 超时配置
    defaultCommandTimeout: 10000,
    requestTimeout: 10000,
    responseTimeout: 10000,
    pageLoadTimeout: 30000,
    
    // 重试配置
    retries: {
      runMode: 2,
      openMode: 0
    },
    
    // 视频和截图配置
    video: true,
    videoCompression: 32,
    screenshotOnRunFailure: true,
    
    // 环境变量
    env: {
      // API配置
      apiUrl: 'http://localhost:8080/api/v1',
      
      // 测试用户配置
      testUser: {
        username: '<EMAIL>',
        password: 'Test123456!',
        role: 'developer'
      },
      
      adminUser: {
        username: '<EMAIL>',
        password: 'Admin123456!',
        role: 'admin'
      },
      
      // 测试数据配置
      testProject: {
        name: 'E2E测试项目',
        description: '端到端测试专用项目',
        template: 'python-fastapi'
      },
      
      // 仿真器配置
      simulator: {
        type: 'carla',
        host: 'localhost',
        port: 2000
      },
      
      // 地图测试配置
      testMap: {
        name: 'E2E测试地图',
        format: 'opendrive',
        file: 'test-map.xodr'
      }
    },
    
    setupNodeEvents(on, config) {
      // 任务配置
      on('task', {
        // 数据库清理任务
        clearDatabase() {
          return new Promise((resolve) => {
            // 清理测试数据
            console.log('清理测试数据库...');
            resolve(null);
          });
        },
        
        // 创建测试数据任务
        createTestData() {
          return new Promise((resolve) => {
            // 创建测试数据
            console.log('创建测试数据...');
            resolve(null);
          });
        },
        
        // 启动仿真器任务
        startSimulator() {
          return new Promise((resolve) => {
            // 启动仿真器
            console.log('启动仿真器...');
            resolve(null);
          });
        },
        
        // 停止仿真器任务
        stopSimulator() {
          return new Promise((resolve) => {
            // 停止仿真器
            console.log('停止仿真器...');
            resolve(null);
          });
        }
      });
      
      // 文件处理
      on('before:browser:launch', (browser = {}, launchOptions) => {
        if (browser.name === 'chrome') {
          launchOptions.args.push('--disable-dev-shm-usage');
          launchOptions.args.push('--no-sandbox');
          launchOptions.args.push('--disable-gpu');
        }
        return launchOptions;
      });
      
      return config;
    },
  },
  
  // 组件测试配置
  component: {
    devServer: {
      framework: 'react',
      bundler: 'vite',
    },
    specPattern: 'src/**/*.cy.{js,jsx,ts,tsx}',
    supportFile: 'cypress/support/component.js'
  },
});

// 测试环境配置
const environments = {
  development: {
    baseUrl: 'http://localhost:3000',
    apiUrl: 'http://localhost:8080/api/v1'
  },
  staging: {
    baseUrl: 'https://staging.autonomous-driving-platform.com',
    apiUrl: 'https://staging.autonomous-driving-platform.com/api/v1'
  },
  production: {
    baseUrl: 'https://autonomous-driving-platform.com',
    apiUrl: 'https://autonomous-driving-platform.com/api/v1'
  }
};

// 根据环境变量选择配置
const env = process.env.CYPRESS_ENV || 'development';
if (environments[env]) {
  module.exports.e2e.baseUrl = environments[env].baseUrl;
  module.exports.e2e.env.apiUrl = environments[env].apiUrl;
}
