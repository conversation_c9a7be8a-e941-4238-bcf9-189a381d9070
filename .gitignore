# 自动驾驶开发加速系统 - Git忽略文件配置

# 操作系统相关
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE和编辑器
.vscode/
.idea/
*.swp
*.swo
*~

# 编译输出
build/
dist/
out/
target/
bin/
obj/

# 依赖目录
node_modules/
vendor/
.pnp
.pnp.js

# 环境配置
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志文件
*.log
logs/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 运行时文件
*.pid
*.seed
*.pid.lock

# 覆盖率报告
coverage/
*.lcov
.nyc_output

# 缓存目录
.cache/
.parcel-cache/
.next/
.nuxt/

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 备份文件
*.bak
*.backup
*.tmp

# 压缩文件
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# Python相关
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/
.pytest_cache/
.coverage
htmlcov/

# Go相关
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.out
go.work

# Rust相关
target/
Cargo.lock
**/*.rs.bk

# C++相关
*.o
*.obj
*.exe
*.dll
*.so
*.dylib
*.a
*.lib
*.pdb
*.ilk
*.exp

# Node.js相关
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.npm
.eslintcache
.stylelintcache

# Docker相关
.dockerignore

# Kubernetes相关
*.kubeconfig

# 临时文件
temp/
tmp/
*.tmp
*.temp

# 测试相关
test-results/
playwright-report/
test-results.xml

# 文档生成
docs/build/
site/

# 密钥和证书
*.key
*.pem
*.crt
*.p12
*.pfx

# 配置文件（包含敏感信息）
config/local.yaml
config/production.yaml
secrets/

# 监控和日志
prometheus/data/
grafana/data/
elasticsearch/data/
