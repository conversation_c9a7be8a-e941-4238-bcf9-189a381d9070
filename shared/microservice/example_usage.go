// 自动驾驶开发加速系统 - 微服务通信框架使用示例
package microservice

import (
	"context"
	"log"
	"time"

	"github.com/sony/gobreaker"
	"google.golang.org/grpc"

	pb "autodriving/shared/proto/common"
	"autodriving/shared/tracing"
)

// ExampleService 示例服务
type ExampleService struct {
	framework ServiceFramework
	tracer    *tracing.TracingManager
}

// NewExampleService 创建示例服务
func NewExampleService() *ExampleService {
	// 创建链路追踪配置
	tracingConfig := &tracing.TracingConfig{
		ServiceName:    "example-service",
		ServiceVersion: "1.0.0",
		Environment:    "development",
		JaegerEndpoint: "http://localhost:14268/api/traces",
		SampleRate:     0.1,
		Enabled:        true,
	}

	// 初始化链路追踪
	tracingManager := tracing.NewTracingManager(tracingConfig)
	if err := tracingManager.Initialize(); err != nil {
		log.Printf("初始化链路追踪失败: %v", err)
	}

	// 创建框架配置
	frameworkConfig := &FrameworkConfig{
		ServiceName: "example-service",
		ServiceType: "business",
		ServiceHost: "localhost",
		ServicePort: 8080,

		GRPCConfig: GRPCConfig{
			MaxRecvMsgSize:    4 * 1024 * 1024, // 4MB
			MaxSendMsgSize:    4 * 1024 * 1024, // 4MB
			KeepAliveTime:     30 * time.Second,
			KeepAliveTimeout:  5 * time.Second,
			MaxConnectionIdle: 15 * time.Minute,
			MaxConnectionAge:  30 * time.Minute,
			Timeout:           10 * time.Second,
		},

		CircuitBreakerConfig: CircuitBreakerConfig{
			MaxRequests: 3,
			Interval:    10 * time.Second,
			Timeout:     60 * time.Second,
			ReadyToTrip: func(counts gobreaker.Counts) bool {
				// 失败率超过50%时熔断
				failureRatio := float64(counts.TotalFailures) / float64(counts.Requests)
				return counts.Requests >= 3 && failureRatio >= 0.5
			},
		},

		TracingConfig: TracingConfig{
			Enabled:     true,
			ServiceName: "example-service",
			Endpoint:    "http://localhost:14268/api/traces",
			SampleRate:  0.1,
		},

		DDSConfig: DDSConfig{
			Enabled:    true,
			DomainID:   0,
			ConfigFile: "/etc/dds/config.xml",
		},
	}

	// 创建微服务框架
	framework := NewServiceFramework(frameworkConfig)

	return &ExampleService{
		framework: framework,
		tracer:    tracingManager,
	}
}

// Start 启动服务
func (es *ExampleService) Start(ctx context.Context) error {
	// 启动微服务框架
	if err := es.framework.Start(ctx); err != nil {
		return err
	}

	// 注册自己的服务信息
	serviceInfo := &pb.ServiceInfo{
		Id:      "example-service-001",
		Name:    "example-service",
		Type:    "business",
		Version: "1.0.0",
		Host:    "localhost",
		Port:    8080,
		Status:  "healthy",
		Endpoints: []string{
			"/api/v1/example",
			"/health",
		},
		Metadata: map[string]string{
			"description": "示例业务服务",
			"team":        "development",
		},
	}

	if err := es.framework.RegisterService(serviceInfo); err != nil {
		return err
	}

	// 订阅DDS消息
	if err := es.subscribeDDSMessages(); err != nil {
		log.Printf("订阅DDS消息失败: %v", err)
	}

	log.Println("示例服务启动成功")
	return nil
}

// Stop 停止服务
func (es *ExampleService) Stop() error {
	// 关闭链路追踪
	if err := es.tracer.Shutdown(context.Background()); err != nil {
		log.Printf("关闭链路追踪失败: %v", err)
	}

	// 停止微服务框架
	return es.framework.Stop()
}

// CallOtherService 调用其他服务的示例
func (es *ExampleService) CallOtherService(ctx context.Context, serviceType string) error {
	// 开始链路追踪
	ctx, span := es.tracer.StartSpan(ctx, "call_other_service")
	defer span.End()

	// 创建客户端连接
	conn, err := es.framework.CreateClient(serviceType)
	if err != nil {
		es.tracer.RecordError(span, err)
		return err
	}

	// 创建gRPC客户端（这里以健康检查为例）
	client := pb.NewHealthServiceClient(conn)

	// 调用远程服务
	request := &pb.HealthCheckRequest{
		Service: serviceType,
	}

	response, err := client.Check(ctx, request)
	if err != nil {
		es.tracer.RecordError(span, err)
		return err
	}

	log.Printf("调用 %s 服务成功，状态: %v", serviceType, response.Status)
	return nil
}

// PublishEvent 发布事件示例
func (es *ExampleService) PublishEvent(ctx context.Context, eventType string, data interface{}) error {
	// 开始链路追踪
	ctx, span := es.tracer.StartSpan(ctx, "publish_event")
	defer span.End()

	// 发布DDS消息
	topic := "system_events"
	event := map[string]interface{}{
		"type":      eventType,
		"data":      data,
		"timestamp": time.Now(),
		"source":    "example-service",
	}

	if err := es.framework.PublishMessage(topic, event); err != nil {
		es.tracer.RecordError(span, err)
		return err
	}

	log.Printf("发布事件成功: %s", eventType)
	return nil
}

// subscribeDDSMessages 订阅DDS消息
func (es *ExampleService) subscribeDDSMessages() error {
	// 订阅系统事件
	if err := es.framework.SubscribeMessage("system_events", es.handleSystemEvent); err != nil {
		return err
	}

	// 订阅仿真数据
	if err := es.framework.SubscribeMessage("simulation_data", es.handleSimulationData); err != nil {
		return err
	}

	return nil
}

// handleSystemEvent 处理系统事件
func (es *ExampleService) handleSystemEvent(message interface{}) error {
	log.Printf("收到系统事件: %+v", message)

	// 这里可以添加具体的事件处理逻辑
	// 比如更新服务状态、触发告警等

	return nil
}

// handleSimulationData 处理仿真数据
func (es *ExampleService) handleSimulationData(message interface{}) error {
	log.Printf("收到仿真数据: %+v", message)

	// 这里可以添加仿真数据处理逻辑
	// 比如数据分析、存储、转发等

	return nil
}

// DiscoverServices 服务发现示例
func (es *ExampleService) DiscoverServices(ctx context.Context) error {
	// 开始链路追踪
	ctx, span := es.tracer.StartSpan(ctx, "discover_services")
	defer span.End()

	// 发现系统管理服务
	systemServices, err := es.framework.DiscoverService("system-management")
	if err != nil {
		es.tracer.RecordError(span, err)
		return err
	}

	log.Printf("发现 %d 个系统管理服务:", len(systemServices))
	for _, service := range systemServices {
		log.Printf("  - %s (%s:%d) 状态: %s", 
			service.Name, service.Host, service.Port, service.Status)
	}

	// 发现仿真服务
	simServices, err := es.framework.DiscoverService("simulation")
	if err != nil {
		log.Printf("发现仿真服务失败: %v", err)
		// 不返回错误，因为仿真服务可能不是必需的
	} else {
		log.Printf("发现 %d 个仿真服务:", len(simServices))
		for _, service := range simServices {
			log.Printf("  - %s (%s:%d) 状态: %s", 
				service.Name, service.Host, service.Port, service.Status)
		}
	}

	return nil
}

// HealthCheck 健康检查示例
func (es *ExampleService) HealthCheck(ctx context.Context) *pb.HealthCheckResponse {
	// 检查依赖服务的健康状态
	dependencies := []string{"system-management", "database", "redis"}
	
	for _, dep := range dependencies {
		services, err := es.framework.DiscoverService(dep)
		if err != nil || len(services) == 0 {
			return &pb.HealthCheckResponse{
				Status:  pb.HealthCheckResponse_NOT_SERVING,
				Message: fmt.Sprintf("依赖服务 %s 不可用", dep),
			}
		}
	}

	return &pb.HealthCheckResponse{
		Status:  pb.HealthCheckResponse_SERVING,
		Message: "服务正常运行",
	}
}

// 使用示例
func ExampleUsage() {
	// 创建服务实例
	service := NewExampleService()

	// 启动服务
	ctx := context.Background()
	if err := service.Start(ctx); err != nil {
		log.Fatalf("启动服务失败: %v", err)
	}

	// 服务发现
	if err := service.DiscoverServices(ctx); err != nil {
		log.Printf("服务发现失败: %v", err)
	}

	// 调用其他服务
	if err := service.CallOtherService(ctx, "system-management"); err != nil {
		log.Printf("调用其他服务失败: %v", err)
	}

	// 发布事件
	if err := service.PublishEvent(ctx, "service_started", map[string]string{
		"service": "example-service",
		"version": "1.0.0",
	}); err != nil {
		log.Printf("发布事件失败: %v", err)
	}

	// 健康检查
	health := service.HealthCheck(ctx)
	log.Printf("健康检查结果: %v", health)

	// 优雅关闭
	defer func() {
		if err := service.Stop(); err != nil {
			log.Printf("停止服务失败: %v", err)
		}
	}()
}
