// 自动驾驶开发加速系统 - 微服务通信框架
// 提供gRPC通信、服务发现、熔断器、链路追踪等功能
package microservice

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/sony/gobreaker"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/trace"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/keepalive"
	"google.golang.org/grpc/metadata"

	pb "autodriving/shared/proto/common"
)

// ServiceFramework 微服务框架接口
type ServiceFramework interface {
	// 注册服务
	RegisterService(info *pb.ServiceInfo) error
	
	// 发现服务
	DiscoverService(serviceType string) ([]*pb.ServiceInfo, error)
	
	// 创建客户端连接
	CreateClient(serviceType string) (*grpc.ClientConn, error)
	
	// 发布消息（DDS集成）
	PublishMessage(topic string, message interface{}) error
	
	// 订阅消息（DDS集成）
	SubscribeMessage(topic string, handler MessageHandler) error
	
	// 启动框架
	Start(ctx context.Context) error
	
	// 停止框架
	Stop() error
}

// MessageHandler 消息处理器
type MessageHandler func(message interface{}) error

// serviceFramework 微服务框架实现
type serviceFramework struct {
	config *FrameworkConfig
	
	// 服务发现
	serviceRegistry map[string][]*pb.ServiceInfo
	registryMu      sync.RWMutex
	
	// gRPC连接池
	connections map[string]*grpc.ClientConn
	connMu      sync.RWMutex
	
	// 熔断器
	circuitBreakers map[string]*gobreaker.CircuitBreaker
	breakerMu       sync.RWMutex
	
	// 链路追踪
	tracer trace.Tracer
	
	// DDS集成
	ddsPublishers  map[string]interface{}
	ddsSubscribers map[string]interface{}
	ddsMu          sync.RWMutex
	
	// 运行状态
	running bool
	stopCh  chan struct{}
}

// FrameworkConfig 框架配置
type FrameworkConfig struct {
	// 服务信息
	ServiceName string
	ServiceType string
	ServiceHost string
	ServicePort int
	
	// gRPC配置
	GRPCConfig GRPCConfig
	
	// 熔断器配置
	CircuitBreakerConfig CircuitBreakerConfig
	
	// 链路追踪配置
	TracingConfig TracingConfig
	
	// DDS配置
	DDSConfig DDSConfig
}

// GRPCConfig gRPC配置
type GRPCConfig struct {
	MaxRecvMsgSize    int           // 最大接收消息大小
	MaxSendMsgSize    int           // 最大发送消息大小
	KeepAliveTime     time.Duration // 保活时间
	KeepAliveTimeout  time.Duration // 保活超时
	MaxConnectionIdle time.Duration // 最大连接空闲时间
	MaxConnectionAge  time.Duration // 最大连接年龄
	Timeout           time.Duration // 连接超时
}

// CircuitBreakerConfig 熔断器配置
type CircuitBreakerConfig struct {
	MaxRequests uint32        // 半开状态最大请求数
	Interval    time.Duration // 统计间隔
	Timeout     time.Duration // 熔断超时
	ReadyToTrip func(counts gobreaker.Counts) bool // 熔断条件
}

// TracingConfig 链路追踪配置
type TracingConfig struct {
	Enabled     bool   // 是否启用
	ServiceName string // 服务名称
	Endpoint    string // Jaeger端点
	SampleRate  float64 // 采样率
}

// DDSConfig DDS配置
type DDSConfig struct {
	Enabled    bool   // 是否启用DDS
	DomainID   int    // DDS域ID
	ConfigFile string // DDS配置文件
}

// NewServiceFramework 创建微服务框架
func NewServiceFramework(config *FrameworkConfig) ServiceFramework {
	return &serviceFramework{
		config:          config,
		serviceRegistry: make(map[string][]*pb.ServiceInfo),
		connections:     make(map[string]*grpc.ClientConn),
		circuitBreakers: make(map[string]*gobreaker.CircuitBreaker),
		ddsPublishers:   make(map[string]interface{}),
		ddsSubscribers:  make(map[string]interface{}),
		stopCh:          make(chan struct{}),
	}
}

// RegisterService 注册服务
func (sf *serviceFramework) RegisterService(info *pb.ServiceInfo) error {
	sf.registryMu.Lock()
	defer sf.registryMu.Unlock()
	
	serviceType := info.Type
	if sf.serviceRegistry[serviceType] == nil {
		sf.serviceRegistry[serviceType] = make([]*pb.ServiceInfo, 0)
	}
	
	// 检查是否已存在
	for i, existing := range sf.serviceRegistry[serviceType] {
		if existing.Id == info.Id {
			// 更新现有服务
			sf.serviceRegistry[serviceType][i] = info
			log.Printf("更新服务注册: %s (%s)", info.Name, info.Id)
			return nil
		}
	}
	
	// 添加新服务
	sf.serviceRegistry[serviceType] = append(sf.serviceRegistry[serviceType], info)
	log.Printf("注册新服务: %s (%s)", info.Name, info.Id)
	
	return nil
}

// DiscoverService 发现服务
func (sf *serviceFramework) DiscoverService(serviceType string) ([]*pb.ServiceInfo, error) {
	sf.registryMu.RLock()
	defer sf.registryMu.RUnlock()
	
	services, exists := sf.serviceRegistry[serviceType]
	if !exists {
		return nil, fmt.Errorf("未找到类型为 %s 的服务", serviceType)
	}
	
	// 过滤健康的服务
	var healthyServices []*pb.ServiceInfo
	for _, service := range services {
		if service.Status == "healthy" {
			healthyServices = append(healthyServices, service)
		}
	}
	
	if len(healthyServices) == 0 {
		return nil, fmt.Errorf("没有健康的 %s 服务可用", serviceType)
	}
	
	return healthyServices, nil
}

// CreateClient 创建客户端连接
func (sf *serviceFramework) CreateClient(serviceType string) (*grpc.ClientConn, error) {
	sf.connMu.Lock()
	defer sf.connMu.Unlock()
	
	// 检查是否已有连接
	if conn, exists := sf.connections[serviceType]; exists {
		return conn, nil
	}
	
	// 发现服务
	services, err := sf.DiscoverService(serviceType)
	if err != nil {
		return nil, fmt.Errorf("服务发现失败: %w", err)
	}
	
	// 选择第一个健康的服务（可以实现负载均衡算法）
	service := services[0]
	target := fmt.Sprintf("%s:%d", service.Host, service.Port)
	
	// 创建gRPC连接
	conn, err := sf.createGRPCConnection(target, serviceType)
	if err != nil {
		return nil, fmt.Errorf("创建gRPC连接失败: %w", err)
	}
	
	// 缓存连接
	sf.connections[serviceType] = conn
	
	return conn, nil
}

// createGRPCConnection 创建gRPC连接
func (sf *serviceFramework) createGRPCConnection(target, serviceType string) (*grpc.ClientConn, error) {
	// 配置gRPC选项
	opts := []grpc.DialOption{
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithDefaultCallOptions(
			grpc.MaxCallRecvMsgSize(sf.config.GRPCConfig.MaxRecvMsgSize),
			grpc.MaxCallSendMsgSize(sf.config.GRPCConfig.MaxSendMsgSize),
		),
		grpc.WithKeepaliveParams(keepalive.ClientParameters{
			Time:                sf.config.GRPCConfig.KeepAliveTime,
			Timeout:             sf.config.GRPCConfig.KeepAliveTimeout,
			PermitWithoutStream: true,
		}),
	}
	
	// 添加拦截器
	opts = append(opts,
		grpc.WithUnaryInterceptor(sf.unaryClientInterceptor(serviceType)),
		grpc.WithStreamInterceptor(sf.streamClientInterceptor(serviceType)),
	)
	
	// 创建连接
	ctx, cancel := context.WithTimeout(context.Background(), sf.config.GRPCConfig.Timeout)
	defer cancel()
	
	conn, err := grpc.DialContext(ctx, target, opts...)
	if err != nil {
		return nil, fmt.Errorf("连接到 %s 失败: %w", target, err)
	}
	
	return conn, nil
}

// unaryClientInterceptor 一元客户端拦截器
func (sf *serviceFramework) unaryClientInterceptor(serviceType string) grpc.UnaryClientInterceptor {
	return func(ctx context.Context, method string, req, reply interface{},
		cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {
		
		// 获取熔断器
		breaker := sf.getCircuitBreaker(serviceType)
		
		// 添加链路追踪
		ctx = sf.addTracing(ctx, method)
		
		// 添加认证信息
		ctx = sf.addAuth(ctx)
		
		// 执行请求（带熔断器）
		result, err := breaker.Execute(func() (interface{}, error) {
			return nil, invoker(ctx, method, req, reply, cc, opts...)
		})
		
		if err != nil {
			log.Printf("gRPC调用失败 [%s %s]: %v", serviceType, method, err)
			return err
		}
		
		_ = result // 忽略result，因为reply已经被填充
		return nil
	}
}

// streamClientInterceptor 流客户端拦截器
func (sf *serviceFramework) streamClientInterceptor(serviceType string) grpc.StreamClientInterceptor {
	return func(ctx context.Context, desc *grpc.StreamDesc, cc *grpc.ClientConn,
		method string, streamer grpc.Streamer, opts ...grpc.CallOption) (grpc.ClientStream, error) {
		
		// 添加链路追踪
		ctx = sf.addTracing(ctx, method)
		
		// 添加认证信息
		ctx = sf.addAuth(ctx)
		
		// 创建流
		stream, err := streamer(ctx, desc, cc, method, opts...)
		if err != nil {
			log.Printf("gRPC流创建失败 [%s %s]: %v", serviceType, method, err)
			return nil, err
		}
		
		return stream, nil
	}
}

// getCircuitBreaker 获取熔断器
func (sf *serviceFramework) getCircuitBreaker(serviceType string) *gobreaker.CircuitBreaker {
	sf.breakerMu.Lock()
	defer sf.breakerMu.Unlock()
	
	if breaker, exists := sf.circuitBreakers[serviceType]; exists {
		return breaker
	}
	
	// 创建新的熔断器
	settings := gobreaker.Settings{
		Name:        fmt.Sprintf("%s-breaker", serviceType),
		MaxRequests: sf.config.CircuitBreakerConfig.MaxRequests,
		Interval:    sf.config.CircuitBreakerConfig.Interval,
		Timeout:     sf.config.CircuitBreakerConfig.Timeout,
		ReadyToTrip: sf.config.CircuitBreakerConfig.ReadyToTrip,
		OnStateChange: func(name string, from gobreaker.State, to gobreaker.State) {
			log.Printf("熔断器状态变化 [%s]: %s -> %s", name, from, to)
		},
	}
	
	breaker := gobreaker.NewCircuitBreaker(settings)
	sf.circuitBreakers[serviceType] = breaker
	
	return breaker
}

// addTracing 添加链路追踪
func (sf *serviceFramework) addTracing(ctx context.Context, method string) context.Context {
	if !sf.config.TracingConfig.Enabled {
		return ctx
	}
	
	// 创建span
	ctx, span := sf.tracer.Start(ctx, method)
	defer span.End()
	
	// 添加属性
	span.SetAttributes(
		trace.WithAttributes(),
	)
	
	return ctx
}

// addAuth 添加认证信息
func (sf *serviceFramework) addAuth(ctx context.Context) context.Context {
	// 从上下文获取认证信息
	if authInfo, ok := ctx.Value("auth").(string); ok {
		md := metadata.Pairs("authorization", authInfo)
		ctx = metadata.NewOutgoingContext(ctx, md)
	}
	
	return ctx
}

// PublishMessage 发布消息（DDS集成）
func (sf *serviceFramework) PublishMessage(topic string, message interface{}) error {
	if !sf.config.DDSConfig.Enabled {
		return fmt.Errorf("DDS未启用")
	}
	
	sf.ddsMu.RLock()
	publisher, exists := sf.ddsPublishers[topic]
	sf.ddsMu.RUnlock()
	
	if !exists {
		return fmt.Errorf("主题 %s 的发布者不存在", topic)
	}
	
	// 这里应该调用实际的DDS发布逻辑
	// 由于DDS库的复杂性，这里只是示例
	log.Printf("发布DDS消息到主题 %s: %+v", topic, message)
	_ = publisher // 使用publisher发布消息
	
	return nil
}

// SubscribeMessage 订阅消息（DDS集成）
func (sf *serviceFramework) SubscribeMessage(topic string, handler MessageHandler) error {
	if !sf.config.DDSConfig.Enabled {
		return fmt.Errorf("DDS未启用")
	}
	
	// 这里应该创建DDS订阅者并设置回调
	log.Printf("订阅DDS主题: %s", topic)
	
	sf.ddsMu.Lock()
	sf.ddsSubscribers[topic] = handler
	sf.ddsMu.Unlock()
	
	return nil
}

// Start 启动框架
func (sf *serviceFramework) Start(ctx context.Context) error {
	if sf.running {
		return fmt.Errorf("框架已在运行")
	}
	
	// 初始化链路追踪
	if sf.config.TracingConfig.Enabled {
		sf.tracer = otel.Tracer(sf.config.TracingConfig.ServiceName)
	}
	
	// 初始化DDS
	if sf.config.DDSConfig.Enabled {
		if err := sf.initDDS(); err != nil {
			return fmt.Errorf("初始化DDS失败: %w", err)
		}
	}
	
	sf.running = true
	log.Printf("微服务框架启动成功")
	
	return nil
}

// Stop 停止框架
func (sf *serviceFramework) Stop() error {
	if !sf.running {
		return nil
	}
	
	// 关闭所有连接
	sf.connMu.Lock()
	for serviceType, conn := range sf.connections {
		if err := conn.Close(); err != nil {
			log.Printf("关闭连接失败 [%s]: %v", serviceType, err)
		}
	}
	sf.connections = make(map[string]*grpc.ClientConn)
	sf.connMu.Unlock()
	
	// 清理DDS资源
	if sf.config.DDSConfig.Enabled {
		sf.cleanupDDS()
	}
	
	close(sf.stopCh)
	sf.running = false
	log.Printf("微服务框架已停止")
	
	return nil
}

// initDDS 初始化DDS
func (sf *serviceFramework) initDDS() error {
	// 这里应该初始化DDS参与者、发布者、订阅者等
	// 由于DDS库的复杂性，这里只是示例
	log.Printf("初始化DDS，域ID: %d", sf.config.DDSConfig.DomainID)
	return nil
}

// cleanupDDS 清理DDS资源
func (sf *serviceFramework) cleanupDDS() {
	// 清理DDS资源
	log.Printf("清理DDS资源")
}
