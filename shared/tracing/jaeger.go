// 自动驾驶开发加速系统 - Jaeger链路追踪配置
package tracing

import (
	"context"
	"fmt"
	"log"

	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/exporters/jaeger"
	"go.opentelemetry.io/otel/propagation"
	"go.opentelemetry.io/otel/sdk/resource"
	sdktrace "go.opentelemetry.io/otel/sdk/trace"
	semconv "go.opentelemetry.io/otel/semconv/v1.17.0"
	"go.opentelemetry.io/otel/trace"
)

// TracingConfig 链路追踪配置
type TracingConfig struct {
	ServiceName    string  `json:"service_name" yaml:"service_name"`
	ServiceVersion string  `json:"service_version" yaml:"service_version"`
	Environment    string  `json:"environment" yaml:"environment"`
	JaegerEndpoint string  `json:"jaeger_endpoint" yaml:"jaeger_endpoint"`
	SampleRate     float64 `json:"sample_rate" yaml:"sample_rate"`
	Enabled        bool    `json:"enabled" yaml:"enabled"`
}

// TracingManager 链路追踪管理器
type TracingManager struct {
	config   *TracingConfig
	provider *sdktrace.TracerProvider
	tracer   trace.Tracer
}

// NewTracingManager 创建链路追踪管理器
func NewTracingManager(config *TracingConfig) *TracingManager {
	return &TracingManager{
		config: config,
	}
}

// Initialize 初始化链路追踪
func (tm *TracingManager) Initialize() error {
	if !tm.config.Enabled {
		log.Println("链路追踪已禁用")
		return nil
	}

	// 创建Jaeger导出器
	exporter, err := tm.createJaegerExporter()
	if err != nil {
		return fmt.Errorf("创建Jaeger导出器失败: %w", err)
	}

	// 创建资源
	res, err := tm.createResource()
	if err != nil {
		return fmt.Errorf("创建资源失败: %w", err)
	}

	// 创建采样器
	sampler := tm.createSampler()

	// 创建TracerProvider
	tm.provider = sdktrace.NewTracerProvider(
		sdktrace.WithBatcher(exporter),
		sdktrace.WithResource(res),
		sdktrace.WithSampler(sampler),
	)

	// 设置全局TracerProvider
	otel.SetTracerProvider(tm.provider)

	// 设置全局传播器
	otel.SetTextMapPropagator(propagation.NewCompositeTextMapPropagator(
		propagation.TraceContext{},
		propagation.Baggage{},
	))

	// 创建Tracer
	tm.tracer = otel.Tracer(tm.config.ServiceName)

	log.Printf("链路追踪初始化成功，服务: %s，端点: %s", 
		tm.config.ServiceName, tm.config.JaegerEndpoint)

	return nil
}

// createJaegerExporter 创建Jaeger导出器
func (tm *TracingManager) createJaegerExporter() (sdktrace.SpanExporter, error) {
	return jaeger.New(jaeger.WithCollectorEndpoint(
		jaeger.WithEndpoint(tm.config.JaegerEndpoint),
	))
}

// createResource 创建资源
func (tm *TracingManager) createResource() (*resource.Resource, error) {
	return resource.Merge(
		resource.Default(),
		resource.NewWithAttributes(
			semconv.SchemaURL,
			semconv.ServiceName(tm.config.ServiceName),
			semconv.ServiceVersion(tm.config.ServiceVersion),
			semconv.DeploymentEnvironment(tm.config.Environment),
			attribute.String("service.type", "microservice"),
			attribute.String("service.framework", "autodriving"),
		),
	)
}

// createSampler 创建采样器
func (tm *TracingManager) createSampler() sdktrace.Sampler {
	if tm.config.SampleRate <= 0 {
		return sdktrace.NeverSample()
	}
	if tm.config.SampleRate >= 1.0 {
		return sdktrace.AlwaysSample()
	}
	return sdktrace.TraceIDRatioBased(tm.config.SampleRate)
}

// GetTracer 获取Tracer
func (tm *TracingManager) GetTracer() trace.Tracer {
	return tm.tracer
}

// StartSpan 开始一个新的span
func (tm *TracingManager) StartSpan(ctx context.Context, operationName string, opts ...trace.SpanStartOption) (context.Context, trace.Span) {
	if tm.tracer == nil {
		return ctx, trace.SpanFromContext(ctx)
	}
	return tm.tracer.Start(ctx, operationName, opts...)
}

// AddSpanAttributes 添加span属性
func (tm *TracingManager) AddSpanAttributes(span trace.Span, attrs ...attribute.KeyValue) {
	if span != nil {
		span.SetAttributes(attrs...)
	}
}

// AddSpanEvent 添加span事件
func (tm *TracingManager) AddSpanEvent(span trace.Span, name string, attrs ...attribute.KeyValue) {
	if span != nil {
		span.AddEvent(name, trace.WithAttributes(attrs...))
	}
}

// RecordError 记录错误
func (tm *TracingManager) RecordError(span trace.Span, err error, attrs ...attribute.KeyValue) {
	if span != nil && err != nil {
		span.RecordError(err, trace.WithAttributes(attrs...))
		span.SetStatus(trace.StatusError, err.Error())
	}
}

// Shutdown 关闭链路追踪
func (tm *TracingManager) Shutdown(ctx context.Context) error {
	if tm.provider == nil {
		return nil
	}

	if err := tm.provider.Shutdown(ctx); err != nil {
		return fmt.Errorf("关闭TracerProvider失败: %w", err)
	}

	log.Println("链路追踪已关闭")
	return nil
}

// TraceMiddleware gRPC链路追踪中间件
func (tm *TracingManager) TraceMiddleware() func(ctx context.Context, method string, req, reply interface{}, cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {
	return func(ctx context.Context, method string, req, reply interface{}, cc *grpc.ClientConn, invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {
		if tm.tracer == nil {
			return invoker(ctx, method, req, reply, cc, opts...)
		}

		// 开始span
		ctx, span := tm.StartSpan(ctx, fmt.Sprintf("grpc.client.%s", method),
			trace.WithSpanKind(trace.SpanKindClient),
			trace.WithAttributes(
				attribute.String("rpc.system", "grpc"),
				attribute.String("rpc.method", method),
				attribute.String("rpc.service", tm.config.ServiceName),
			),
		)
		defer span.End()

		// 执行调用
		err := invoker(ctx, method, req, reply, cc, opts...)
		if err != nil {
			tm.RecordError(span, err,
				attribute.String("error.type", "grpc_call_error"),
			)
		} else {
			span.SetStatus(trace.StatusOK, "")
		}

		return err
	}
}

// HTTPTraceMiddleware HTTP链路追踪中间件
func (tm *TracingManager) HTTPTraceMiddleware() func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			if tm.tracer == nil {
				next.ServeHTTP(w, r)
				return
			}

			// 从请求头提取trace上下文
			ctx := otel.GetTextMapPropagator().Extract(r.Context(), propagation.HeaderCarrier(r.Header))

			// 开始span
			ctx, span := tm.StartSpan(ctx, fmt.Sprintf("%s %s", r.Method, r.URL.Path),
				trace.WithSpanKind(trace.SpanKindServer),
				trace.WithAttributes(
					attribute.String("http.method", r.Method),
					attribute.String("http.url", r.URL.String()),
					attribute.String("http.scheme", r.URL.Scheme),
					attribute.String("http.host", r.Host),
					attribute.String("http.user_agent", r.UserAgent()),
					attribute.String("http.remote_addr", r.RemoteAddr),
				),
			)
			defer span.End()

			// 创建响应写入器包装器
			wrapped := &responseWriter{
				ResponseWriter: w,
				statusCode:     200,
			}

			// 将trace上下文注入到响应头
			otel.GetTextMapPropagator().Inject(ctx, propagation.HeaderCarrier(w.Header()))

			// 执行请求
			next.ServeHTTP(wrapped, r.WithContext(ctx))

			// 记录响应信息
			tm.AddSpanAttributes(span,
				attribute.Int("http.status_code", wrapped.statusCode),
				attribute.Int("http.response_size", wrapped.bytesWritten),
			)

			// 设置span状态
			if wrapped.statusCode >= 400 {
				span.SetStatus(trace.StatusError, fmt.Sprintf("HTTP %d", wrapped.statusCode))
			} else {
				span.SetStatus(trace.StatusOK, "")
			}
		})
	}
}

// responseWriter 响应写入器包装器
type responseWriter struct {
	http.ResponseWriter
	statusCode   int
	bytesWritten int
}

func (rw *responseWriter) WriteHeader(code int) {
	rw.statusCode = code
	rw.ResponseWriter.WriteHeader(code)
}

func (rw *responseWriter) Write(b []byte) (int, error) {
	n, err := rw.ResponseWriter.Write(b)
	rw.bytesWritten += n
	return n, err
}

// CreateSpanFromContext 从上下文创建子span
func (tm *TracingManager) CreateSpanFromContext(ctx context.Context, operationName string, attrs ...attribute.KeyValue) (context.Context, trace.Span) {
	if tm.tracer == nil {
		return ctx, trace.SpanFromContext(ctx)
	}

	opts := []trace.SpanStartOption{
		trace.WithAttributes(attrs...),
	}

	return tm.tracer.Start(ctx, operationName, opts...)
}

// InjectTraceContext 注入trace上下文到载体
func (tm *TracingManager) InjectTraceContext(ctx context.Context, carrier propagation.TextMapCarrier) {
	otel.GetTextMapPropagator().Inject(ctx, carrier)
}

// ExtractTraceContext 从载体提取trace上下文
func (tm *TracingManager) ExtractTraceContext(ctx context.Context, carrier propagation.TextMapCarrier) context.Context {
	return otel.GetTextMapPropagator().Extract(ctx, carrier)
}

// GetDefaultConfig 获取默认配置
func GetDefaultConfig(serviceName string) *TracingConfig {
	return &TracingConfig{
		ServiceName:    serviceName,
		ServiceVersion: "1.0.0",
		Environment:    "development",
		JaegerEndpoint: "http://localhost:14268/api/traces",
		SampleRate:     0.1, // 10%采样率
		Enabled:        true,
	}
}
