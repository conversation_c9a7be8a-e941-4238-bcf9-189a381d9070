# 自动驾驶开发加速系统 - DDS通信模块构建配置

cmake_minimum_required(VERSION 3.16)
project(AutoDrivingDDS VERSION 1.0.0 LANGUAGES CXX)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 编译选项
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -O2")
set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -g -O0 -DDEBUG")
set(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} -O3 -DNDEBUG")

# 查找依赖包
find_package(PkgConfig REQUIRED)

# 选择DDS实现
option(USE_RTI_DDS "使用RTI Connext DDS" OFF)
option(USE_CYCLONE_DDS "使用Eclipse Cyclone DDS" ON)

if(USE_RTI_DDS)
    # RTI Connext DDS配置
    message(STATUS "使用RTI Connext DDS")
    
    # 查找RTI Connext DDS
    find_package(RTIConnextDDS REQUIRED)
    
    if(NOT RTIConnextDDS_FOUND)
        message(FATAL_ERROR "未找到RTI Connext DDS，请确保已正确安装")
    endif()
    
    # 设置编译定义
    add_definitions(-DUSE_RTI_DDS)
    
    # DDS库
    set(DDS_LIBRARIES ${RTIConnextDDS_LIBRARIES})
    set(DDS_INCLUDE_DIRS ${RTIConnextDDS_INCLUDE_DIRS})
    
elseif(USE_CYCLONE_DDS)
    # Eclipse Cyclone DDS配置
    message(STATUS "使用Eclipse Cyclone DDS")
    
    # 查找Cyclone DDS
    find_package(CycloneDX QUIET)
    if(NOT CycloneDX_FOUND)
        # 尝试使用pkg-config查找
        pkg_check_modules(CYCLONE_DDS REQUIRED cyclonedx)
        if(NOT CYCLONE_DDS_FOUND)
            message(FATAL_ERROR "未找到Eclipse Cyclone DDS，请确保已正确安装")
        endif()
        set(DDS_LIBRARIES ${CYCLONE_DDS_LIBRARIES})
        set(DDS_INCLUDE_DIRS ${CYCLONE_DDS_INCLUDE_DIRS})
    else()
        set(DDS_LIBRARIES CycloneDX::ddsc)
        set(DDS_INCLUDE_DIRS ${CycloneDX_INCLUDE_DIRS})
    endif()
    
    # 设置编译定义
    add_definitions(-DUSE_CYCLONE_DDS)
    
else()
    message(FATAL_ERROR "请选择一个DDS实现：-DUSE_RTI_DDS=ON 或 -DUSE_CYCLONE_DDS=ON")
endif()

# 查找其他依赖
find_package(Threads REQUIRED)

# 测试框架
find_package(GTest QUIET)
if(NOT GTest_FOUND)
    # 如果系统没有安装GTest，使用FetchContent下载
    include(FetchContent)
    FetchContent_Declare(
        googletest
        URL https://github.com/google/googletest/archive/03597a01ee50ed33e9fd7188aa8c636f3b5e3c8e.zip
    )
    FetchContent_MakeAvailable(googletest)
    set(GTEST_LIBRARIES gtest gtest_main)
else()
    set(GTEST_LIBRARIES GTest::gtest GTest::gtest_main)
endif()

# 包含目录
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${DDS_INCLUDE_DIRS}
)

# IDL文件处理
set(IDL_FILES
    types.idl
    autodriving.idl
)

# 生成的源文件列表
set(GENERATED_SOURCES "")
set(GENERATED_HEADERS "")

if(USE_RTI_DDS)
    # RTI DDS代码生成
    foreach(IDL_FILE ${IDL_FILES})
        get_filename_component(IDL_NAME ${IDL_FILE} NAME_WE)
        
        # 生成的文件
        set(GENERATED_CPP "${CMAKE_CURRENT_BINARY_DIR}/${IDL_NAME}.cxx")
        set(GENERATED_HPP "${CMAKE_CURRENT_BINARY_DIR}/${IDL_NAME}.hpp")
        set(GENERATED_PLUGIN_CPP "${CMAKE_CURRENT_BINARY_DIR}/${IDL_NAME}Plugin.cxx")
        set(GENERATED_PLUGIN_HPP "${CMAKE_CURRENT_BINARY_DIR}/${IDL_NAME}Plugin.hpp")
        
        # 添加到生成文件列表
        list(APPEND GENERATED_SOURCES ${GENERATED_CPP} ${GENERATED_PLUGIN_CPP})
        list(APPEND GENERATED_HEADERS ${GENERATED_HPP} ${GENERATED_PLUGIN_HPP})
        
        # 自定义命令生成代码
        add_custom_command(
            OUTPUT ${GENERATED_CPP} ${GENERATED_HPP} ${GENERATED_PLUGIN_CPP} ${GENERATED_PLUGIN_HPP}
            COMMAND rtiddsgen -language C++11 -update typefiles -d ${CMAKE_CURRENT_BINARY_DIR} ${CMAKE_CURRENT_SOURCE_DIR}/${IDL_FILE}
            DEPENDS ${CMAKE_CURRENT_SOURCE_DIR}/${IDL_FILE}
            COMMENT "生成RTI DDS代码: ${IDL_FILE}"
        )
    endforeach()
    
elseif(USE_CYCLONE_DDS)
    # Cyclone DDS代码生成
    foreach(IDL_FILE ${IDL_FILES})
        get_filename_component(IDL_NAME ${IDL_FILE} NAME_WE)
        
        # 生成的文件
        set(GENERATED_CPP "${CMAKE_CURRENT_BINARY_DIR}/${IDL_NAME}.cpp")
        set(GENERATED_HPP "${CMAKE_CURRENT_BINARY_DIR}/${IDL_NAME}.hpp")
        
        # 添加到生成文件列表
        list(APPEND GENERATED_SOURCES ${GENERATED_CPP})
        list(APPEND GENERATED_HEADERS ${GENERATED_HPP})
        
        # 自定义命令生成代码
        add_custom_command(
            OUTPUT ${GENERATED_CPP} ${GENERATED_HPP}
            COMMAND idlc -l cxx11 -d ${CMAKE_CURRENT_BINARY_DIR} ${CMAKE_CURRENT_SOURCE_DIR}/${IDL_FILE}
            DEPENDS ${CMAKE_CURRENT_SOURCE_DIR}/${IDL_FILE}
            COMMENT "生成Cyclone DDS代码: ${IDL_FILE}"
        )
    endforeach()
endif()

# 创建DDS通信库
add_library(autodriving_dds STATIC
    ${GENERATED_SOURCES}
    ${GENERATED_HEADERS}
)

# 设置库属性
target_include_directories(autodriving_dds PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${CMAKE_CURRENT_BINARY_DIR}
    ${DDS_INCLUDE_DIRS}
)

target_link_libraries(autodriving_dds PUBLIC
    ${DDS_LIBRARIES}
    Threads::Threads
)

# 设置编译属性
target_compile_features(autodriving_dds PUBLIC cxx_std_17)

# 安装规则
install(TARGETS autodriving_dds
    ARCHIVE DESTINATION lib
    LIBRARY DESTINATION lib
    RUNTIME DESTINATION bin
)

install(FILES ${GENERATED_HEADERS}
    DESTINATION include/autodriving_dds
)

install(FILES 
    types.idl
    autodriving.idl
    dds_config.xml
    DESTINATION share/autodriving_dds
)

# 构建测试（可选）
option(BUILD_TESTS "构建DDS通信测试" ON)

if(BUILD_TESTS)
    enable_testing()
    
    # DDS通信测试
    add_executable(test_dds_communication
        test_dds_communication.cpp
    )
    
    target_link_libraries(test_dds_communication
        autodriving_dds
        ${GTEST_LIBRARIES}
        Threads::Threads
    )
    
    # 添加测试
    add_test(NAME DDSCommunicationTest COMMAND test_dds_communication)
    
    # 设置测试环境变量
    if(USE_RTI_DDS)
        set_tests_properties(DDSCommunicationTest PROPERTIES
            ENVIRONMENT "NDDS_QOS_PROFILES=${CMAKE_CURRENT_SOURCE_DIR}/dds_config.xml"
        )
    elseif(USE_CYCLONE_DDS)
        set_tests_properties(DDSCommunicationTest PROPERTIES
            ENVIRONMENT "CYCLONEDX_URI=file://${CMAKE_CURRENT_SOURCE_DIR}/dds_config.xml"
        )
    endif()
    
    # 性能测试
    add_executable(dds_performance_test
        test_dds_communication.cpp
    )
    
    target_compile_definitions(dds_performance_test PRIVATE
        PERFORMANCE_TEST_ONLY
    )
    
    target_link_libraries(dds_performance_test
        autodriving_dds
        ${GTEST_LIBRARIES}
        Threads::Threads
    )
    
    # 基准测试
    add_executable(dds_benchmark
        test_dds_communication.cpp
    )
    
    target_compile_definitions(dds_benchmark PRIVATE
        BENCHMARK_MODE
    )
    
    target_link_libraries(dds_benchmark
        autodriving_dds
        Threads::Threads
    )
endif()

# 文档生成（可选）
option(BUILD_DOCS "生成DDS文档" OFF)

if(BUILD_DOCS)
    find_package(Doxygen)
    if(DOXYGEN_FOUND)
        set(DOXYGEN_IN ${CMAKE_CURRENT_SOURCE_DIR}/Doxyfile.in)
        set(DOXYGEN_OUT ${CMAKE_CURRENT_BINARY_DIR}/Doxyfile)
        
        configure_file(${DOXYGEN_IN} ${DOXYGEN_OUT} @ONLY)
        
        add_custom_target(dds_docs ALL
            COMMAND ${DOXYGEN_EXECUTABLE} ${DOXYGEN_OUT}
            WORKING_DIRECTORY ${CMAKE_CURRENT_BINARY_DIR}
            COMMENT "生成DDS API文档"
            VERBATIM
        )
    else()
        message(WARNING "未找到Doxygen，无法生成文档")
    endif()
endif()

# 打印配置信息
message(STATUS "=== DDS通信模块配置 ===")
message(STATUS "DDS实现: ${DDS_IMPLEMENTATION}")
message(STATUS "DDS库: ${DDS_LIBRARIES}")
message(STATUS "构建测试: ${BUILD_TESTS}")
message(STATUS "构建文档: ${BUILD_DOCS}")
message(STATUS "========================")
