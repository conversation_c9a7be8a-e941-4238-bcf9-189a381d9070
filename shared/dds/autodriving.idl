/**
 * 自动驾驶开发加速系统 DDS 数据类型定义
 * 
 * 本文件定义了系统中使用的所有DDS数据类型和主题结构
 * 支持实时数据分发和跨服务通信
 */

module AutoDriving {
    
    // ============================================================================
    // 通用数据类型定义
    // ============================================================================
    
    module Common {
        /**
         * 时间戳结构体
         * 用于标记数据的时间信息
         */
        struct Timestamp {
            long sec;        // 秒数
            long nanosec;    // 纳秒数
        };
        
        /**
         * 三维向量结构体
         * 用于表示位置、速度、加速度等三维数据
         */
        struct Vector3D {
            double x;        // X轴坐标
            double y;        // Y轴坐标
            double z;        // Z轴坐标
        };
        
        /**
         * 四元数结构体
         * 用于表示旋转信息
         */
        struct Quaternion {
            double x;        // X分量
            double y;        // Y分量
            double z;        // Z分量
            double w;        // W分量
        };
        
        /**
         * 通用状态枚举
         */
        enum Status {
            UNKNOWN,         // 未知状态
            INITIALIZING,    // 初始化中
            RUNNING,         // 运行中
            PAUSED,          // 暂停
            STOPPED,         // 已停止
            ERROR            // 错误状态
        };
    };
    
    // ============================================================================
    // 系统管理相关数据类型
    // ============================================================================
    
    module SystemManagement {
        /**
         * 服务状态信息
         * 用于服务注册和健康检查
         */
        struct ServiceStatus {
            string service_name;                    // 服务名称
            string service_id;                      // 服务唯一标识
            Common::Status status;                  // 服务状态
            Common::Timestamp timestamp;           // 状态更新时间
            map<string, string> metrics;           // 性能指标
            string endpoint;                        // 服务端点
            long uptime;                           // 运行时间（秒）
        };
        
        /**
         * 用户认证信息
         */
        struct UserAuth {
            string user_id;                        // 用户ID
            string username;                       // 用户名
            sequence<string> roles;                // 用户角色
            sequence<string> permissions;          // 用户权限
            Common::Timestamp login_time;          // 登录时间
            string session_id;                     // 会话ID
        };
        
        /**
         * 系统配置变更事件
         */
        struct ConfigChangeEvent {
            string config_key;                     // 配置键
            string old_value;                      // 旧值
            string new_value;                      // 新值
            string changed_by;                     // 变更人
            Common::Timestamp change_time;         // 变更时间
            string reason;                         // 变更原因
        };
    };
    
    // ============================================================================
    // 开发工具相关数据类型
    // ============================================================================
    
    module Development {
        /**
         * 代码生成请求
         */
        struct CodeGenerationRequest {
            string request_id;                     // 请求ID
            string template_id;                    // 模板ID
            string project_name;                   // 项目名称
            map<string, string> parameters;       // 模板参数
            string user_id;                        // 请求用户
            Common::Timestamp request_time;       // 请求时间
        };
        
        /**
         * 代码生成响应
         */
        struct CodeGenerationResponse {
            string request_id;                     // 对应的请求ID
            Common::Status status;                 // 生成状态
            string project_path;                   // 生成的项目路径
            string download_url;                   // 下载链接
            sequence<string> generated_files;     // 生成的文件列表
            string error_message;                  // 错误信息（如果有）
            Common::Timestamp completion_time;    // 完成时间
        };
        
        /**
         * 项目事件
         */
        struct ProjectEvent {
            string project_id;                     // 项目ID
            string event_type;                     // 事件类型：created, updated, deleted, built
            string user_id;                        // 操作用户
            Common::Timestamp timestamp;          // 事件时间
            string details;                        // 事件详情
            map<string, string> metadata;         // 元数据
        };
        
        /**
         * 构建状态
         */
        struct BuildStatus {
            string project_id;                     // 项目ID
            string build_id;                       // 构建ID
            Common::Status status;                 // 构建状态
            long progress;                         // 构建进度（0-100）
            sequence<string> logs;                 // 构建日志
            Common::Timestamp start_time;         // 开始时间
            Common::Timestamp end_time;           // 结束时间
        };
    };
    
    // ============================================================================
    // 仿真集成相关数据类型
    // ============================================================================
    
    module Simulation {
        /**
         * 仿真器类型枚举
         */
        enum SimulatorType {
            CARLA,           // CARLA仿真器
            AIRSIM,          // AirSim仿真器
            SUMO,            // SUMO交通仿真器
            GAZEBO,          // Gazebo机器人仿真器
            CUSTOM           // 自定义仿真器
        };
        
        /**
         * 传感器数据类型枚举
         */
        enum SensorType {
            CAMERA,          // 摄像头
            LIDAR,           // 激光雷达
            RADAR,           // 毫米波雷达
            IMU,             // 惯性测量单元
            GPS,             // GPS定位
            ULTRASONIC       // 超声波传感器
        };
        
        /**
         * 传感器数据
         */
        struct SensorData {
            string sensor_id;                      // 传感器ID
            SensorType sensor_type;                // 传感器类型
            sequence<octet> data;                  // 传感器数据（二进制）
            Common::Timestamp timestamp;          // 数据时间戳
            map<string, string> metadata;         // 元数据（分辨率、频率等）
        };
        
        /**
         * 车辆状态信息
         */
        struct VehicleState {
            string vehicle_id;                     // 车辆ID
            Common::Vector3D position;             // 位置信息
            Common::Vector3D velocity;             // 速度信息
            Common::Vector3D acceleration;         // 加速度信息
            Common::Quaternion orientation;        // 姿态信息
            double steering_angle;                 // 方向盘转角
            double throttle;                       // 油门踏板
            double brake;                          // 刹车踏板
            Common::Timestamp timestamp;          // 状态时间戳
        };
        
        /**
         * 仿真命令
         */
        struct SimulationCommand {
            string session_id;                     // 仿真会话ID
            SimulatorType simulator_type;          // 仿真器类型
            string command_type;                   // 命令类型：start, stop, pause, resume, reset
            string scenario_id;                    // 场景ID
            map<string, string> parameters;       // 命令参数
            string user_id;                        // 操作用户
            Common::Timestamp timestamp;          // 命令时间戳
        };
        
        /**
         * 仿真状态
         */
        struct SimulationStatus {
            string session_id;                     // 仿真会话ID
            SimulatorType simulator_type;          // 仿真器类型
            Common::Status status;                 // 仿真状态
            string scenario_id;                    // 当前场景ID
            double simulation_time;                // 仿真时间
            double real_time_factor;               // 实时因子
            map<string, double> performance_metrics; // 性能指标
            Common::Timestamp timestamp;          // 状态时间戳
        };
        
        /**
         * 场景信息
         */
        struct ScenarioInfo {
            string scenario_id;                    // 场景ID
            string name;                           // 场景名称
            string description;                    // 场景描述
            SimulatorType simulator_type;          // 适用的仿真器类型
            string scenario_data;                  // 场景数据（JSON格式）
            sequence<string> tags;                 // 场景标签
            string created_by;                     // 创建者
            Common::Timestamp created_time;       // 创建时间
            Common::Timestamp updated_time;       // 更新时间
        };
    };
    
    // ============================================================================
    // 地图编辑相关数据类型
    // ============================================================================
    
    module MapEditor {
        /**
         * 地图元素类型枚举
         */
        enum ElementType {
            ROAD,            // 道路
            LANE,            // 车道
            JUNCTION,        // 路口
            TRAFFIC_LIGHT,   // 交通信号灯
            TRAFFIC_SIGN,    // 交通标志
            CROSSWALK,       // 人行横道
            PARKING_SPACE,   // 停车位
            BUILDING,        // 建筑物
            VEGETATION       // 植被
        };
        
        /**
         * 地图元素
         */
        struct MapElement {
            string element_id;                     // 元素ID
            ElementType element_type;              // 元素类型
            string geometry_data;                  // 几何数据（WKT格式）
            map<string, string> attributes;       // 元素属性
            sequence<string> connected_elements;  // 连接的元素ID列表
            Common::Timestamp created_time;       // 创建时间
            Common::Timestamp updated_time;       // 更新时间
        };
        
        /**
         * 编辑操作类型枚举
         */
        enum OperationType {
            ADD,             // 添加
            UPDATE,          // 更新
            DELETE,          // 删除
            MOVE,            // 移动
            COPY,            // 复制
            UNDO,            // 撤销
            REDO             // 重做
        };
        
        /**
         * 编辑操作
         */
        struct EditOperation {
            string operation_id;                   // 操作ID
            string map_id;                         // 地图ID
            OperationType operation_type;          // 操作类型
            MapElement element;                    // 操作的元素
            MapElement previous_element;           // 操作前的元素状态（用于撤销）
            string user_id;                        // 操作用户
            Common::Timestamp timestamp;          // 操作时间
            string description;                    // 操作描述
        };
        
        /**
         * 地图信息
         */
        struct MapInfo {
            string map_id;                         // 地图ID
            string name;                           // 地图名称
            string description;                    // 地图描述
            string format;                         // 地图格式：opendrive, lanelet2, osm
            Common::Vector3D bounds_min;           // 边界最小值
            Common::Vector3D bounds_max;           // 边界最大值
            long element_count;                    // 元素数量
            sequence<string> collaborators;       // 协作者列表
            string created_by;                     // 创建者
            Common::Timestamp created_time;       // 创建时间
            Common::Timestamp updated_time;       // 更新时间
            long version;                          // 版本号
        };
        
        /**
         * 协作状态
         */
        struct CollaborationStatus {
            string map_id;                         // 地图ID
            string user_id;                        // 用户ID
            string user_name;                      // 用户名称
            Common::Vector3D cursor_position;      // 光标位置
            sequence<string> selected_elements;   // 选中的元素
            string current_tool;                   // 当前使用的工具
            Common::Timestamp last_activity;      // 最后活动时间
            boolean is_online;                     // 是否在线
        };
    };
    
    // ============================================================================
    // 部署运维相关数据类型
    // ============================================================================
    
    module DevOps {
        /**
         * 部署环境枚举
         */
        enum Environment {
            DEVELOPMENT,     // 开发环境
            TESTING,         // 测试环境
            STAGING,         // 预发布环境
            PRODUCTION       // 生产环境
        };
        
        /**
         * 部署状态
         */
        struct DeploymentStatus {
            string deployment_id;                  // 部署ID
            string service_name;                   // 服务名称
            Environment environment;               // 部署环境
            string version;                        // 部署版本
            Common::Status status;                 // 部署状态
            long progress;                         // 部署进度（0-100）
            sequence<string> logs;                 // 部署日志
            string deployed_by;                    // 部署人员
            Common::Timestamp start_time;         // 开始时间
            Common::Timestamp end_time;           // 结束时间
        };
        
        /**
         * CI/CD流水线状态
         */
        struct PipelineStatus {
            string pipeline_id;                    // 流水线ID
            string project_id;                     // 项目ID
            string branch;                         // 分支名称
            string commit_hash;                    // 提交哈希
            Common::Status status;                 // 流水线状态
            sequence<string> stages;               // 阶段列表
            string current_stage;                  // 当前阶段
            map<string, Common::Status> stage_status; // 各阶段状态
            Common::Timestamp start_time;         // 开始时间
            Common::Timestamp end_time;           // 结束时间
        };
        
        /**
         * 环境配置
         */
        struct EnvironmentConfig {
            string config_id;                      // 配置ID
            Environment environment;               // 环境类型
            string service_name;                   // 服务名称
            map<string, string> config_data;      // 配置数据
            map<string, string> secrets;          // 敏感配置（加密）
            string updated_by;                     // 更新人员
            Common::Timestamp updated_time;       // 更新时间
            long version;                          // 配置版本
        };
    };
};
