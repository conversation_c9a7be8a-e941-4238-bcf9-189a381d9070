/**
 * 自动驾驶开发加速系统 - DDS数据类型定义
 * 
 * 定义系统中各个服务间通信使用的数据结构
 * 使用OMG IDL (Interface Definition Language) 语法
 */

module AutonomousDriving {
    
    // 基础数据类型
    
    /**
     * 时间戳结构
     */
    struct Timestamp {
        long long seconds;      // 秒数 (Unix时间戳)
        long nanoseconds;       // 纳秒数
    };
    
    /**
     * 三维坐标点
     */
    struct Point3D {
        double x;               // X坐标
        double y;               // Y坐标  
        double z;               // Z坐标
    };
    
    /**
     * 四元数表示的旋转
     */
    struct Quaternion {
        double x;
        double y;
        double z;
        double w;
    };
    
    /**
     * 位姿信息 (位置 + 姿态)
     */
    struct Pose {
        Point3D position;       // 位置
        Quaternion orientation; // 姿态
    };
    
    // 系统管理相关数据类型
    
    /**
     * 服务状态枚举
     */
    enum ServiceStatus {
        SERVICE_UNKNOWN,        // 未知状态
        SERVICE_STARTING,       // 启动中
        SERVICE_RUNNING,        // 运行中
        SERVICE_STOPPING,       // 停止中
        SERVICE_STOPPED,        // 已停止
        SERVICE_ERROR          // 错误状态
    };
    
    /**
     * 服务信息
     */
    struct ServiceInfo {
        string service_id;      // 服务唯一标识
        string service_name;    // 服务名称
        string service_type;    // 服务类型
        string version;         // 服务版本
        string host;            // 主机地址
        long port;              // 端口号
        ServiceStatus status;   // 服务状态
        Timestamp last_heartbeat; // 最后心跳时间
        sequence<string> endpoints; // 服务端点列表
    };
    
    /**
     * 心跳消息
     */
    struct HeartbeatMessage {
        string service_id;      // 服务ID
        Timestamp timestamp;    // 时间戳
        ServiceStatus status;   // 当前状态
        double cpu_usage;       // CPU使用率 (0-100)
        double memory_usage;    // 内存使用率 (0-100)
        long active_connections; // 活跃连接数
    };
    
    // 仿真相关数据类型
    
    /**
     * 传感器类型枚举
     */
    enum SensorType {
        SENSOR_CAMERA,          // 摄像头
        SENSOR_LIDAR,           // 激光雷达
        SENSOR_RADAR,           // 毫米波雷达
        SENSOR_IMU,             // 惯性测量单元
        SENSOR_GPS,             // GPS
        SENSOR_ULTRASONIC      // 超声波传感器
    };
    
    /**
     * 传感器数据头部
     */
    struct SensorHeader {
        string sensor_id;       // 传感器ID
        SensorType sensor_type; // 传感器类型
        Timestamp timestamp;    // 数据时间戳
        long sequence_number;   // 序列号
        string frame_id;        // 坐标系ID
    };
    
    /**
     * 图像数据
     */
    struct ImageData {
        SensorHeader header;    // 数据头部
        long width;             // 图像宽度
        long height;            // 图像高度
        string encoding;        // 编码格式 (rgb8, bgr8, mono8等)
        sequence<octet> data;   // 图像数据
    };
    
    /**
     * 点云数据点
     */
    struct PointCloudPoint {
        float x, y, z;          // 3D坐标
        float intensity;        // 强度值
        octet ring;             // 激光环号
    };
    
    /**
     * 点云数据
     */
    struct PointCloudData {
        SensorHeader header;    // 数据头部
        sequence<PointCloudPoint> points; // 点云数据
    };
    
    /**
     * 车辆状态
     */
    struct VehicleState {
        Timestamp timestamp;    // 时间戳
        Pose pose;              // 车辆位姿
        double velocity;        // 速度 (m/s)
        double acceleration;    // 加速度 (m/s²)
        double steering_angle;  // 转向角 (弧度)
        double throttle;        // 油门 (0-1)
        double brake;           // 刹车 (0-1)
    };
    
    // 地图编辑相关数据类型
    
    /**
     * 地图操作类型
     */
    enum MapOperationType {
        MAP_OP_CREATE,          // 创建
        MAP_OP_UPDATE,          // 更新
        MAP_OP_DELETE,          // 删除
        MAP_OP_MOVE            // 移动
    };
    
    /**
     * 地图元素类型
     */
    enum MapElementType {
        ELEMENT_ROAD,           // 道路
        ELEMENT_LANE,           // 车道
        ELEMENT_JUNCTION,       // 交叉口
        ELEMENT_TRAFFIC_SIGN,   // 交通标志
        ELEMENT_TRAFFIC_LIGHT  // 交通信号灯
    };
    
    /**
     * 地图编辑操作
     */
    struct MapEditOperation {
        string operation_id;    // 操作ID
        string user_id;         // 用户ID
        string map_id;          // 地图ID
        MapOperationType operation_type; // 操作类型
        MapElementType element_type;     // 元素类型
        string element_id;      // 元素ID
        Timestamp timestamp;    // 操作时间
        string data;            // 操作数据 (JSON格式)
    };
    
    /**
     * 地图同步状态
     */
    struct MapSyncStatus {
        string map_id;          // 地图ID
        string version;         // 版本号
        Timestamp last_modified; // 最后修改时间
        sequence<string> active_editors; // 活跃编辑者列表
        boolean is_locked;      // 是否被锁定
        string lock_owner;      // 锁定者
    };
    
    // 开发工具相关数据类型
    
    /**
     * 构建状态枚举
     */
    enum BuildStatus {
        BUILD_PENDING,          // 等待中
        BUILD_RUNNING,          // 构建中
        BUILD_SUCCESS,          // 构建成功
        BUILD_FAILED,           // 构建失败
        BUILD_CANCELLED        // 已取消
    };
    
    /**
     * 项目构建信息
     */
    struct ProjectBuildInfo {
        string project_id;      // 项目ID
        string build_id;        // 构建ID
        BuildStatus status;     // 构建状态
        Timestamp start_time;   // 开始时间
        Timestamp end_time;     // 结束时间
        string log_url;         // 日志URL
        sequence<string> artifacts; // 构建产物列表
    };
    
    /**
     * 代码生成请求
     */
    struct CodeGenerationRequest {
        string request_id;      // 请求ID
        string template_id;     // 模板ID
        string project_name;    // 项目名称
        string target_language; // 目标语言
        sequence<string> parameters; // 参数列表 (key=value格式)
        string output_path;     // 输出路径
    };
    
    /**
     * 代码生成响应
     */
    struct CodeGenerationResponse {
        string request_id;      // 请求ID
        boolean success;        // 是否成功
        string message;         // 响应消息
        sequence<string> generated_files; // 生成的文件列表
        string download_url;    // 下载URL
    };
    
    // 系统监控相关数据类型
    
    /**
     * 系统指标
     */
    struct SystemMetrics {
        Timestamp timestamp;    // 时间戳
        string node_id;         // 节点ID
        double cpu_usage;       // CPU使用率
        double memory_usage;    // 内存使用率
        double disk_usage;      // 磁盘使用率
        double network_in;      // 网络入流量 (bytes/s)
        double network_out;     // 网络出流量 (bytes/s)
        long process_count;     // 进程数量
        double load_average;    // 系统负载
    };
    
    /**
     * 告警级别
     */
    enum AlertLevel {
        ALERT_INFO,             // 信息
        ALERT_WARNING,          // 警告
        ALERT_ERROR,            // 错误
        ALERT_CRITICAL         // 严重
    };
    
    /**
     * 系统告警
     */
    struct SystemAlert {
        string alert_id;        // 告警ID
        AlertLevel level;       // 告警级别
        string source;          // 告警源
        string message;         // 告警消息
        Timestamp timestamp;    // 告警时间
        boolean resolved;       // 是否已解决
        string resolution_note; // 解决说明
    };
    
}; // module AutonomousDriving
