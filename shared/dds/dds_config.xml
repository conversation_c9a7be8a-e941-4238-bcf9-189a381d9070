<?xml version="1.0" encoding="UTF-8"?>
<!--
    自动驾驶开发加速系统 DDS 配置文件
    
    本文件定义了DDS域、QoS策略和传输配置
    支持高性能实时数据分发和可靠通信
-->
<dds xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
     xsi:noNamespaceSchemaLocation="http://community.rti.com/schema/6.1.0/rti_dds_profiles.xsd">

    <!-- ================================================================ -->
    <!-- DDS域配置 -->
    <!-- ================================================================ -->
    <domain_library name="AutoDrivingDomains">
        <!-- 系统管理域 -->
        <domain name="SystemManagement" domain_id="0">
            <register_type name="ServiceStatus" type_ref="AutoDriving::SystemManagement::ServiceStatus"/>
            <register_type name="UserAuth" type_ref="AutoDriving::SystemManagement::UserAuth"/>
            <register_type name="ConfigChangeEvent" type_ref="AutoDriving::SystemManagement::ConfigChangeEvent"/>
            
            <topic name="ServiceStatusTopic" register_type_ref="ServiceStatus"/>
            <topic name="UserAuthTopic" register_type_ref="UserAuth"/>
            <topic name="ConfigChangeEventTopic" register_type_ref="ConfigChangeEvent"/>
        </domain>
        
        <!-- 开发工具域 -->
        <domain name="DevelopmentTools" domain_id="1">
            <register_type name="CodeGenerationRequest" type_ref="AutoDriving::Development::CodeGenerationRequest"/>
            <register_type name="CodeGenerationResponse" type_ref="AutoDriving::Development::CodeGenerationResponse"/>
            <register_type name="ProjectEvent" type_ref="AutoDriving::Development::ProjectEvent"/>
            <register_type name="BuildStatus" type_ref="AutoDriving::Development::BuildStatus"/>
            
            <topic name="CodeGenerationRequestTopic" register_type_ref="CodeGenerationRequest"/>
            <topic name="CodeGenerationResponseTopic" register_type_ref="CodeGenerationResponse"/>
            <topic name="ProjectEventTopic" register_type_ref="ProjectEvent"/>
            <topic name="BuildStatusTopic" register_type_ref="BuildStatus"/>
        </domain>
        
        <!-- 仿真测试域 -->
        <domain name="SimulationTesting" domain_id="2">
            <register_type name="SensorData" type_ref="AutoDriving::Simulation::SensorData"/>
            <register_type name="VehicleState" type_ref="AutoDriving::Simulation::VehicleState"/>
            <register_type name="SimulationCommand" type_ref="AutoDriving::Simulation::SimulationCommand"/>
            <register_type name="SimulationStatus" type_ref="AutoDriving::Simulation::SimulationStatus"/>
            <register_type name="ScenarioInfo" type_ref="AutoDriving::Simulation::ScenarioInfo"/>
            
            <topic name="SensorDataTopic" register_type_ref="SensorData"/>
            <topic name="VehicleStateTopic" register_type_ref="VehicleState"/>
            <topic name="SimulationCommandTopic" register_type_ref="SimulationCommand"/>
            <topic name="SimulationStatusTopic" register_type_ref="SimulationStatus"/>
            <topic name="ScenarioInfoTopic" register_type_ref="ScenarioInfo"/>
        </domain>
        
        <!-- 地图编辑域 -->
        <domain name="MapEditing" domain_id="3">
            <register_type name="MapElement" type_ref="AutoDriving::MapEditor::MapElement"/>
            <register_type name="EditOperation" type_ref="AutoDriving::MapEditor::EditOperation"/>
            <register_type name="MapInfo" type_ref="AutoDriving::MapEditor::MapInfo"/>
            <register_type name="CollaborationStatus" type_ref="AutoDriving::MapEditor::CollaborationStatus"/>
            
            <topic name="MapElementTopic" register_type_ref="MapElement"/>
            <topic name="EditOperationTopic" register_type_ref="EditOperation"/>
            <topic name="MapInfoTopic" register_type_ref="MapInfo"/>
            <topic name="CollaborationStatusTopic" register_type_ref="CollaborationStatus"/>
        </domain>
        
        <!-- 部署运维域 -->
        <domain name="DevOps" domain_id="4">
            <register_type name="DeploymentStatus" type_ref="AutoDriving::DevOps::DeploymentStatus"/>
            <register_type name="PipelineStatus" type_ref="AutoDriving::DevOps::PipelineStatus"/>
            <register_type name="EnvironmentConfig" type_ref="AutoDriving::DevOps::EnvironmentConfig"/>
            
            <topic name="DeploymentStatusTopic" register_type_ref="DeploymentStatus"/>
            <topic name="PipelineStatusTopic" register_type_ref="PipelineStatus"/>
            <topic name="EnvironmentConfigTopic" register_type_ref="EnvironmentConfig"/>
        </domain>
    </domain_library>

    <!-- ================================================================ -->
    <!-- QoS策略配置 -->
    <!-- ================================================================ -->
    <qos_library name="AutoDrivingQoSLibrary">
        
        <!-- 实时数据传输QoS（用于传感器数据等高频数据） -->
        <qos_profile name="RealTimeProfile" base_name="BuiltinQosLib::Generic.BestEffort">
            <datawriter_qos>
                <reliability>
                    <kind>BEST_EFFORT_RELIABILITY_QOS</kind>
                </reliability>
                <history>
                    <kind>KEEP_LAST_HISTORY_QOS</kind>
                    <depth>1</depth>
                </history>
                <deadline>
                    <period>
                        <sec>0</sec>
                        <nanosec>100000000</nanosec> <!-- 100ms -->
                    </period>
                </deadline>
                <resource_limits>
                    <max_samples>10</max_samples>
                    <max_instances>100</max_instances>
                    <max_samples_per_instance>1</max_samples_per_instance>
                </resource_limits>
            </datawriter_qos>
            <datareader_qos>
                <reliability>
                    <kind>BEST_EFFORT_RELIABILITY_QOS</kind>
                </reliability>
                <history>
                    <kind>KEEP_LAST_HISTORY_QOS</kind>
                    <depth>1</depth>
                </history>
                <deadline>
                    <period>
                        <sec>0</sec>
                        <nanosec>100000000</nanosec> <!-- 100ms -->
                    </period>
                </deadline>
            </datareader_qos>
        </qos_profile>
        
        <!-- 可靠数据传输QoS（用于配置数据、命令等重要数据） -->
        <qos_profile name="ReliableProfile" base_name="BuiltinQosLib::Generic.Reliable">
            <datawriter_qos>
                <reliability>
                    <kind>RELIABLE_RELIABILITY_QOS</kind>
                    <max_blocking_time>
                        <sec>1</sec>
                        <nanosec>0</nanosec>
                    </max_blocking_time>
                </reliability>
                <history>
                    <kind>KEEP_ALL_HISTORY_QOS</kind>
                </history>
                <durability>
                    <kind>TRANSIENT_LOCAL_DURABILITY_QOS</kind>
                </durability>
                <resource_limits>
                    <max_samples>1000</max_samples>
                    <max_instances>100</max_instances>
                    <max_samples_per_instance>10</max_samples_per_instance>
                </resource_limits>
            </datawriter_qos>
            <datareader_qos>
                <reliability>
                    <kind>RELIABLE_RELIABILITY_QOS</kind>
                </reliability>
                <history>
                    <kind>KEEP_ALL_HISTORY_QOS</kind>
                </history>
                <durability>
                    <kind>TRANSIENT_LOCAL_DURABILITY_QOS</kind>
                </durability>
            </datareader_qos>
        </qos_profile>
        
        <!-- 持久化数据QoS（用于系统配置等需要持久化的数据） -->
        <qos_profile name="PersistentProfile" base_name="BuiltinQosLib::Generic.Reliable">
            <datawriter_qos>
                <reliability>
                    <kind>RELIABLE_RELIABILITY_QOS</kind>
                </reliability>
                <history>
                    <kind>KEEP_ALL_HISTORY_QOS</kind>
                </history>
                <durability>
                    <kind>PERSISTENT_DURABILITY_QOS</kind>
                </durability>
                <liveliness>
                    <kind>AUTOMATIC_LIVELINESS_QOS</kind>
                    <lease_duration>
                        <sec>30</sec>
                        <nanosec>0</nanosec>
                    </lease_duration>
                </liveliness>
            </datawriter_qos>
            <datareader_qos>
                <reliability>
                    <kind>RELIABLE_RELIABILITY_QOS</kind>
                </reliability>
                <history>
                    <kind>KEEP_ALL_HISTORY_QOS</kind>
                </history>
                <durability>
                    <kind>PERSISTENT_DURABILITY_QOS</kind>
                </durability>
            </datareader_qos>
        </qos_profile>
        
        <!-- 协作编辑QoS（用于地图编辑等需要实时协作的场景） -->
        <qos_profile name="CollaborativeProfile" base_name="BuiltinQosLib::Generic.Reliable">
            <datawriter_qos>
                <reliability>
                    <kind>RELIABLE_RELIABILITY_QOS</kind>
                </reliability>
                <history>
                    <kind>KEEP_LAST_HISTORY_QOS</kind>
                    <depth>10</depth>
                </history>
                <deadline>
                    <period>
                        <sec>0</sec>
                        <nanosec>500000000</nanosec> <!-- 500ms -->
                    </period>
                </deadline>
                <liveliness>
                    <kind>AUTOMATIC_LIVELINESS_QOS</kind>
                    <lease_duration>
                        <sec>5</sec>
                        <nanosec>0</nanosec>
                    </lease_duration>
                </liveliness>
            </datawriter_qos>
            <datareader_qos>
                <reliability>
                    <kind>RELIABLE_RELIABILITY_QOS</kind>
                </reliability>
                <history>
                    <kind>KEEP_LAST_HISTORY_QOS</kind>
                    <depth>10</depth>
                </history>
                <deadline>
                    <period>
                        <sec>0</sec>
                        <nanosec>500000000</nanosec> <!-- 500ms -->
                    </period>
                </deadline>
            </datareader_qos>
        </qos_profile>
    </qos_library>

    <!-- ================================================================ -->
    <!-- 参与者配置 -->
    <!-- ================================================================ -->
    <domain_participant_library name="AutoDrivingParticipants">
        
        <!-- 系统管理服务参与者 -->
        <domain_participant name="SystemManagementParticipant" domain_ref="AutoDrivingDomains::SystemManagement">
            <participant_qos>
                <user_data>
                    <value>SystemManagementService</value>
                </user_data>
                <entity_factory>
                    <autoenable_created_entities>true</autoenable_created_entities>
                </entity_factory>
            </participant_qos>
            
            <!-- 服务状态发布者 -->
            <publisher name="ServiceStatusPublisher">
                <data_writer name="ServiceStatusWriter" topic_ref="ServiceStatusTopic">
                    <datawriter_qos base_name="AutoDrivingQoSLibrary::ReliableProfile"/>
                </data_writer>
            </publisher>
            
            <!-- 配置变更订阅者 -->
            <subscriber name="ConfigChangeSubscriber">
                <data_reader name="ConfigChangeReader" topic_ref="ConfigChangeEventTopic">
                    <datareader_qos base_name="AutoDrivingQoSLibrary::PersistentProfile"/>
                </data_reader>
            </subscriber>
        </domain_participant>
        
        <!-- 仿真服务参与者 -->
        <domain_participant name="SimulationParticipant" domain_ref="AutoDrivingDomains::SimulationTesting">
            <participant_qos>
                <user_data>
                    <value>SimulationService</value>
                </user_data>
            </participant_qos>
            
            <!-- 传感器数据发布者 -->
            <publisher name="SensorDataPublisher">
                <data_writer name="SensorDataWriter" topic_ref="SensorDataTopic">
                    <datawriter_qos base_name="AutoDrivingQoSLibrary::RealTimeProfile"/>
                </data_writer>
            </publisher>
            
            <!-- 车辆状态发布者 -->
            <publisher name="VehicleStatePublisher">
                <data_writer name="VehicleStateWriter" topic_ref="VehicleStateTopic">
                    <datawriter_qos base_name="AutoDrivingQoSLibrary::RealTimeProfile"/>
                </data_writer>
            </publisher>
            
            <!-- 仿真命令订阅者 -->
            <subscriber name="SimulationCommandSubscriber">
                <data_reader name="SimulationCommandReader" topic_ref="SimulationCommandTopic">
                    <datareader_qos base_name="AutoDrivingQoSLibrary::ReliableProfile"/>
                </data_reader>
            </subscriber>
        </domain_participant>
        
        <!-- 地图编辑参与者 -->
        <domain_participant name="MapEditorParticipant" domain_ref="AutoDrivingDomains::MapEditing">
            <participant_qos>
                <user_data>
                    <value>MapEditorService</value>
                </user_data>
            </participant_qos>
            
            <!-- 编辑操作发布者/订阅者（协作编辑） -->
            <publisher name="EditOperationPublisher">
                <data_writer name="EditOperationWriter" topic_ref="EditOperationTopic">
                    <datawriter_qos base_name="AutoDrivingQoSLibrary::CollaborativeProfile"/>
                </data_writer>
            </publisher>
            
            <subscriber name="EditOperationSubscriber">
                <data_reader name="EditOperationReader" topic_ref="EditOperationTopic">
                    <datareader_qos base_name="AutoDrivingQoSLibrary::CollaborativeProfile"/>
                </data_reader>
            </subscriber>
            
            <!-- 协作状态发布者 -->
            <publisher name="CollaborationStatusPublisher">
                <data_writer name="CollaborationStatusWriter" topic_ref="CollaborationStatusTopic">
                    <datawriter_qos base_name="AutoDrivingQoSLibrary::RealTimeProfile"/>
                </data_writer>
            </publisher>
        </domain_participant>
    </domain_participant_library>

    <!-- ================================================================ -->
    <!-- 传输配置 -->
    <!-- ================================================================ -->
    <participant_factory_qos>
        <profile>
            <participant_factory_qos>
                <entity_factory>
                    <autoenable_created_entities>true</autoenable_created_entities>
                </entity_factory>
            </participant_factory_qos>
        </profile>
    </participant_factory_qos>

</dds>
