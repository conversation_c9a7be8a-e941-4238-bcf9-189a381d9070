// 自动驾驶开发加速系统 - 通用gRPC定义
// 定义各个微服务间通信使用的通用数据结构和接口

syntax = "proto3";

package autodriving.common;

option go_package = "autodriving/shared/proto/common";

import "google/protobuf/timestamp.proto";
import "google/protobuf/any.proto";

// ================================================================
// 通用数据类型
// ================================================================

// 响应状态
message Status {
  int32 code = 1;                    // 状态码
  string message = 2;                // 状态消息
  repeated string details = 3;       // 详细信息
}

// 分页请求
message PageRequest {
  int32 page = 1;                    // 页码（从1开始）
  int32 page_size = 2;               // 每页大小
  string sort_by = 3;                // 排序字段
  string sort_order = 4;             // 排序方向（asc/desc）
}

// 分页响应
message PageResponse {
  int32 total = 1;                   // 总记录数
  int32 page = 2;                    // 当前页码
  int32 page_size = 3;               // 每页大小
  int32 total_pages = 4;             // 总页数
}

// 时间范围
message TimeRange {
  google.protobuf.Timestamp start = 1;  // 开始时间
  google.protobuf.Timestamp end = 2;    // 结束时间
}

// 键值对
message KeyValue {
  string key = 1;                    // 键
  string value = 2;                  // 值
}

// 标签
message Label {
  string key = 1;                    // 标签键
  string value = 2;                  // 标签值
}

// ================================================================
// 用户相关
// ================================================================

// 用户信息
message UserInfo {
  string id = 1;                     // 用户ID
  string username = 2;               // 用户名
  string email = 3;                  // 邮箱
  string full_name = 4;              // 全名
  string role = 5;                   // 角色
  string status = 6;                 // 状态
  google.protobuf.Timestamp created_at = 7;  // 创建时间
  google.protobuf.Timestamp updated_at = 8;  // 更新时间
}

// 认证上下文
message AuthContext {
  string user_id = 1;                // 用户ID
  string username = 2;               // 用户名
  string role = 3;                   // 角色
  repeated string permissions = 4;    // 权限列表
  string session_id = 5;             // 会话ID
}

// ================================================================
// 服务发现相关
// ================================================================

// 服务信息
message ServiceInfo {
  string id = 1;                     // 服务ID
  string name = 2;                   // 服务名称
  string type = 3;                   // 服务类型
  string version = 4;                // 版本
  string host = 5;                   // 主机地址
  int32 port = 6;                    // 端口
  string status = 7;                 // 状态
  repeated string endpoints = 8;      // 端点列表
  map<string, string> metadata = 9;  // 元数据
  google.protobuf.Timestamp registered_at = 10;    // 注册时间
  google.protobuf.Timestamp last_heartbeat = 11;   // 最后心跳时间
}

// 健康检查状态
message HealthStatus {
  string service_id = 1;             // 服务ID
  string status = 2;                 // 健康状态（healthy/unhealthy/unknown）
  string message = 3;                // 状态消息
  int64 response_time_ms = 4;        // 响应时间（毫秒）
  google.protobuf.Timestamp checked_at = 5;  // 检查时间
}

// ================================================================
// 项目管理相关
// ================================================================

// 项目信息
message ProjectInfo {
  string id = 1;                     // 项目ID
  string name = 2;                   // 项目名称
  string description = 3;            // 项目描述
  string owner_id = 4;               // 所有者ID
  string status = 5;                 // 项目状态
  string repository_url = 6;         // 仓库URL
  string branch = 7;                 // 分支
  map<string, string> build_config = 8;  // 构建配置
  google.protobuf.Timestamp created_at = 9;   // 创建时间
  google.protobuf.Timestamp updated_at = 10;  // 更新时间
}

// 构建信息
message BuildInfo {
  string id = 1;                     // 构建ID
  string project_id = 2;             // 项目ID
  int32 build_number = 3;            // 构建号
  string commit_hash = 4;            // 提交哈希
  string branch = 5;                 // 分支
  string status = 6;                 // 构建状态
  string trigger_type = 7;           // 触发类型
  string triggered_by = 8;           // 触发者
  google.protobuf.Timestamp started_at = 9;   // 开始时间
  google.protobuf.Timestamp finished_at = 10; // 结束时间
  int32 duration_seconds = 11;       // 持续时间（秒）
  string log_url = 12;               // 日志URL
  repeated string artifacts = 13;     // 构建产物
}

// ================================================================
// 仿真相关
// ================================================================

// 仿真场景
message SimulationScenario {
  string id = 1;                     // 场景ID
  string name = 2;                   // 场景名称
  string description = 3;            // 场景描述
  string category = 4;               // 场景分类
  google.protobuf.Any scenario_data = 5;  // 场景数据
  map<string, string> parameters = 6;     // 参数配置
  string author_id = 7;              // 作者ID
  bool is_public = 8;                // 是否公开
  int32 usage_count = 9;             // 使用次数
  google.protobuf.Timestamp created_at = 10;  // 创建时间
  google.protobuf.Timestamp updated_at = 11;  // 更新时间
}

// 仿真任务
message SimulationTask {
  string id = 1;                     // 任务ID
  string project_id = 2;             // 项目ID
  string scenario_id = 3;            // 场景ID
  string name = 4;                   // 任务名称
  string status = 5;                 // 任务状态
  string simulator_type = 6;         // 仿真器类型
  google.protobuf.Any config = 7;    // 配置信息
  string started_by = 8;             // 启动者
  google.protobuf.Timestamp started_at = 9;   // 开始时间
  google.protobuf.Timestamp finished_at = 10; // 结束时间
  int32 duration_seconds = 11;       // 持续时间
  google.protobuf.Any result_data = 12;       // 结果数据
  google.protobuf.Any metrics = 13;           // 指标数据
  string log_url = 14;               // 日志URL
}

// ================================================================
// 地图相关
// ================================================================

// 地图信息
message MapInfo {
  string id = 1;                     // 地图ID
  string name = 2;                   // 地图名称
  string description = 3;            // 地图描述
  string format = 4;                 // 地图格式
  string version = 5;                // 版本
  string file_path = 6;              // 文件路径
  int64 file_size = 7;               // 文件大小
  string checksum = 8;               // 校验和
  google.protobuf.Any metadata = 9;  // 元数据
  string author_id = 10;             // 作者ID
  bool is_public = 11;               // 是否公开
  int32 download_count = 12;         // 下载次数
  google.protobuf.Timestamp created_at = 13;  // 创建时间
  google.protobuf.Timestamp updated_at = 14;  // 更新时间
}

// 地图编辑操作
message MapEditOperation {
  string id = 1;                     // 操作ID
  string session_id = 2;             // 会话ID
  string operation_type = 3;         // 操作类型
  string element_type = 4;           // 元素类型
  string element_id = 5;             // 元素ID
  google.protobuf.Any operation_data = 6;     // 操作数据
  google.protobuf.Timestamp timestamp = 7;   // 时间戳
}

// ================================================================
// 系统监控相关
// ================================================================

// 系统指标
message SystemMetric {
  string id = 1;                     // 指标ID
  string node_id = 2;                // 节点ID
  string metric_type = 3;            // 指标类型
  string metric_name = 4;            // 指标名称
  double metric_value = 5;           // 指标值
  string unit = 6;                   // 单位
  map<string, string> tags = 7;      // 标签
  google.protobuf.Timestamp timestamp = 8;   // 时间戳
}

// 系统事件
message SystemEvent {
  string id = 1;                     // 事件ID
  string event_type = 2;             // 事件类型
  string severity = 3;               // 严重程度
  string source = 4;                 // 事件源
  string message = 5;                // 事件消息
  google.protobuf.Any details = 6;   // 详细信息
  string user_id = 7;                // 用户ID
  bool resolved = 8;                 // 是否已解决
  string resolved_by = 9;            // 解决者
  google.protobuf.Timestamp resolved_at = 10;  // 解决时间
  google.protobuf.Timestamp created_at = 11;   // 创建时间
}

// ================================================================
// 通用服务接口
// ================================================================

// 健康检查服务
service HealthService {
  // 检查服务健康状态
  rpc Check(HealthCheckRequest) returns (HealthCheckResponse);
  
  // 监控服务健康状态
  rpc Watch(HealthCheckRequest) returns (stream HealthCheckResponse);
}

// 健康检查请求
message HealthCheckRequest {
  string service = 1;                // 服务名称
}

// 健康检查响应
message HealthCheckResponse {
  enum ServingStatus {
    UNKNOWN = 0;
    SERVING = 1;
    NOT_SERVING = 2;
    SERVICE_UNKNOWN = 3;
  }
  ServingStatus status = 1;          // 服务状态
  string message = 2;                // 状态消息
}

// ================================================================
// 错误定义
// ================================================================

// 错误代码
enum ErrorCode {
  OK = 0;                           // 成功
  CANCELLED = 1;                    // 取消
  UNKNOWN = 2;                      // 未知错误
  INVALID_ARGUMENT = 3;             // 无效参数
  DEADLINE_EXCEEDED = 4;            // 超时
  NOT_FOUND = 5;                    // 未找到
  ALREADY_EXISTS = 6;               // 已存在
  PERMISSION_DENIED = 7;            // 权限拒绝
  RESOURCE_EXHAUSTED = 8;           // 资源耗尽
  FAILED_PRECONDITION = 9;          // 前置条件失败
  ABORTED = 10;                     // 中止
  OUT_OF_RANGE = 11;                // 超出范围
  UNIMPLEMENTED = 12;               // 未实现
  INTERNAL = 13;                    // 内部错误
  UNAVAILABLE = 14;                 // 不可用
  DATA_LOSS = 15;                   // 数据丢失
  UNAUTHENTICATED = 16;             // 未认证
}

// 错误详情
message ErrorDetail {
  ErrorCode code = 1;               // 错误代码
  string message = 2;               // 错误消息
  string field = 3;                 // 错误字段
  google.protobuf.Any metadata = 4; // 错误元数据
}
