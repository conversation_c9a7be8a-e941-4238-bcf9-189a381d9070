# 自动驾驶开发加速系统 - 测试环境 Docker Compose 配置
version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: autonomous_driving_test
      POSTGRES_USER: test_user
      POSTGRES_PASSWORD: test_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_test_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U test_user -d autonomous_driving_test"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes
    volumes:
      - redis_test_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # 系统管理服务
  system-management:
    build:
      context: ./services/system-management
      dockerfile: Dockerfile
    environment:
      - DATABASE_URL=************************************************/autonomous_driving_test
      - REDIS_URL=redis://redis:6379
      - LOG_LEVEL=debug
      - ENVIRONMENT=test
    ports:
      - "8080:8080"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 开发工具服务
  development-tools:
    build:
      context: ./services/development-tools
      dockerfile: Dockerfile
    environment:
      - DATABASE_URL=************************************************/autonomous_driving_test
      - REDIS_URL=redis://redis:6379
      - LOG_LEVEL=debug
      - ENVIRONMENT=test
    ports:
      - "8081:8080"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 仿真集成服务
  simulation-integration:
    build:
      context: ./services/simulation-integration
      dockerfile: Dockerfile
    environment:
      - DATABASE_URL=************************************************/autonomous_driving_test
      - REDIS_URL=redis://redis:6379
      - LOG_LEVEL=debug
      - ENVIRONMENT=test
      - DDS_DOMAIN_ID=42
    ports:
      - "8082:8080"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 地图编辑服务
  map-editor:
    build:
      context: ./services/map-editor
      dockerfile: Dockerfile
    environment:
      - DATABASE_URL=************************************************/autonomous_driving_test
      - REDIS_URL=redis://redis:6379
      - LOG_LEVEL=debug
      - ENVIRONMENT=test
    ports:
      - "8083:8080"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # API 网关
  api-gateway:
    image: openresty/openresty:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./infrastructure/api-gateway/nginx.conf:/usr/local/openresty/nginx/conf/nginx.conf
      - ./infrastructure/api-gateway/conf.d:/etc/nginx/conf.d
    depends_on:
      - system-management
      - development-tools
      - simulation-integration
      - map-editor
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端应用
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: test
    environment:
      - REACT_APP_API_BASE_URL=http://api-gateway
      - REACT_APP_ENVIRONMENT=test
    ports:
      - "3000:3000"
    depends_on:
      - api-gateway
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 测试运行器
  test-runner:
    build:
      context: ./tests
      dockerfile: Dockerfile
    environment:
      - API_BASE_URL=http://api-gateway
      - DATABASE_URL=************************************************/autonomous_driving_test
      - REDIS_URL=redis://redis:6379
    volumes:
      - ./tests:/app/tests
      - ./integration-tests/reports:/app/reports
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      system-management:
        condition: service_healthy
      development-tools:
        condition: service_healthy
      simulation-integration:
        condition: service_healthy
      map-editor:
        condition: service_healthy
      api-gateway:
        condition: service_healthy
      frontend:
        condition: service_healthy
    command: ["npm", "run", "test:integration"]

  # Prometheus 监控
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_test_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'

  # Grafana 可视化
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_test_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
    depends_on:
      - prometheus

  # Jaeger 链路追踪
  jaeger:
    image: jaegertracing/all-in-one:latest
    ports:
      - "16686:16686"
      - "14268:14268"
    environment:
      - COLLECTOR_OTLP_ENABLED=true

  # ELK Stack - Elasticsearch
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_test_data:/usr/share/elasticsearch/data

  # ELK Stack - Logstash
  logstash:
    image: docker.elastic.co/logstash/logstash:8.11.0
    ports:
      - "5044:5044"
    volumes:
      - ./monitoring/logstash/pipeline:/usr/share/logstash/pipeline
    depends_on:
      - elasticsearch

  # ELK Stack - Kibana
  kibana:
    image: docker.elastic.co/kibana/kibana:8.11.0
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch

  # MinIO 对象存储
  minio:
    image: minio/minio:latest
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin
    volumes:
      - minio_test_data:/data
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

volumes:
  postgres_test_data:
  redis_test_data:
  prometheus_test_data:
  grafana_test_data:
  elasticsearch_test_data:
  minio_test_data:

networks:
  default:
    driver: bridge
