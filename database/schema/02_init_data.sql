-- 自动驾驶开发加速系统 - 初始数据插入脚本
-- 插入系统必需的基础数据
-- 版本: 1.0.0

-- ================================================================
-- 权限数据初始化
-- ================================================================

-- 插入系统权限
INSERT INTO permissions (id, name, description, resource, action) VALUES
-- 用户管理权限
(uuid_generate_v4(), 'user.create', '创建用户', 'user', 'create'),
(uuid_generate_v4(), 'user.read', '查看用户信息', 'user', 'read'),
(uuid_generate_v4(), 'user.update', '更新用户信息', 'user', 'update'),
(uuid_generate_v4(), 'user.delete', '删除用户', 'user', 'delete'),
(uuid_generate_v4(), 'user.manage_roles', '管理用户角色', 'user', 'manage_roles'),

-- 项目管理权限
(uuid_generate_v4(), 'project.create', '创建项目', 'project', 'create'),
(uuid_generate_v4(), 'project.read', '查看项目', 'project', 'read'),
(uuid_generate_v4(), 'project.update', '更新项目', 'project', 'update'),
(uuid_generate_v4(), 'project.delete', '删除项目', 'project', 'delete'),
(uuid_generate_v4(), 'project.manage_members', '管理项目成员', 'project', 'manage_members'),

-- 代码模板权限
(uuid_generate_v4(), 'template.create', '创建代码模板', 'template', 'create'),
(uuid_generate_v4(), 'template.read', '查看代码模板', 'template', 'read'),
(uuid_generate_v4(), 'template.update', '更新代码模板', 'template', 'update'),
(uuid_generate_v4(), 'template.delete', '删除代码模板', 'template', 'delete'),
(uuid_generate_v4(), 'template.publish', '发布代码模板', 'template', 'publish'),

-- 构建部署权限
(uuid_generate_v4(), 'build.create', '创建构建', 'build', 'create'),
(uuid_generate_v4(), 'build.read', '查看构建信息', 'build', 'read'),
(uuid_generate_v4(), 'build.cancel', '取消构建', 'build', 'cancel'),
(uuid_generate_v4(), 'deployment.create', '创建部署', 'deployment', 'create'),
(uuid_generate_v4(), 'deployment.read', '查看部署信息', 'deployment', 'read'),
(uuid_generate_v4(), 'deployment.rollback', '回滚部署', 'deployment', 'rollback'),

-- 仿真权限
(uuid_generate_v4(), 'simulation.create', '创建仿真任务', 'simulation', 'create'),
(uuid_generate_v4(), 'simulation.read', '查看仿真结果', 'simulation', 'read'),
(uuid_generate_v4(), 'simulation.control', '控制仿真执行', 'simulation', 'control'),
(uuid_generate_v4(), 'scenario.create', '创建仿真场景', 'scenario', 'create'),
(uuid_generate_v4(), 'scenario.read', '查看仿真场景', 'scenario', 'read'),
(uuid_generate_v4(), 'scenario.update', '更新仿真场景', 'scenario', 'update'),
(uuid_generate_v4(), 'scenario.delete', '删除仿真场景', 'scenario', 'delete'),

-- 地图编辑权限
(uuid_generate_v4(), 'map.create', '创建地图', 'map', 'create'),
(uuid_generate_v4(), 'map.read', '查看地图', 'map', 'read'),
(uuid_generate_v4(), 'map.update', '编辑地图', 'map', 'update'),
(uuid_generate_v4(), 'map.delete', '删除地图', 'map', 'delete'),
(uuid_generate_v4(), 'map.collaborate', '协作编辑地图', 'map', 'collaborate'),

-- 系统管理权限
(uuid_generate_v4(), 'system.monitor', '系统监控', 'system', 'monitor'),
(uuid_generate_v4(), 'system.configure', '系统配置', 'system', 'configure'),
(uuid_generate_v4(), 'system.backup', '系统备份', 'system', 'backup'),
(uuid_generate_v4(), 'system.restore', '系统恢复', 'system', 'restore'),
(uuid_generate_v4(), 'audit.read', '查看审计日志', 'audit', 'read');

-- ================================================================
-- 角色权限关联
-- ================================================================

-- 管理员权限（拥有所有权限）
INSERT INTO role_permissions (role, permission_id)
SELECT 'admin', id FROM permissions;

-- 开发者权限
INSERT INTO role_permissions (role, permission_id)
SELECT 'developer', id FROM permissions 
WHERE name IN (
    'user.read', 'user.update',
    'project.create', 'project.read', 'project.update', 'project.manage_members',
    'template.create', 'template.read', 'template.update', 'template.publish',
    'build.create', 'build.read', 'build.cancel',
    'deployment.read',
    'simulation.create', 'simulation.read', 'simulation.control',
    'scenario.create', 'scenario.read', 'scenario.update',
    'map.create', 'map.read', 'map.update', 'map.collaborate'
);

-- DevOps权限
INSERT INTO role_permissions (role, permission_id)
SELECT 'devops', id FROM permissions 
WHERE name IN (
    'user.read',
    'project.read',
    'template.read',
    'build.create', 'build.read', 'build.cancel',
    'deployment.create', 'deployment.read', 'deployment.rollback',
    'system.monitor', 'system.configure', 'system.backup', 'system.restore',
    'audit.read'
);

-- 普通用户权限
INSERT INTO role_permissions (role, permission_id)
SELECT 'user', id FROM permissions 
WHERE name IN (
    'user.read', 'user.update',
    'project.read',
    'template.read',
    'build.read',
    'deployment.read',
    'simulation.read',
    'scenario.read',
    'map.read'
);

-- ================================================================
-- 默认用户数据
-- ================================================================

-- 创建默认管理员用户
INSERT INTO users (id, username, email, password_hash, full_name, role, status) VALUES
(uuid_generate_v4(), 'admin', '<EMAIL>', 
 crypt('admin123', gen_salt('bf')), '系统管理员', 'admin', 'active'),

(uuid_generate_v4(), 'developer', '<EMAIL>', 
 crypt('dev123', gen_salt('bf')), '开发工程师', 'developer', 'active'),

(uuid_generate_v4(), 'devops', '<EMAIL>', 
 crypt('ops123', gen_salt('bf')), 'DevOps工程师', 'devops', 'active'),

(uuid_generate_v4(), 'testuser', '<EMAIL>', 
 crypt('test123', gen_salt('bf')), '测试用户', 'user', 'active');

-- ================================================================
-- 默认环境配置
-- ================================================================

-- 创建默认部署环境
INSERT INTO environments (id, name, type, description, config, status) VALUES
(uuid_generate_v4(), 'development', 'development', '开发环境', 
 '{"cluster": "dev-k8s", "namespace": "autodriving-dev", "replicas": 1, "resources": {"cpu": "500m", "memory": "1Gi"}}', 'active'),

(uuid_generate_v4(), 'testing', 'testing', '测试环境', 
 '{"cluster": "test-k8s", "namespace": "autodriving-test", "replicas": 2, "resources": {"cpu": "1", "memory": "2Gi"}}', 'active'),

(uuid_generate_v4(), 'staging', 'staging', '预发布环境', 
 '{"cluster": "staging-k8s", "namespace": "autodriving-staging", "replicas": 3, "resources": {"cpu": "2", "memory": "4Gi"}}', 'active'),

(uuid_generate_v4(), 'production', 'production', '生产环境', 
 '{"cluster": "prod-k8s", "namespace": "autodriving-prod", "replicas": 5, "resources": {"cpu": "4", "memory": "8Gi"}}', 'active');

-- ================================================================
-- 默认代码模板
-- ================================================================

-- 获取管理员用户ID用于模板创建
DO $$
DECLARE
    admin_user_id UUID;
BEGIN
    SELECT id INTO admin_user_id FROM users WHERE username = 'admin';
    
    -- 插入默认代码模板
    INSERT INTO code_templates (id, name, description, category, language, framework, version, template_data, variables, author_id, is_public) VALUES
    (uuid_generate_v4(), 'Go微服务模板', '基于Gin框架的Go微服务项目模板', 'microservice', 'go', 'gin', '1.0.0',
     '{"files": [{"path": "main.go", "content": "package main\n\nimport \"github.com/gin-gonic/gin\"\n\nfunc main() {\n\tr := gin.Default()\n\tr.GET(\"/health\", func(c *gin.Context) {\n\t\tc.JSON(200, gin.H{\"status\": \"ok\"})\n\t})\n\tr.Run(\":8080\")\n}"}]}',
     '{"service_name": {"type": "string", "description": "服务名称", "default": "my-service"}, "port": {"type": "number", "description": "服务端口", "default": 8080}}',
     admin_user_id, true),

    (uuid_generate_v4(), 'React前端模板', '基于React + TypeScript的前端项目模板', 'frontend', 'typescript', 'react', '1.0.0',
     '{"files": [{"path": "src/App.tsx", "content": "import React from \"react\";\n\nfunction App() {\n  return (\n    <div className=\"App\">\n      <h1>{{app_name}}</h1>\n    </div>\n  );\n}\n\nexport default App;"}]}',
     '{"app_name": {"type": "string", "description": "应用名称", "default": "My App"}}',
     admin_user_id, true),

    (uuid_generate_v4(), 'Python数据处理模板', '基于FastAPI的Python数据处理服务模板', 'data-processing', 'python', 'fastapi', '1.0.0',
     '{"files": [{"path": "main.py", "content": "from fastapi import FastAPI\n\napp = FastAPI(title=\"{{service_name}}\")\n\<EMAIL>(\"/health\")\ndef health_check():\n    return {\"status\": \"healthy\"}\n\nif __name__ == \"__main__\":\n    import uvicorn\n    uvicorn.run(app, host=\"0.0.0.0\", port={{port}})"}]}',
     '{"service_name": {"type": "string", "description": "服务名称", "default": "data-processor"}, "port": {"type": "number", "description": "服务端口", "default": 8000}}',
     admin_user_id, true),

    (uuid_generate_v4(), 'Rust地图处理模板', '基于Rust的地图数据处理库模板', 'map-processing', 'rust', 'tokio', '1.0.0',
     '{"files": [{"path": "src/lib.rs", "content": "//! {{crate_name}} - 地图数据处理库\n\npub mod map_parser;\npub mod coordinate_transform;\n\npub use map_parser::*;\npub use coordinate_transform::*;\n\n#[cfg(test)]\nmod tests {\n    use super::*;\n\n    #[test]\n    fn it_works() {\n        assert_eq!(2 + 2, 4);\n    }\n}"}]}',
     '{"crate_name": {"type": "string", "description": "Crate名称", "default": "map-processor"}}',
     admin_user_id, true);
END $$;

-- ================================================================
-- 默认仿真场景
-- ================================================================

-- 获取管理员用户ID用于场景创建
DO $$
DECLARE
    admin_user_id UUID;
BEGIN
    SELECT id INTO admin_user_id FROM users WHERE username = 'admin';
    
    -- 插入默认仿真场景
    INSERT INTO simulation_scenarios (id, name, description, category, scenario_data, parameters, author_id, is_public) VALUES
    (uuid_generate_v4(), '城市道路直行', '在城市道路上直行行驶的基础场景', 'basic',
     '{"map": "Town01", "weather": "clear", "time": "noon", "vehicles": [{"type": "ego", "spawn_point": {"x": 0, "y": 0, "z": 0}}]}',
     '{"speed_limit": {"type": "number", "description": "速度限制(km/h)", "default": 50}, "traffic_density": {"type": "string", "description": "交通密度", "default": "low", "options": ["low", "medium", "high"]}}',
     admin_user_id, true),

    (uuid_generate_v4(), '十字路口左转', '在十字路口进行左转的场景', 'intersection',
     '{"map": "Town03", "weather": "clear", "time": "noon", "vehicles": [{"type": "ego", "spawn_point": {"x": 100, "y": 0, "z": 0}}, {"type": "npc", "spawn_point": {"x": 0, "y": 100, "z": 0}}]}',
     '{"traffic_light": {"type": "boolean", "description": "是否有交通信号灯", "default": true}, "pedestrians": {"type": "number", "description": "行人数量", "default": 2}}',
     admin_user_id, true),

    (uuid_generate_v4(), '高速公路超车', '在高速公路上进行超车的场景', 'highway',
     '{"map": "Town04", "weather": "clear", "time": "noon", "vehicles": [{"type": "ego", "spawn_point": {"x": 0, "y": 0, "z": 0}}, {"type": "npc", "spawn_point": {"x": 50, "y": 0, "z": 0}}]}',
     '{"ego_speed": {"type": "number", "description": "自车初始速度(km/h)", "default": 80}, "npc_speed": {"type": "number", "description": "前车速度(km/h)", "default": 60}}',
     admin_user_id, true),

    (uuid_generate_v4(), '雨天行驶', '在雨天条件下的行驶场景', 'weather',
     '{"map": "Town01", "weather": "rain", "time": "noon", "vehicles": [{"type": "ego", "spawn_point": {"x": 0, "y": 0, "z": 0}}]}',
     '{"rain_intensity": {"type": "number", "description": "降雨强度(0-100)", "default": 50}, "visibility": {"type": "number", "description": "能见度(米)", "default": 100}}',
     admin_user_id, true);
END $$;

-- ================================================================
-- 系统配置数据
-- ================================================================

-- 插入系统事件（示例）
INSERT INTO system_events (event_type, severity, source, message, details) VALUES
('system_start', 'info', 'database', '数据库初始化完成', '{"version": "1.0.0", "tables_created": 25, "indexes_created": 35}'),
('data_init', 'info', 'database', '初始数据插入完成', '{"users_created": 4, "templates_created": 4, "scenarios_created": 4, "environments_created": 4}');

-- ================================================================
-- 数据完整性检查
-- ================================================================

-- 检查数据插入结果
DO $$
DECLARE
    user_count INTEGER;
    permission_count INTEGER;
    template_count INTEGER;
    scenario_count INTEGER;
    environment_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO user_count FROM users;
    SELECT COUNT(*) INTO permission_count FROM permissions;
    SELECT COUNT(*) INTO template_count FROM code_templates;
    SELECT COUNT(*) INTO scenario_count FROM simulation_scenarios;
    SELECT COUNT(*) INTO environment_count FROM environments;
    
    RAISE NOTICE '数据初始化完成统计:';
    RAISE NOTICE '- 用户数量: %', user_count;
    RAISE NOTICE '- 权限数量: %', permission_count;
    RAISE NOTICE '- 代码模板数量: %', template_count;
    RAISE NOTICE '- 仿真场景数量: %', scenario_count;
    RAISE NOTICE '- 环境数量: %', environment_count;
    
    -- 验证关键数据
    IF user_count < 4 THEN
        RAISE EXCEPTION '用户数据初始化失败';
    END IF;
    
    IF permission_count < 30 THEN
        RAISE EXCEPTION '权限数据初始化失败';
    END IF;
    
    RAISE NOTICE '数据初始化验证通过！';
END $$;

-- 初始化完成
COMMENT ON SCHEMA public IS '自动驾驶开发加速系统初始数据 v1.0.0 - 初始化完成';
