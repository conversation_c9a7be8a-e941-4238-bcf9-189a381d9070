# 自动驾驶开发加速系统 - Redis配置文件
# 针对缓存、会话存储和实时数据优化的Redis配置
# 版本: 7.0+

# ================================================================
# 网络配置
# ================================================================

# 绑定地址 - 允许所有接口访问（生产环境应限制）
bind 0.0.0.0

# 端口配置
port 6379

# TCP监听队列长度
tcp-backlog 511

# 客户端超时时间（秒）- 0表示禁用
timeout 300

# TCP keepalive时间（秒）
tcp-keepalive 300

# ================================================================
# 通用配置
# ================================================================

# 守护进程模式
daemonize no

# 进程文件
pidfile /var/run/redis_6379.pid

# 日志级别：debug, verbose, notice, warning
loglevel notice

# 日志文件路径
logfile /var/log/redis/redis-server.log

# 数据库数量
databases 16

# ================================================================
# 安全配置
# ================================================================

# 密码认证（生产环境必须设置）
# requirepass your-strong-password

# 重命名危险命令
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command KEYS ""
rename-command CONFIG "CONFIG_b835c3f8a5d9e7f2"
rename-command SHUTDOWN "SHUTDOWN_a7b9c2d1e4f6g8h3"
rename-command DEBUG ""
rename-command EVAL ""

# 保护模式
protected-mode yes

# ================================================================
# 内存管理
# ================================================================

# 最大内存限制（根据系统内存调整）
maxmemory 2gb

# 内存淘汰策略
# allkeys-lru: 所有key使用LRU算法淘汰
# volatile-lru: 只对设置了过期时间的key使用LRU
# allkeys-random: 随机淘汰所有key
# volatile-random: 随机淘汰有过期时间的key
# volatile-ttl: 淘汰即将过期的key
# noeviction: 不淘汰，内存满时返回错误
maxmemory-policy allkeys-lru

# 内存采样数量（用于LRU算法）
maxmemory-samples 5

# ================================================================
# 持久化配置
# ================================================================

# RDB持久化配置
# save <seconds> <changes>
save 900 1      # 900秒内至少1个key变化时保存
save 300 10     # 300秒内至少10个key变化时保存
save 60 10000   # 60秒内至少10000个key变化时保存

# RDB文件名
dbfilename dump.rdb

# RDB文件目录
dir /data

# RDB文件压缩
rdbcompression yes

# RDB文件校验
rdbchecksum yes

# 后台保存出错时停止写入
stop-writes-on-bgsave-error yes

# AOF持久化配置
appendonly yes
appendfilename "appendonly.aof"

# AOF同步策略
# always: 每个写命令都同步
# everysec: 每秒同步一次（推荐）
# no: 由操作系统决定何时同步
appendfsync everysec

# AOF重写期间是否同步
no-appendfsync-on-rewrite no

# AOF自动重写配置
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# AOF加载时忽略最后一个不完整的命令
aof-load-truncated yes

# 混合持久化（RDB + AOF）
aof-use-rdb-preamble yes

# ================================================================
# 复制配置（主从复制）
# ================================================================

# 从服务器配置（如果是从服务器）
# replicaof <masterip> <masterport>

# 主服务器密码（如果主服务器设置了密码）
# masterauth <master-password>

# 从服务器只读
replica-read-only yes

# 复制超时时间
repl-timeout 60

# 复制积压缓冲区大小
repl-backlog-size 1mb

# 复制积压缓冲区TTL
repl-backlog-ttl 3600

# 从服务器优先级（用于哨兵选举）
replica-priority 100

# ================================================================
# 客户端配置
# ================================================================

# 最大客户端连接数
maxclients 10000

# 客户端输出缓冲区限制
# client-output-buffer-limit <class> <hard limit> <soft limit> <soft seconds>
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit replica 256mb 64mb 60
client-output-buffer-limit pubsub 32mb 8mb 60

# 客户端查询缓冲区限制
client-query-buffer-limit 1gb

# ================================================================
# 慢查询日志
# ================================================================

# 慢查询阈值（微秒）
slowlog-log-slower-than 10000

# 慢查询日志最大长度
slowlog-max-len 128

# ================================================================
# 延迟监控
# ================================================================

# 延迟监控阈值（毫秒）
latency-monitor-threshold 100

# ================================================================
# 事件通知
# ================================================================

# 键空间通知配置
# K: 键空间事件，以__keyspace@<db>__为前缀
# E: 键事件，以__keyevent@<db>__为前缀
# g: 通用命令（DEL, EXPIRE, RENAME等）
# $: 字符串命令
# l: 列表命令
# s: 集合命令
# h: 哈希命令
# z: 有序集合命令
# x: 过期事件
# e: 驱逐事件
# A: g$lshzxe的别名
notify-keyspace-events "Ex"

# ================================================================
# 高级配置
# ================================================================

# 哈希表配置
hash-max-ziplist-entries 512
hash-max-ziplist-value 64

# 列表配置
list-max-ziplist-size -2
list-compress-depth 0

# 集合配置
set-max-intset-entries 512

# 有序集合配置
zset-max-ziplist-entries 128
zset-max-ziplist-value 64

# HyperLogLog配置
hll-sparse-max-bytes 3000

# 流配置
stream-node-max-bytes 4096
stream-node-max-entries 100

# 活跃重新哈希
activerehashing yes

# 客户端输出缓冲区限制
hz 10

# 动态HZ
dynamic-hz yes

# AOF重写增量同步
aof-rewrite-incremental-fsync yes

# RDB保存增量同步
rdb-save-incremental-fsync yes

# ================================================================
# 模块配置
# ================================================================

# 加载模块（如果需要）
# loadmodule /path/to/module.so

# ================================================================
# 集群配置（如果使用Redis集群）
# ================================================================

# 启用集群模式
# cluster-enabled yes

# 集群配置文件
# cluster-config-file nodes-6379.conf

# 集群节点超时时间
# cluster-node-timeout 15000

# 集群故障转移投票有效时间
# cluster-replica-validity-factor 10

# 集群迁移屏障
# cluster-migration-barrier 1

# 集群要求完整覆盖
# cluster-require-full-coverage yes

# ================================================================
# 监控和统计
# ================================================================

# 启用统计信息
# 可以通过INFO命令查看详细统计

# 内存使用情况报告
# 可以通过MEMORY USAGE命令查看key的内存使用

# ================================================================
# 自定义配置（针对自动驾驶系统）
# ================================================================

# JWT令牌缓存配置（数据库0）
# 用于存储JWT令牌和用户会话信息

# 实时数据缓存配置（数据库1）
# 用于存储仿真数据、传感器数据等实时信息

# 地图编辑锁配置（数据库2）
# 用于地图协作编辑的分布式锁

# 系统监控数据配置（数据库3）
# 用于存储系统性能指标和监控数据

# 任务队列配置（数据库4）
# 用于异步任务处理和消息队列

# ================================================================
# 性能优化建议
# ================================================================

# 1. 根据实际内存大小调整maxmemory
# 2. 根据业务特点选择合适的淘汰策略
# 3. 监控慢查询日志，优化慢查询
# 4. 合理设置持久化策略，平衡性能和数据安全
# 5. 使用连接池减少连接开销
# 6. 避免使用KEYS命令，使用SCAN代替
# 7. 合理设置过期时间，避免内存泄漏

# ================================================================
# 安全建议
# ================================================================

# 1. 设置强密码
# 2. 使用防火墙限制访问
# 3. 重命名或禁用危险命令
# 4. 启用保护模式
# 5. 定期备份数据
# 6. 监控异常访问

# 配置文件结束
