// 自动驾驶开发加速系统 - WebSocket处理器
use axum::{
    extract::{
        ws::{Message, WebSocket, WebSocketUpgrade},
        Path, Query, State,
    },
    response::Response,
};
use futures::{sink::SinkExt, stream::StreamExt};
use serde::{Deserialize, Serialize};
use std::{collections::HashMap, sync::Arc};
use tokio::sync::broadcast;
use tracing::{error, info, warn};
use uuid::Uuid;

use crate::services::collaboration_service::{
    CollaborationService, WebSocketMessage, ParticipantInfo, CollaborativeOperation,
};
use crate::AppState;

/// WebSocket连接查询参数
#[derive(Debug, Deserialize)]
pub struct WebSocketQuery {
    pub session_id: Uuid,
    pub user_id: Uuid,
    pub username: String,
    pub role: Option<String>,
}

/// WebSocket连接信息
#[derive(Debug, <PERSON>lone)]
struct ConnectionInfo {
    pub session_id: Uuid,
    pub user_id: Uuid,
    pub username: String,
    pub role: String,
}

/// WebSocket升级处理
pub async fn websocket_handler(
    ws: WebSocketUpgrade,
    Query(params): Query<WebSocketQuery>,
    State(state): State<Arc<AppState>>,
) -> Response {
    info!(
        "WebSocket连接请求: session={}, user={}, username={}",
        params.session_id, params.user_id, params.username
    );

    let connection_info = ConnectionInfo {
        session_id: params.session_id,
        user_id: params.user_id,
        username: params.username,
        role: params.role.unwrap_or_else(|| "editor".to_string()),
    };

    ws.on_upgrade(move |socket| handle_socket(socket, connection_info, state))
}

/// 处理WebSocket连接
async fn handle_socket(
    socket: WebSocket,
    connection_info: ConnectionInfo,
    state: Arc<AppState>,
) {
    let (mut sender, mut receiver) = socket.split();
    let collaboration_service = &state.collaboration_service;

    // 创建参与者信息
    let participant = ParticipantInfo {
        user_id: connection_info.user_id,
        username: connection_info.username.clone(),
        role: connection_info.role.clone(),
        cursor_position: None,
        selection: None,
        last_seen: chrono::Utc::now(),
        is_active: true,
    };

    // 加入协作会话
    if let Err(e) = collaboration_service
        .join_session(connection_info.session_id, participant)
        .await
    {
        error!("加入协作会话失败: {}", e);
        let _ = sender
            .send(Message::Text(
                serde_json::to_string(&WebSocketMessage::Error {
                    message: format!("加入会话失败: {}", e),
                    code: Some("JOIN_SESSION_FAILED".to_string()),
                })
                .unwrap(),
            ))
            .await;
        return;
    }

    // 订阅广播消息
    let mut broadcast_rx = collaboration_service.subscribe();

    // 发送连接成功消息
    let session_state = match collaboration_service
        .get_session_state(connection_info.session_id)
        .await
    {
        Ok(state) => state,
        Err(e) => {
            error!("获取会话状态失败: {}", e);
            return;
        }
    };

    let welcome_message = WebSocketMessage::SessionUpdate {
        session: session_state.session,
        participants: session_state.participants.values().cloned().collect(),
    };

    if let Err(e) = sender
        .send(Message::Text(serde_json::to_string(&welcome_message).unwrap()))
        .await
    {
        error!("发送欢迎消息失败: {}", e);
        return;
    }

    info!(
        "用户 {} 成功连接到会话 {}",
        connection_info.username, connection_info.session_id
    );

    // 处理消息的任务
    let connection_info_clone = connection_info.clone();
    let collaboration_service_clone = collaboration_service.clone();
    let mut send_task = tokio::spawn(async move {
        while let Ok((session_id, message)) = broadcast_rx.recv().await {
            // 只发送属于当前会话的消息
            if session_id == connection_info_clone.session_id {
                let message_text = match serde_json::to_string(&message) {
                    Ok(text) => text,
                    Err(e) => {
                        error!("序列化消息失败: {}", e);
                        continue;
                    }
                };

                if sender.send(Message::Text(message_text)).await.is_err() {
                    break;
                }
            }
        }
    });

    // 接收消息的任务
    let connection_info_clone = connection_info.clone();
    let collaboration_service_clone = collaboration_service.clone();
    let mut recv_task = tokio::spawn(async move {
        while let Some(msg) = receiver.next().await {
            match msg {
                Ok(Message::Text(text)) => {
                    if let Err(e) = handle_text_message(
                        &text,
                        &connection_info_clone,
                        &collaboration_service_clone,
                    )
                    .await
                    {
                        error!("处理文本消息失败: {}", e);
                    }
                }
                Ok(Message::Binary(_)) => {
                    warn!("收到二进制消息，暂不支持");
                }
                Ok(Message::Close(_)) => {
                    info!("WebSocket连接关闭");
                    break;
                }
                Ok(Message::Ping(data)) => {
                    // 响应Ping
                    if sender.send(Message::Pong(data)).await.is_err() {
                        break;
                    }
                }
                Ok(Message::Pong(_)) => {
                    // 忽略Pong消息
                }
                Err(e) => {
                    error!("WebSocket错误: {}", e);
                    break;
                }
            }
        }
    });

    // 等待任务完成
    tokio::select! {
        _ = (&mut send_task) => {
            recv_task.abort();
        },
        _ = (&mut recv_task) => {
            send_task.abort();
        }
    }

    // 用户离开会话
    if let Err(e) = collaboration_service
        .leave_session(connection_info.session_id, connection_info.user_id)
        .await
    {
        error!("离开协作会话失败: {}", e);
    }

    info!(
        "用户 {} 断开连接，会话 {}",
        connection_info.username, connection_info.session_id
    );
}

/// 处理文本消息
async fn handle_text_message(
    text: &str,
    connection_info: &ConnectionInfo,
    collaboration_service: &CollaborationService,
) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    let message: WebSocketMessage = serde_json::from_str(text)?;

    match message {
        WebSocketMessage::Operation { operation } => {
            // 验证操作权限
            if operation.user_id != connection_info.user_id
                || operation.session_id != connection_info.session_id
            {
                return Err("操作权限验证失败".into());
            }

            // 处理协作操作
            let applied_operations = collaboration_service.handle_operation(operation).await?;

            info!(
                "处理协作操作完成，应用了 {} 个操作",
                applied_operations.len()
            );
        }

        WebSocketMessage::CursorUpdate {
            session_id,
            user_id,
            position,
        } => {
            // 验证权限
            if user_id != connection_info.user_id || session_id != connection_info.session_id {
                return Err("光标更新权限验证失败".into());
            }

            // 更新光标位置
            collaboration_service
                .update_cursor(session_id, user_id, position)
                .await?;
        }

        WebSocketMessage::SelectionUpdate {
            session_id,
            user_id,
            selection,
        } => {
            // 验证权限
            if user_id != connection_info.user_id || session_id != connection_info.session_id {
                return Err("选择更新权限验证失败".into());
            }

            // 更新选择区域
            collaboration_service
                .update_selection(session_id, user_id, selection)
                .await?;
        }

        WebSocketMessage::JoinSession { .. } => {
            // 忽略，连接时已处理
        }

        WebSocketMessage::LeaveSession { .. } => {
            // 忽略，断开连接时会自动处理
        }

        _ => {
            warn!("收到不支持的消息类型");
        }
    }

    Ok(())
}

/// 获取会话参与者列表
pub async fn get_session_participants(
    Path(session_id): Path<Uuid>,
    State(state): State<Arc<AppState>>,
) -> Result<axum::Json<Vec<ParticipantInfo>>, axum::http::StatusCode> {
    match state
        .collaboration_service
        .get_session_state(session_id)
        .await
    {
        Ok(session_state) => {
            let participants: Vec<ParticipantInfo> =
                session_state.participants.values().cloned().collect();
            Ok(axum::Json(participants))
        }
        Err(_) => Err(axum::http::StatusCode::NOT_FOUND),
    }
}

/// 获取操作历史
pub async fn get_operation_history(
    Path(session_id): Path<Uuid>,
    Query(params): Query<HashMap<String, String>>,
    State(state): State<Arc<AppState>>,
) -> Result<axum::Json<Vec<CollaborativeOperation>>, axum::http::StatusCode> {
    let limit = params
        .get("limit")
        .and_then(|s| s.parse::<usize>().ok());

    match state
        .collaboration_service
        .get_operation_history(session_id, limit)
        .await
    {
        Ok(operations) => Ok(axum::Json(operations)),
        Err(_) => Err(axum::http::StatusCode::NOT_FOUND),
    }
}

/// 创建协作会话
#[derive(Debug, Deserialize)]
pub struct CreateSessionRequest {
    pub map_id: Uuid,
    pub name: String,
}

pub async fn create_collaboration_session(
    State(state): State<Arc<AppState>>,
    axum::Json(request): axum::Json<CreateSessionRequest>,
) -> Result<axum::Json<crate::models::map::CollaborationSession>, axum::http::StatusCode> {
    // 这里应该从认证中获取用户ID，简化处理
    let creator_id = Uuid::new_v4(); // 临时生成

    match state
        .collaboration_service
        .create_session(request.map_id, creator_id, request.name)
        .await
    {
        Ok(session) => Ok(axum::Json(session)),
        Err(e) => {
            error!("创建协作会话失败: {}", e);
            Err(axum::http::StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

/// 健康检查端点
pub async fn websocket_health() -> &'static str {
    "WebSocket服务正常运行"
}
