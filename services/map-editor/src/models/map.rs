// 自动驾驶开发加速系统 - 地图数据模型
use chrono::{DateTime, Utc};
use geo_types::{Coord, LineString, Point, Polygon};
use serde::{Deserialize, Serialize};
use sqlx::FromRow;
use uuid::Uuid;
use validator::Validate;

/// 地图格式枚举
#[derive(Debug, <PERSON>lone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "map_format", rename_all = "lowercase")]
pub enum MapFormat {
    OpenDrive,
    Lanelet2,
    Osm,
    Geojson,
    Custom,
}

/// 地图状态枚举
#[derive(Debug, Clone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "map_status", rename_all = "lowercase")]
pub enum MapStatus {
    Draft,
    Published,
    Archived,
    Deprecated,
}

/// 坐标系枚举
#[derive(Debug, <PERSON>lone, Serialize, Deserialize, sqlx::Type)]
#[sqlx(type_name = "coordinate_system", rename_all = "lowercase")]
pub enum CoordinateSystem {
    Wgs84,
    Utm,
    LocalCartesian,
    Custom,
}

/// 地图元数据
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, FromRow, Validate)]
pub struct Map {
    pub id: Uuid,
    
    #[validate(length(min = 1, max = 200))]
    pub name: String,
    
    #[validate(length(max = 1000))]
    pub description: Option<String>,
    
    pub format: MapFormat,
    pub status: MapStatus,
    pub coordinate_system: CoordinateSystem,
    
    /// 地图边界框 (min_lon, min_lat, max_lon, max_lat)
    pub bounding_box: Option<[f64; 4]>,
    
    /// 地图中心点
    pub center_point: Option<Point<f64>>,
    
    /// 地图缩放级别
    pub zoom_level: Option<i32>,
    
    /// 地图版本
    pub version: String,
    
    /// 父地图ID（用于版本管理）
    pub parent_id: Option<Uuid>,
    
    /// 创建者ID
    pub creator_id: Uuid,
    
    /// 最后修改者ID
    pub last_modifier_id: Option<Uuid>,
    
    /// 文件路径
    pub file_path: Option<String>,
    
    /// 文件大小（字节）
    pub file_size: Option<i64>,
    
    /// 文件哈希
    pub file_hash: Option<String>,
    
    /// 元数据JSON
    pub metadata: Option<serde_json::Value>,
    
    /// 标签
    pub tags: Vec<String>,
    
    /// 是否公开
    pub is_public: bool,
    
    /// 创建时间
    pub created_at: DateTime<Utc>,
    
    /// 更新时间
    pub updated_at: DateTime<Utc>,
    
    /// 发布时间
    pub published_at: Option<DateTime<Utc>>,
}

/// 地图层
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct MapLayer {
    pub id: Uuid,
    pub map_id: Uuid,
    
    /// 层名称
    pub name: String,
    
    /// 层类型
    pub layer_type: String,
    
    /// 层描述
    pub description: Option<String>,
    
    /// 显示顺序
    pub display_order: i32,
    
    /// 是否可见
    pub visible: bool,
    
    /// 是否可编辑
    pub editable: bool,
    
    /// 样式配置
    pub style_config: Option<serde_json::Value>,
    
    /// 数据源配置
    pub data_source: Option<serde_json::Value>,
    
    /// 创建时间
    pub created_at: DateTime<Utc>,
    
    /// 更新时间
    pub updated_at: DateTime<Utc>,
}

/// 地图要素基类
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MapFeature {
    pub id: Uuid,
    pub layer_id: Uuid,
    
    /// 要素类型
    pub feature_type: String,
    
    /// 几何数据
    pub geometry: GeometryData,
    
    /// 属性数据
    pub properties: serde_json::Value,
    
    /// 样式
    pub style: Option<serde_json::Value>,
    
    /// 创建时间
    pub created_at: DateTime<Utc>,
    
    /// 更新时间
    pub updated_at: DateTime<Utc>,
}

/// 几何数据枚举
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum GeometryData {
    Point { coordinates: Point<f64> },
    LineString { coordinates: LineString<f64> },
    Polygon { coordinates: Polygon<f64> },
    MultiPoint { coordinates: Vec<Point<f64>> },
    MultiLineString { coordinates: Vec<LineString<f64>> },
    MultiPolygon { coordinates: Vec<Polygon<f64>> },
}

/// 道路网络
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct RoadNetwork {
    pub id: Uuid,
    pub map_id: Uuid,
    
    /// 道路网络名称
    pub name: String,
    
    /// 道路网络类型
    pub network_type: String,
    
    /// 道路段列表
    pub road_segments: Vec<Uuid>,
    
    /// 交叉口列表
    pub junctions: Vec<Uuid>,
    
    /// 网络拓扑
    pub topology: Option<serde_json::Value>,
    
    /// 创建时间
    pub created_at: DateTime<Utc>,
    
    /// 更新时间
    pub updated_at: DateTime<Utc>,
}

/// 道路段
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct RoadSegment {
    pub id: Uuid,
    pub network_id: Uuid,
    
    /// 道路段名称
    pub name: String,
    
    /// 道路类型
    pub road_type: String,
    
    /// 中心线
    pub centerline: LineString<f64>,
    
    /// 道路宽度
    pub width: f64,
    
    /// 车道数量
    pub lane_count: i32,
    
    /// 速度限制
    pub speed_limit: Option<f64>,
    
    /// 起始节点ID
    pub start_node_id: Option<Uuid>,
    
    /// 结束节点ID
    pub end_node_id: Option<Uuid>,
    
    /// 前驱道路段
    pub predecessors: Vec<Uuid>,
    
    /// 后继道路段
    pub successors: Vec<Uuid>,
    
    /// 属性数据
    pub properties: serde_json::Value,
    
    /// 创建时间
    pub created_at: DateTime<Utc>,
    
    /// 更新时间
    pub updated_at: DateTime<Utc>,
}

/// 车道
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct Lane {
    pub id: Uuid,
    pub road_segment_id: Uuid,
    
    /// 车道索引
    pub lane_index: i32,
    
    /// 车道类型
    pub lane_type: String,
    
    /// 车道方向
    pub direction: String,
    
    /// 左边界线
    pub left_boundary: LineString<f64>,
    
    /// 右边界线
    pub right_boundary: LineString<f64>,
    
    /// 中心线
    pub centerline: LineString<f64>,
    
    /// 车道宽度
    pub width: f64,
    
    /// 速度限制
    pub speed_limit: Option<f64>,
    
    /// 左相邻车道ID
    pub left_neighbor_id: Option<Uuid>,
    
    /// 右相邻车道ID
    pub right_neighbor_id: Option<Uuid>,
    
    /// 属性数据
    pub properties: serde_json::Value,
    
    /// 创建时间
    pub created_at: DateTime<Utc>,
    
    /// 更新时间
    pub updated_at: DateTime<Utc>,
}

/// 交叉口
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct Junction {
    pub id: Uuid,
    pub network_id: Uuid,
    
    /// 交叉口名称
    pub name: String,
    
    /// 交叉口类型
    pub junction_type: String,
    
    /// 中心点
    pub center_point: Point<f64>,
    
    /// 边界多边形
    pub boundary: Polygon<f64>,
    
    /// 连接的道路段
    pub connected_roads: Vec<Uuid>,
    
    /// 交通信号灯
    pub traffic_lights: Vec<Uuid>,
    
    /// 交通标志
    pub traffic_signs: Vec<Uuid>,
    
    /// 属性数据
    pub properties: serde_json::Value,
    
    /// 创建时间
    pub created_at: DateTime<Utc>,
    
    /// 更新时间
    pub updated_at: DateTime<Utc>,
}

/// 交通信号灯
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct TrafficLight {
    pub id: Uuid,
    pub junction_id: Option<Uuid>,
    
    /// 信号灯位置
    pub position: Point<f64>,
    
    /// 信号灯类型
    pub light_type: String,
    
    /// 控制的车道
    pub controlled_lanes: Vec<Uuid>,
    
    /// 信号相位配置
    pub phase_config: Option<serde_json::Value>,
    
    /// 属性数据
    pub properties: serde_json::Value,
    
    /// 创建时间
    pub created_at: DateTime<Utc>,
    
    /// 更新时间
    pub updated_at: DateTime<Utc>,
}

/// 交通标志
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct TrafficSign {
    pub id: Uuid,
    pub junction_id: Option<Uuid>,
    
    /// 标志位置
    pub position: Point<f64>,
    
    /// 标志类型
    pub sign_type: String,
    
    /// 标志内容
    pub content: String,
    
    /// 影响的车道
    pub affected_lanes: Vec<Uuid>,
    
    /// 属性数据
    pub properties: serde_json::Value,
    
    /// 创建时间
    pub created_at: DateTime<Utc>,
    
    /// 更新时间
    pub updated_at: DateTime<Utc>,
}

/// 地图版本
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct MapVersion {
    pub id: Uuid,
    pub map_id: Uuid,
    
    /// 版本号
    pub version: String,
    
    /// 版本描述
    pub description: Option<String>,
    
    /// 变更日志
    pub changelog: Option<String>,
    
    /// 创建者ID
    pub creator_id: Uuid,
    
    /// 是否为当前版本
    pub is_current: bool,
    
    /// 文件路径
    pub file_path: String,
    
    /// 文件大小
    pub file_size: i64,
    
    /// 文件哈希
    pub file_hash: String,
    
    /// 创建时间
    pub created_at: DateTime<Utc>,
}

/// 地图协作会话
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct CollaborationSession {
    pub id: Uuid,
    pub map_id: Uuid,
    
    /// 会话名称
    pub name: String,
    
    /// 创建者ID
    pub creator_id: Uuid,
    
    /// 参与者列表
    pub participants: Vec<Uuid>,
    
    /// 会话状态
    pub status: String,
    
    /// 会话配置
    pub config: Option<serde_json::Value>,
    
    /// 创建时间
    pub created_at: DateTime<Utc>,
    
    /// 更新时间
    pub updated_at: DateTime<Utc>,
    
    /// 结束时间
    pub ended_at: Option<DateTime<Utc>>,
}

/// 地图操作记录
#[derive(Debug, Clone, Serialize, Deserialize, FromRow)]
pub struct MapOperation {
    pub id: Uuid,
    pub map_id: Uuid,
    pub session_id: Option<Uuid>,
    
    /// 操作类型
    pub operation_type: String,
    
    /// 操作者ID
    pub operator_id: Uuid,
    
    /// 操作目标
    pub target_type: String,
    pub target_id: Uuid,
    
    /// 操作前数据
    pub before_data: Option<serde_json::Value>,
    
    /// 操作后数据
    pub after_data: Option<serde_json::Value>,
    
    /// 操作描述
    pub description: Option<String>,
    
    /// 操作时间
    pub created_at: DateTime<Utc>,
}

impl Map {
    /// 创建新地图
    pub fn new(
        name: String,
        format: MapFormat,
        coordinate_system: CoordinateSystem,
        creator_id: Uuid,
    ) -> Self {
        let now = Utc::now();
        Self {
            id: Uuid::new_v4(),
            name,
            description: None,
            format,
            status: MapStatus::Draft,
            coordinate_system,
            bounding_box: None,
            center_point: None,
            zoom_level: None,
            version: "1.0.0".to_string(),
            parent_id: None,
            creator_id,
            last_modifier_id: None,
            file_path: None,
            file_size: None,
            file_hash: None,
            metadata: None,
            tags: Vec::new(),
            is_public: false,
            created_at: now,
            updated_at: now,
            published_at: None,
        }
    }
    
    /// 更新地图
    pub fn update(&mut self, modifier_id: Uuid) {
        self.last_modifier_id = Some(modifier_id);
        self.updated_at = Utc::now();
    }
    
    /// 发布地图
    pub fn publish(&mut self) {
        self.status = MapStatus::Published;
        self.published_at = Some(Utc::now());
        self.updated_at = Utc::now();
    }
}

impl GeometryData {
    /// 获取几何体的边界框
    pub fn bounding_box(&self) -> Option<[f64; 4]> {
        match self {
            GeometryData::Point { coordinates } => {
                let coord = coordinates.0;
                Some([coord.x, coord.y, coord.x, coord.y])
            }
            GeometryData::LineString { coordinates } => {
                if coordinates.0.is_empty() {
                    return None;
                }
                let mut min_x = f64::INFINITY;
                let mut min_y = f64::INFINITY;
                let mut max_x = f64::NEG_INFINITY;
                let mut max_y = f64::NEG_INFINITY;
                
                for coord in &coordinates.0 {
                    min_x = min_x.min(coord.x);
                    min_y = min_y.min(coord.y);
                    max_x = max_x.max(coord.x);
                    max_y = max_y.max(coord.y);
                }
                
                Some([min_x, min_y, max_x, max_y])
            }
            // 其他几何类型的实现...
            _ => None,
        }
    }
}

/// 地图创建请求
#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct CreateMapRequest {
    #[validate(length(min = 1, max = 200))]
    pub name: String,
    
    #[validate(length(max = 1000))]
    pub description: Option<String>,
    
    pub format: MapFormat,
    pub coordinate_system: CoordinateSystem,
    pub tags: Option<Vec<String>>,
    pub is_public: Option<bool>,
}

/// 地图更新请求
#[derive(Debug, Serialize, Deserialize, Validate)]
pub struct UpdateMapRequest {
    #[validate(length(min = 1, max = 200))]
    pub name: Option<String>,
    
    #[validate(length(max = 1000))]
    pub description: Option<String>,
    
    pub tags: Option<Vec<String>>,
    pub is_public: Option<bool>,
    pub metadata: Option<serde_json::Value>,
}

/// 地图查询参数
#[derive(Debug, Serialize, Deserialize)]
pub struct MapQuery {
    pub format: Option<MapFormat>,
    pub status: Option<MapStatus>,
    pub creator_id: Option<Uuid>,
    pub tags: Option<Vec<String>>,
    pub is_public: Option<bool>,
    pub search: Option<String>,
    pub page: Option<u32>,
    pub page_size: Option<u32>,
    pub sort_by: Option<String>,
    pub sort_order: Option<String>,
}
