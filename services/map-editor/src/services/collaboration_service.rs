// 自动驾驶开发加速系统 - 协作编辑服务
use anyhow::{anyhow, Result};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::{broadcast, RwLock};
use tracing::{info, warn, error};
use uuid::Uuid;

use crate::models::map::{CollaborationSession, MapOperation};
use crate::repositories::map_repository::MapRepository;

/// 操作类型
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub enum OperationType {
    Insert,
    Delete,
    Update,
    Move,
    Transform,
}

/// 协作操作
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CollaborativeOperation {
    pub id: Uuid,
    pub session_id: Uuid,
    pub user_id: Uuid,
    pub operation_type: OperationType,
    pub target_type: String,
    pub target_id: Uuid,
    pub position: Option<u64>,
    pub content: Option<serde_json::Value>,
    pub metadata: HashMap<String, serde_json::Value>,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub vector_clock: VectorClock,
}

/// 向量时钟
#[derive(Debug, Clone, Serialize, Deserialize, PartialEq)]
pub struct VectorClock {
    pub clocks: HashMap<Uuid, u64>,
}

impl VectorClock {
    pub fn new() -> Self {
        Self {
            clocks: HashMap::new(),
        }
    }
    
    pub fn increment(&mut self, user_id: Uuid) {
        let counter = self.clocks.entry(user_id).or_insert(0);
        *counter += 1;
    }
    
    pub fn update(&mut self, other: &VectorClock) {
        for (user_id, clock) in &other.clocks {
            let current = self.clocks.entry(*user_id).or_insert(0);
            *current = (*current).max(*clock);
        }
    }
    
    pub fn happens_before(&self, other: &VectorClock) -> bool {
        let mut strictly_less = false;
        
        for (user_id, other_clock) in &other.clocks {
            let self_clock = self.clocks.get(user_id).unwrap_or(&0);
            if self_clock > other_clock {
                return false;
            }
            if self_clock < other_clock {
                strictly_less = true;
            }
        }
        
        for (user_id, self_clock) in &self.clocks {
            let other_clock = other.clocks.get(user_id).unwrap_or(&0);
            if self_clock > other_clock {
                return false;
            }
        }
        
        strictly_less
    }
    
    pub fn concurrent_with(&self, other: &VectorClock) -> bool {
        !self.happens_before(other) && !other.happens_before(self) && self != other
    }
}

/// 操作转换结果
#[derive(Debug, Clone)]
pub struct TransformResult {
    pub transformed_op: CollaborativeOperation,
    pub should_apply: bool,
}

/// 冲突解决策略
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ConflictResolutionStrategy {
    LastWriterWins,
    FirstWriterWins,
    UserPriority(Uuid),
    Manual,
}

/// 协作会话状态
#[derive(Debug, Clone)]
pub struct SessionState {
    pub session: CollaborationSession,
    pub participants: HashMap<Uuid, ParticipantInfo>,
    pub operation_history: Vec<CollaborativeOperation>,
    pub vector_clock: VectorClock,
    pub conflict_resolution: ConflictResolutionStrategy,
}

/// 参与者信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ParticipantInfo {
    pub user_id: Uuid,
    pub username: String,
    pub role: String,
    pub cursor_position: Option<serde_json::Value>,
    pub selection: Option<serde_json::Value>,
    pub last_seen: chrono::DateTime<chrono::Utc>,
    pub is_active: bool,
}

/// WebSocket消息类型
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type")]
pub enum WebSocketMessage {
    JoinSession {
        session_id: Uuid,
        user_info: ParticipantInfo,
    },
    LeaveSession {
        session_id: Uuid,
        user_id: Uuid,
    },
    Operation {
        operation: CollaborativeOperation,
    },
    CursorUpdate {
        session_id: Uuid,
        user_id: Uuid,
        position: serde_json::Value,
    },
    SelectionUpdate {
        session_id: Uuid,
        user_id: Uuid,
        selection: serde_json::Value,
    },
    ConflictNotification {
        session_id: Uuid,
        conflict_id: Uuid,
        operations: Vec<CollaborativeOperation>,
    },
    SessionUpdate {
        session: CollaborationSession,
        participants: Vec<ParticipantInfo>,
    },
    Error {
        message: String,
        code: Option<String>,
    },
}

/// 协作编辑服务
pub struct CollaborationService {
    repository: Arc<dyn MapRepository>,
    sessions: Arc<RwLock<HashMap<Uuid, SessionState>>>,
    broadcast_tx: broadcast::Sender<(Uuid, WebSocketMessage)>,
    _broadcast_rx: broadcast::Receiver<(Uuid, WebSocketMessage)>,
}

impl CollaborationService {
    pub fn new(repository: Arc<dyn MapRepository>) -> Self {
        let (broadcast_tx, broadcast_rx) = broadcast::channel(1000);
        
        Self {
            repository,
            sessions: Arc::new(RwLock::new(HashMap::new())),
            broadcast_tx,
            _broadcast_rx: broadcast_rx,
        }
    }
    
    /// 创建协作会话
    pub async fn create_session(
        &self,
        map_id: Uuid,
        creator_id: Uuid,
        name: String,
    ) -> Result<CollaborationSession> {
        let session = CollaborationSession {
            id: Uuid::new_v4(),
            map_id,
            name,
            creator_id,
            participants: vec![creator_id],
            status: "active".to_string(),
            config: None,
            created_at: chrono::Utc::now(),
            updated_at: chrono::Utc::now(),
            ended_at: None,
        };
        
        // 保存到数据库
        let saved_session = self.repository.create_collaboration_session(session.clone()).await?;
        
        // 创建会话状态
        let session_state = SessionState {
            session: saved_session.clone(),
            participants: HashMap::new(),
            operation_history: Vec::new(),
            vector_clock: VectorClock::new(),
            conflict_resolution: ConflictResolutionStrategy::LastWriterWins,
        };
        
        // 添加到内存
        self.sessions.write().await.insert(saved_session.id, session_state);
        
        info!("创建协作会话: {} ({})", saved_session.name, saved_session.id);
        Ok(saved_session)
    }
    
    /// 加入协作会话
    pub async fn join_session(
        &self,
        session_id: Uuid,
        participant: ParticipantInfo,
    ) -> Result<()> {
        let mut sessions = self.sessions.write().await;
        let session_state = sessions.get_mut(&session_id)
            .ok_or_else(|| anyhow!("协作会话不存在"))?;
        
        // 添加参与者
        session_state.participants.insert(participant.user_id, participant.clone());
        session_state.session.participants.push(participant.user_id);
        session_state.session.updated_at = chrono::Utc::now();
        
        // 广播会话更新
        let message = WebSocketMessage::SessionUpdate {
            session: session_state.session.clone(),
            participants: session_state.participants.values().cloned().collect(),
        };
        
        self.broadcast_to_session(session_id, message).await?;
        
        info!("用户 {} 加入协作会话 {}", participant.user_id, session_id);
        Ok(())
    }
    
    /// 离开协作会话
    pub async fn leave_session(&self, session_id: Uuid, user_id: Uuid) -> Result<()> {
        let mut sessions = self.sessions.write().await;
        let session_state = sessions.get_mut(&session_id)
            .ok_or_else(|| anyhow!("协作会话不存在"))?;
        
        // 移除参与者
        session_state.participants.remove(&user_id);
        session_state.session.participants.retain(|&id| id != user_id);
        session_state.session.updated_at = chrono::Utc::now();
        
        // 广播会话更新
        let message = WebSocketMessage::SessionUpdate {
            session: session_state.session.clone(),
            participants: session_state.participants.values().cloned().collect(),
        };
        
        self.broadcast_to_session(session_id, message).await?;
        
        info!("用户 {} 离开协作会话 {}", user_id, session_id);
        Ok(())
    }
    
    /// 处理协作操作
    pub async fn handle_operation(
        &self,
        mut operation: CollaborativeOperation,
    ) -> Result<Vec<CollaborativeOperation>> {
        let mut sessions = self.sessions.write().await;
        let session_state = sessions.get_mut(&operation.session_id)
            .ok_or_else(|| anyhow!("协作会话不存在"))?;
        
        // 更新向量时钟
        session_state.vector_clock.increment(operation.user_id);
        operation.vector_clock = session_state.vector_clock.clone();
        
        // 检查并发操作
        let concurrent_ops = self.find_concurrent_operations(&operation, &session_state.operation_history);
        
        if !concurrent_ops.is_empty() {
            info!("检测到并发操作，开始操作转换");
            
            // 执行操作转换
            let transform_results = self.transform_operations(&operation, &concurrent_ops).await?;
            
            // 应用转换后的操作
            let mut applied_operations = Vec::new();
            for result in transform_results {
                if result.should_apply {
                    self.apply_operation(&result.transformed_op, session_state).await?;
                    applied_operations.push(result.transformed_op);
                }
            }
            
            return Ok(applied_operations);
        }
        
        // 没有冲突，直接应用操作
        self.apply_operation(&operation, session_state).await?;
        Ok(vec![operation])
    }
    
    /// 更新光标位置
    pub async fn update_cursor(
        &self,
        session_id: Uuid,
        user_id: Uuid,
        position: serde_json::Value,
    ) -> Result<()> {
        let mut sessions = self.sessions.write().await;
        let session_state = sessions.get_mut(&session_id)
            .ok_or_else(|| anyhow!("协作会话不存在"))?;
        
        if let Some(participant) = session_state.participants.get_mut(&user_id) {
            participant.cursor_position = Some(position.clone());
            participant.last_seen = chrono::Utc::now();
        }
        
        // 广播光标更新
        let message = WebSocketMessage::CursorUpdate {
            session_id,
            user_id,
            position,
        };
        
        self.broadcast_to_session(session_id, message).await?;
        Ok(())
    }
    
    /// 更新选择区域
    pub async fn update_selection(
        &self,
        session_id: Uuid,
        user_id: Uuid,
        selection: serde_json::Value,
    ) -> Result<()> {
        let mut sessions = self.sessions.write().await;
        let session_state = sessions.get_mut(&session_id)
            .ok_or_else(|| anyhow!("协作会话不存在"))?;
        
        if let Some(participant) = session_state.participants.get_mut(&user_id) {
            participant.selection = Some(selection.clone());
            participant.last_seen = chrono::Utc::now();
        }
        
        // 广播选择更新
        let message = WebSocketMessage::SelectionUpdate {
            session_id,
            user_id,
            selection,
        };
        
        self.broadcast_to_session(session_id, message).await?;
        Ok(())
    }
    
    /// 获取会话状态
    pub async fn get_session_state(&self, session_id: Uuid) -> Result<SessionState> {
        let sessions = self.sessions.read().await;
        let session_state = sessions.get(&session_id)
            .ok_or_else(|| anyhow!("协作会话不存在"))?;
        
        Ok(session_state.clone())
    }
    
    /// 获取操作历史
    pub async fn get_operation_history(
        &self,
        session_id: Uuid,
        limit: Option<usize>,
    ) -> Result<Vec<CollaborativeOperation>> {
        let sessions = self.sessions.read().await;
        let session_state = sessions.get(&session_id)
            .ok_or_else(|| anyhow!("协作会话不存在"))?;
        
        let history = &session_state.operation_history;
        let operations = if let Some(limit) = limit {
            history.iter().rev().take(limit).cloned().collect()
        } else {
            history.clone()
        };
        
        Ok(operations)
    }
    
    /// 获取广播接收器
    pub fn subscribe(&self) -> broadcast::Receiver<(Uuid, WebSocketMessage)> {
        self.broadcast_tx.subscribe()
    }
    
    // 私有方法
    
    async fn apply_operation(
        &self,
        operation: &CollaborativeOperation,
        session_state: &mut SessionState,
    ) -> Result<()> {
        // 添加到操作历史
        session_state.operation_history.push(operation.clone());
        
        // 记录到数据库
        let map_operation = MapOperation {
            id: operation.id,
            map_id: session_state.session.map_id,
            session_id: Some(operation.session_id),
            operation_type: format!("{:?}", operation.operation_type),
            operator_id: operation.user_id,
            target_type: operation.target_type.clone(),
            target_id: operation.target_id,
            before_data: None,
            after_data: operation.content.clone(),
            description: None,
            created_at: operation.timestamp,
        };
        
        self.repository.record_operation(map_operation).await?;
        
        // 广播操作
        let message = WebSocketMessage::Operation {
            operation: operation.clone(),
        };
        
        self.broadcast_to_session(operation.session_id, message).await?;
        
        Ok(())
    }
    
    fn find_concurrent_operations(
        &self,
        operation: &CollaborativeOperation,
        history: &[CollaborativeOperation],
    ) -> Vec<CollaborativeOperation> {
        history
            .iter()
            .filter(|op| {
                op.user_id != operation.user_id
                    && op.vector_clock.concurrent_with(&operation.vector_clock)
                    && self.operations_conflict(operation, op)
            })
            .cloned()
            .collect()
    }
    
    fn operations_conflict(
        &self,
        op1: &CollaborativeOperation,
        op2: &CollaborativeOperation,
    ) -> bool {
        // 简化的冲突检测逻辑
        op1.target_type == op2.target_type && op1.target_id == op2.target_id
    }
    
    async fn transform_operations(
        &self,
        operation: &CollaborativeOperation,
        concurrent_ops: &[CollaborativeOperation],
    ) -> Result<Vec<TransformResult>> {
        let mut results = Vec::new();
        
        // 简化的操作转换实现
        for concurrent_op in concurrent_ops {
            let transformed = self.transform_operation_pair(operation, concurrent_op).await?;
            results.push(transformed);
        }
        
        // 添加原始操作
        results.push(TransformResult {
            transformed_op: operation.clone(),
            should_apply: true,
        });
        
        Ok(results)
    }
    
    async fn transform_operation_pair(
        &self,
        op1: &CollaborativeOperation,
        op2: &CollaborativeOperation,
    ) -> Result<TransformResult> {
        // 简化的操作转换逻辑
        // 实际实现需要根据具体的操作类型进行复杂的转换
        
        match (&op1.operation_type, &op2.operation_type) {
            (OperationType::Insert, OperationType::Insert) => {
                // 处理插入-插入冲突
                self.transform_insert_insert(op1, op2).await
            }
            (OperationType::Delete, OperationType::Delete) => {
                // 处理删除-删除冲突
                self.transform_delete_delete(op1, op2).await
            }
            (OperationType::Update, OperationType::Update) => {
                // 处理更新-更新冲突
                self.transform_update_update(op1, op2).await
            }
            _ => {
                // 其他类型的冲突处理
                Ok(TransformResult {
                    transformed_op: op1.clone(),
                    should_apply: true,
                })
            }
        }
    }
    
    async fn transform_insert_insert(
        &self,
        op1: &CollaborativeOperation,
        op2: &CollaborativeOperation,
    ) -> Result<TransformResult> {
        // 简化实现：根据位置调整插入操作
        let mut transformed_op = op1.clone();
        
        if let (Some(pos1), Some(pos2)) = (op1.position, op2.position) {
            if pos1 >= pos2 {
                transformed_op.position = Some(pos1 + 1);
            }
        }
        
        Ok(TransformResult {
            transformed_op,
            should_apply: true,
        })
    }
    
    async fn transform_delete_delete(
        &self,
        op1: &CollaborativeOperation,
        op2: &CollaborativeOperation,
    ) -> Result<TransformResult> {
        // 简化实现：如果删除同一个对象，只应用一个
        if op1.target_id == op2.target_id {
            Ok(TransformResult {
                transformed_op: op1.clone(),
                should_apply: false, // 已经被删除
            })
        } else {
            Ok(TransformResult {
                transformed_op: op1.clone(),
                should_apply: true,
            })
        }
    }
    
    async fn transform_update_update(
        &self,
        op1: &CollaborativeOperation,
        op2: &CollaborativeOperation,
    ) -> Result<TransformResult> {
        // 简化实现：使用最后写入者获胜策略
        if op1.timestamp > op2.timestamp {
            Ok(TransformResult {
                transformed_op: op1.clone(),
                should_apply: true,
            })
        } else {
            Ok(TransformResult {
                transformed_op: op1.clone(),
                should_apply: false,
            })
        }
    }
    
    async fn broadcast_to_session(
        &self,
        session_id: Uuid,
        message: WebSocketMessage,
    ) -> Result<()> {
        if let Err(e) = self.broadcast_tx.send((session_id, message)) {
            warn!("广播消息失败: {}", e);
        }
        Ok(())
    }
}
