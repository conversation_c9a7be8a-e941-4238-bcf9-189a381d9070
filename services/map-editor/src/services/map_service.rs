// 自动驾驶开发加速系统 - 地图服务层
use anyhow::{anyhow, Result};
use std::sync::Arc;
use uuid::Uuid;
use tracing::{info, warn, error};

use crate::models::map::{
    Map, MapLayer, MapFeature, RoadNetwork, MapVersion, CollaborationSession,
    MapOperation, MapQuery, CreateMapRequest, UpdateMapRequest, MapFormat
};
use crate::repositories::map_repository::MapRepository;
use crate::services::file_service::FileService;
use crate::services::format_converter::FormatConverter;
use crate::services::validation_service::ValidationService;

pub struct MapService {
    repository: Arc<dyn MapRepository>,
    file_service: Arc<FileService>,
    format_converter: Arc<FormatConverter>,
    validation_service: Arc<ValidationService>,
}

impl MapService {
    pub fn new(
        repository: Arc<dyn MapRepository>,
        file_service: Arc<FileService>,
        format_converter: Arc<FormatConverter>,
        validation_service: Arc<ValidationService>,
    ) -> Self {
        Self {
            repository,
            file_service,
            format_converter,
            validation_service,
        }
    }
    
    /// 创建新地图
    pub async fn create_map(&self, request: CreateMapRequest, creator_id: Uuid) -> Result<Map> {
        info!("创建新地图: {}", request.name);
        
        // 验证请求
        self.validation_service.validate_create_map_request(&request)?;
        
        // 检查地图名称是否已存在
        if self.is_map_name_exists(&request.name, creator_id).await? {
            return Err(anyhow!("地图名称已存在"));
        }
        
        // 创建地图
        let map = self.repository.create_map(request, creator_id).await?;
        
        // 创建默认图层
        self.create_default_layers(&map).await?;
        
        info!("地图创建成功: {} ({})", map.name, map.id);
        Ok(map)
    }
    
    /// 获取地图详情
    pub async fn get_map(&self, id: Uuid, user_id: Uuid) -> Result<Option<Map>> {
        let map = self.repository.get_map_by_id(id).await?;
        
        if let Some(ref map) = map {
            // 检查访问权限
            if !self.check_map_access_permission(map, user_id).await? {
                return Err(anyhow!("没有访问权限"));
            }
        }
        
        Ok(map)
    }
    
    /// 更新地图
    pub async fn update_map(
        &self,
        id: Uuid,
        request: UpdateMapRequest,
        modifier_id: Uuid,
    ) -> Result<Map> {
        info!("更新地图: {}", id);
        
        // 验证请求
        self.validation_service.validate_update_map_request(&request)?;
        
        // 检查地图是否存在
        let existing_map = self.repository.get_map_by_id(id).await?
            .ok_or_else(|| anyhow!("地图不存在"))?;
        
        // 检查编辑权限
        if !self.check_map_edit_permission(&existing_map, modifier_id).await? {
            return Err(anyhow!("没有编辑权限"));
        }
        
        // 更新地图
        let updated_map = self.repository.update_map(id, request, modifier_id).await?;
        
        // 记录操作
        self.record_map_operation(
            id,
            "update_map".to_string(),
            modifier_id,
            "map".to_string(),
            id,
            None,
            Some(serde_json::to_value(&updated_map)?),
            Some("更新地图信息".to_string()),
        ).await?;
        
        info!("地图更新成功: {}", id);
        Ok(updated_map)
    }
    
    /// 删除地图
    pub async fn delete_map(&self, id: Uuid, user_id: Uuid) -> Result<()> {
        info!("删除地图: {}", id);
        
        // 检查地图是否存在
        let map = self.repository.get_map_by_id(id).await?
            .ok_or_else(|| anyhow!("地图不存在"))?;
        
        // 检查删除权限
        if !self.check_map_delete_permission(&map, user_id).await? {
            return Err(anyhow!("没有删除权限"));
        }
        
        // 检查是否有活跃的协作会话
        let active_sessions = self.repository.get_active_sessions(id).await?;
        if !active_sessions.is_empty() {
            return Err(anyhow!("存在活跃的协作会话，无法删除地图"));
        }
        
        // 删除相关文件
        if let Some(file_path) = &map.file_path {
            if let Err(e) = self.file_service.delete_file(file_path).await {
                warn!("删除地图文件失败: {}", e);
            }
        }
        
        // 删除地图版本文件
        let versions = self.repository.get_map_versions(id).await?;
        for version in versions {
            if let Err(e) = self.file_service.delete_file(&version.file_path).await {
                warn!("删除版本文件失败: {}", e);
            }
        }
        
        // 删除地图
        self.repository.delete_map(id).await?;
        
        info!("地图删除成功: {}", id);
        Ok(())
    }
    
    /// 查询地图列表
    pub async fn list_maps(&self, query: MapQuery, user_id: Uuid) -> Result<(Vec<Map>, u64)> {
        // 如果不是管理员，只能查看自己的地图或公开地图
        let mut filtered_query = query;
        if !self.is_admin(user_id).await? {
            // 添加权限过滤条件
            // 这里简化处理，实际应该根据用户权限进行复杂的过滤
        }
        
        let (maps, total) = self.repository.list_maps(filtered_query).await?;
        Ok((maps, total))
    }
    
    /// 上传地图文件
    pub async fn upload_map_file(
        &self,
        map_id: Uuid,
        file_data: Vec<u8>,
        file_name: String,
        user_id: Uuid,
    ) -> Result<Map> {
        info!("上传地图文件: {} -> {}", file_name, map_id);
        
        // 检查地图是否存在
        let mut map = self.repository.get_map_by_id(map_id).await?
            .ok_or_else(|| anyhow!("地图不存在"))?;
        
        // 检查编辑权限
        if !self.check_map_edit_permission(&map, user_id).await? {
            return Err(anyhow!("没有编辑权限"));
        }
        
        // 验证文件格式
        let detected_format = self.detect_file_format(&file_data, &file_name)?;
        if detected_format != map.format {
            return Err(anyhow!("文件格式与地图格式不匹配"));
        }
        
        // 验证文件内容
        self.validation_service.validate_map_file(&file_data, &map.format).await?;
        
        // 保存文件
        let file_path = self.file_service.save_map_file(map_id, &file_data, &file_name).await?;
        let file_hash = self.file_service.calculate_file_hash(&file_data)?;
        
        // 更新地图信息
        map.file_path = Some(file_path);
        map.file_size = Some(file_data.len() as i64);
        map.file_hash = Some(file_hash);
        map.update(user_id);
        
        // 解析地图内容并更新边界框等信息
        if let Ok(bounds) = self.extract_map_bounds(&file_data, &map.format).await {
            map.bounding_box = Some(bounds);
        }
        
        // 保存更新
        let request = UpdateMapRequest {
            name: None,
            description: None,
            tags: None,
            is_public: None,
            metadata: None,
        };
        let updated_map = self.repository.update_map(map_id, request, user_id).await?;
        
        // 创建新版本
        self.create_map_version(&updated_map, user_id, "文件上传".to_string()).await?;
        
        info!("地图文件上传成功: {}", map_id);
        Ok(updated_map)
    }
    
    /// 下载地图文件
    pub async fn download_map_file(&self, map_id: Uuid, user_id: Uuid) -> Result<Vec<u8>> {
        // 检查地图是否存在
        let map = self.repository.get_map_by_id(map_id).await?
            .ok_or_else(|| anyhow!("地图不存在"))?;
        
        // 检查访问权限
        if !self.check_map_access_permission(&map, user_id).await? {
            return Err(anyhow!("没有访问权限"));
        }
        
        // 获取文件路径
        let file_path = map.file_path
            .ok_or_else(|| anyhow!("地图文件不存在"))?;
        
        // 读取文件
        let file_data = self.file_service.read_file(&file_path).await?;
        
        Ok(file_data)
    }
    
    /// 转换地图格式
    pub async fn convert_map_format(
        &self,
        map_id: Uuid,
        target_format: MapFormat,
        user_id: Uuid,
    ) -> Result<Vec<u8>> {
        info!("转换地图格式: {} -> {:?}", map_id, target_format);
        
        // 检查地图是否存在
        let map = self.repository.get_map_by_id(map_id).await?
            .ok_or_else(|| anyhow!("地图不存在"))?;
        
        // 检查访问权限
        if !self.check_map_access_permission(&map, user_id).await? {
            return Err(anyhow!("没有访问权限"));
        }
        
        // 获取源文件
        let file_path = map.file_path
            .ok_or_else(|| anyhow!("地图文件不存在"))?;
        let source_data = self.file_service.read_file(&file_path).await?;
        
        // 执行格式转换
        let converted_data = self.format_converter.convert(
            &source_data,
            &map.format,
            &target_format,
        ).await?;
        
        info!("地图格式转换成功: {} -> {:?}", map_id, target_format);
        Ok(converted_data)
    }
    
    /// 创建地图版本
    pub async fn create_map_version(
        &self,
        map: &Map,
        creator_id: Uuid,
        description: String,
    ) -> Result<MapVersion> {
        let version = MapVersion {
            id: Uuid::new_v4(),
            map_id: map.id,
            version: self.generate_next_version(&map.version)?,
            description: Some(description),
            changelog: None,
            creator_id,
            is_current: true,
            file_path: map.file_path.clone().unwrap_or_default(),
            file_size: map.file_size.unwrap_or(0),
            file_hash: map.file_hash.clone().unwrap_or_default(),
            created_at: chrono::Utc::now(),
        };
        
        self.repository.create_map_version(version).await
    }
    
    /// 获取地图版本列表
    pub async fn get_map_versions(&self, map_id: Uuid, user_id: Uuid) -> Result<Vec<MapVersion>> {
        // 检查访问权限
        let map = self.repository.get_map_by_id(map_id).await?
            .ok_or_else(|| anyhow!("地图不存在"))?;
        
        if !self.check_map_access_permission(&map, user_id).await? {
            return Err(anyhow!("没有访问权限"));
        }
        
        self.repository.get_map_versions(map_id).await
    }
    
    // 私有辅助方法
    
    async fn is_map_name_exists(&self, name: &str, creator_id: Uuid) -> Result<bool> {
        let query = MapQuery {
            format: None,
            status: None,
            creator_id: Some(creator_id),
            tags: None,
            is_public: None,
            search: Some(name.to_string()),
            page: Some(1),
            page_size: Some(1),
            sort_by: None,
            sort_order: None,
        };
        
        let (maps, _) = self.repository.list_maps(query).await?;
        Ok(maps.iter().any(|m| m.name == name))
    }
    
    async fn check_map_access_permission(&self, map: &Map, user_id: Uuid) -> Result<bool> {
        // 检查是否为创建者、公开地图或有特殊权限
        Ok(map.creator_id == user_id || map.is_public || self.is_admin(user_id).await?)
    }
    
    async fn check_map_edit_permission(&self, map: &Map, user_id: Uuid) -> Result<bool> {
        // 检查是否为创建者或有编辑权限
        Ok(map.creator_id == user_id || self.is_admin(user_id).await?)
    }
    
    async fn check_map_delete_permission(&self, map: &Map, user_id: Uuid) -> Result<bool> {
        // 检查是否为创建者或有删除权限
        Ok(map.creator_id == user_id || self.is_admin(user_id).await?)
    }
    
    async fn is_admin(&self, user_id: Uuid) -> Result<bool> {
        // 这里应该查询用户权限系统
        // 简化实现，返回false
        Ok(false)
    }
    
    async fn create_default_layers(&self, map: &Map) -> Result<()> {
        // 创建默认图层：道路、建筑、地标等
        let default_layers = vec![
            ("道路网络", "road_network", 1),
            ("建筑物", "buildings", 2),
            ("地标", "landmarks", 3),
            ("交通设施", "traffic_facilities", 4),
        ];
        
        for (name, layer_type, order) in default_layers {
            let layer = MapLayer {
                id: Uuid::new_v4(),
                map_id: map.id,
                name: name.to_string(),
                layer_type: layer_type.to_string(),
                description: None,
                display_order: order,
                visible: true,
                editable: true,
                style_config: None,
                data_source: None,
                created_at: chrono::Utc::now(),
                updated_at: chrono::Utc::now(),
            };
            
            self.repository.create_layer(layer).await?;
        }
        
        Ok(())
    }
    
    fn detect_file_format(&self, file_data: &[u8], file_name: &str) -> Result<MapFormat> {
        // 根据文件扩展名和内容检测格式
        let extension = std::path::Path::new(file_name)
            .extension()
            .and_then(|ext| ext.to_str())
            .unwrap_or("");
        
        match extension.to_lowercase().as_str() {
            "xodr" => Ok(MapFormat::OpenDrive),
            "osm" => Ok(MapFormat::Osm),
            "geojson" | "json" => Ok(MapFormat::Geojson),
            _ => {
                // 根据文件内容检测
                if file_data.starts_with(b"<?xml") {
                    if String::from_utf8_lossy(file_data).contains("OpenDRIVE") {
                        Ok(MapFormat::OpenDrive)
                    } else {
                        Ok(MapFormat::Osm)
                    }
                } else if file_data.starts_with(b"{") {
                    Ok(MapFormat::Geojson)
                } else {
                    Ok(MapFormat::Custom)
                }
            }
        }
    }
    
    async fn extract_map_bounds(&self, file_data: &[u8], format: &MapFormat) -> Result<[f64; 4]> {
        // 根据格式解析地图边界
        match format {
            MapFormat::OpenDrive => {
                // 解析OpenDRIVE文件获取边界
                // 这里简化实现
                Ok([-180.0, -90.0, 180.0, 90.0])
            }
            MapFormat::Geojson => {
                // 解析GeoJSON文件获取边界
                // 这里简化实现
                Ok([-180.0, -90.0, 180.0, 90.0])
            }
            _ => Ok([-180.0, -90.0, 180.0, 90.0]),
        }
    }
    
    fn generate_next_version(&self, current_version: &str) -> Result<String> {
        // 简单的版本号生成逻辑
        let parts: Vec<&str> = current_version.split('.').collect();
        if parts.len() != 3 {
            return Err(anyhow!("无效的版本号格式"));
        }
        
        let major: u32 = parts[0].parse()?;
        let minor: u32 = parts[1].parse()?;
        let patch: u32 = parts[2].parse()?;
        
        Ok(format!("{}.{}.{}", major, minor, patch + 1))
    }
    
    async fn record_map_operation(
        &self,
        map_id: Uuid,
        operation_type: String,
        operator_id: Uuid,
        target_type: String,
        target_id: Uuid,
        before_data: Option<serde_json::Value>,
        after_data: Option<serde_json::Value>,
        description: Option<String>,
    ) -> Result<()> {
        let operation = MapOperation {
            id: Uuid::new_v4(),
            map_id,
            session_id: None,
            operation_type,
            operator_id,
            target_type,
            target_id,
            before_data,
            after_data,
            description,
            created_at: chrono::Utc::now(),
        };
        
        self.repository.record_operation(operation).await?;
        Ok(())
    }
}
