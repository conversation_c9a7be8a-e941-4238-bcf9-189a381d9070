# 自动驾驶开发加速系统 - 地图编辑服务
[package]
name = "map-editor-service"
version = "1.0.0"
edition = "2021"
authors = ["Autonomous Driving Development Team"]
description = "地图编辑服务 - 支持OpenDRIVE、Lanelet2等地图格式的编辑和管理"
license = "MIT"

[dependencies]
# Web框架
tokio = { version = "1.0", features = ["full"] }
axum = { version = "0.7", features = ["ws", "multipart"] }
tower = { version = "0.4", features = ["full"] }
tower-http = { version = "0.5", features = ["cors", "trace", "fs"] }
hyper = { version = "1.0", features = ["full"] }

# 序列化和反序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
serde_yaml = "0.9"
serde_xml_rs = "0.6"

# 数据库
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "postgres", "uuid", "chrono", "json"] }
redis = { version = "0.24", features = ["tokio-comp"] }

# 地理空间数据处理
gdal = { version = "0.16", features = ["array"] }
geo = "0.27"
geo-types = "0.7"
geojson = "0.24"
proj = "0.27"

# UUID和时间处理
uuid = { version = "1.0", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }

# 日志和错误处理
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
anyhow = "1.0"
thiserror = "1.0"

# 配置管理
config = "0.14"
dotenvy = "0.15"

# 异步和并发
futures = "0.3"
async-trait = "0.1"

# HTTP客户端
reqwest = { version = "0.11", features = ["json", "multipart"] }

# 文件处理
tempfile = "3.0"
zip = "0.6"

# 验证
validator = { version = "0.18", features = ["derive"] }

# WebSocket
tokio-tungstenite = "0.21"

# 密码学
sha2 = "0.10"
base64 = "0.21"

# 内存映射文件
memmap2 = "0.9"

# XML处理
quick-xml = { version = "0.31", features = ["serialize"] }

# 数学计算
nalgebra = "0.32"

[dev-dependencies]
tokio-test = "0.4"
mockall = "0.12"
tempfile = "3.0"

[features]
default = ["postgres"]
postgres = []
mysql = ["sqlx/mysql"]
sqlite = ["sqlx/sqlite"]

[[bin]]
name = "map-editor-service"
path = "src/main.rs"

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"

[profile.dev]
opt-level = 0
debug = true
