// 自动驾驶开发加速系统 - 系统管理服务主程序
// 提供用户认证、权限管理、服务注册发现等核心功能
package main

import (
	"context"
	"fmt"
	"log"
	"net"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"

	"autodriving/services/system-management/internal/config"
	"autodriving/services/system-management/internal/handler"
	"autodriving/services/system-management/internal/middleware"
	"autodriving/services/system-management/internal/repository"
	"autodriving/services/system-management/internal/service"
	pb "autodriving/services/system-management/proto"
)

func main() {
	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 初始化日志
	setupLogger(cfg.Log.Level)

	// 初始化数据库连接
	db, err := repository.NewDatabase(cfg.Database)
	if err != nil {
		log.Fatalf("数据库连接失败: %v", err)
	}
	defer db.Close()

	// 初始化Redis连接
	redis, err := repository.NewRedis(cfg.Redis)
	if err != nil {
		log.Fatalf("Redis连接失败: %v", err)
	}
	defer redis.Close()

	// 初始化仓储层
	userRepo := repository.NewUserRepository(db)
	sessionRepo := repository.NewSessionRepository(redis)
	serviceRepo := repository.NewServiceRepository(redis)

	// 初始化服务层
	authService := service.NewAuthService(userRepo, sessionRepo, cfg.JWT)
	userService := service.NewUserService(userRepo)
	serviceDiscovery := service.NewServiceDiscovery(serviceRepo)

	// 初始化处理器
	authHandler := handler.NewAuthHandler(authService)
	userHandler := handler.NewUserHandler(userService)
	serviceHandler := handler.NewServiceHandler(serviceDiscovery)

	// 创建上下文
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// 启动gRPC服务器
	grpcServer := startGRPCServer(cfg, authService, userService, serviceDiscovery)

	// 启动HTTP服务器
	httpServer := startHTTPServer(cfg, authHandler, userHandler, serviceHandler)

	// 启动监控服务器
	metricsServer := startMetricsServer(cfg.Metrics.Port)

	// 启动服务发现
	go func() {
		if err := serviceDiscovery.Start(ctx); err != nil {
			log.Printf("服务发现启动失败: %v", err)
		}
	}()

	log.Printf("系统管理服务启动成功")
	log.Printf("gRPC服务器监听: %s", cfg.GRPC.Address)
	log.Printf("HTTP服务器监听: %s", cfg.HTTP.Address)
	log.Printf("监控服务器监听: :%d", cfg.Metrics.Port)

	// 等待中断信号
	waitForShutdown(ctx, cancel, grpcServer, httpServer, metricsServer)
}

// setupLogger 设置日志配置
func setupLogger(level string) {
	// 这里可以集成更复杂的日志库如logrus或zap
	log.SetFlags(log.LstdFlags | log.Lshortfile)
	log.Printf("日志级别设置为: %s", level)
}

// startGRPCServer 启动gRPC服务器
func startGRPCServer(cfg *config.Config, authService *service.AuthService, 
	userService *service.UserService, serviceDiscovery *service.ServiceDiscovery) *grpc.Server {
	
	// 创建gRPC服务器
	server := grpc.NewServer(
		grpc.UnaryInterceptor(middleware.GRPCLoggingInterceptor),
	)

	// 注册服务
	pb.RegisterAuthServiceServer(server, handler.NewGRPCAuthHandler(authService))
	pb.RegisterUserServiceServer(server, handler.NewGRPCUserHandler(userService))
	pb.RegisterServiceDiscoveryServer(server, handler.NewGRPCServiceHandler(serviceDiscovery))

	// 启用反射（开发环境）
	if cfg.Environment == "development" {
		reflection.Register(server)
	}

	// 启动监听
	go func() {
		lis, err := net.Listen("tcp", cfg.GRPC.Address)
		if err != nil {
			log.Fatalf("gRPC监听失败: %v", err)
		}

		log.Printf("gRPC服务器启动，监听地址: %s", cfg.GRPC.Address)
		if err := server.Serve(lis); err != nil {
			log.Fatalf("gRPC服务器启动失败: %v", err)
		}
	}()

	return server
}

// startHTTPServer 启动HTTP服务器
func startHTTPServer(cfg *config.Config, authHandler *handler.AuthHandler,
	userHandler *handler.UserHandler, serviceHandler *handler.ServiceHandler) *http.Server {
	
	// 设置Gin模式
	if cfg.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	}

	// 创建路由器
	router := gin.New()

	// 添加中间件
	router.Use(gin.Logger())
	router.Use(gin.Recovery())
	router.Use(middleware.CORS())
	router.Use(middleware.RequestID())

	// 健康检查
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":    "healthy",
			"timestamp": time.Now().Unix(),
			"service":   "system-management",
			"version":   cfg.Version,
		})
	})

	// API路由组
	v1 := router.Group("/api/v1")
	{
		// 认证相关路由
		auth := v1.Group("/auth")
		{
			auth.POST("/login", authHandler.Login)
			auth.POST("/logout", authHandler.Logout)
			auth.POST("/refresh", authHandler.RefreshToken)
			auth.GET("/me", middleware.AuthRequired(), authHandler.GetCurrentUser)
		}

		// 用户管理路由
		users := v1.Group("/users")
		users.Use(middleware.AuthRequired())
		{
			users.GET("", userHandler.ListUsers)
			users.POST("", middleware.RequireRole("admin"), userHandler.CreateUser)
			users.GET("/:id", userHandler.GetUser)
			users.PUT("/:id", userHandler.UpdateUser)
			users.DELETE("/:id", middleware.RequireRole("admin"), userHandler.DeleteUser)
		}

		// 服务发现路由
		services := v1.Group("/services")
		services.Use(middleware.AuthRequired())
		{
			services.GET("", serviceHandler.ListServices)
			services.POST("/register", serviceHandler.RegisterService)
			services.DELETE("/:id", serviceHandler.UnregisterService)
			services.GET("/:id/health", serviceHandler.CheckServiceHealth)
		}

		// 系统管理路由
		system := v1.Group("/system")
		system.Use(middleware.AuthRequired(), middleware.RequireRole("admin"))
		{
			system.GET("/info", func(c *gin.Context) {
				c.JSON(http.StatusOK, gin.H{
					"version":     cfg.Version,
					"environment": cfg.Environment,
					"uptime":      time.Since(startTime).String(),
				})
			})
		}
	}

	// 创建HTTP服务器
	server := &http.Server{
		Addr:         cfg.HTTP.Address,
		Handler:      router,
		ReadTimeout:  time.Duration(cfg.HTTP.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(cfg.HTTP.WriteTimeout) * time.Second,
		IdleTimeout:  time.Duration(cfg.HTTP.IdleTimeout) * time.Second,
	}

	// 启动服务器
	go func() {
		log.Printf("HTTP服务器启动，监听地址: %s", cfg.HTTP.Address)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("HTTP服务器启动失败: %v", err)
		}
	}()

	return server
}

// startMetricsServer 启动监控指标服务器
func startMetricsServer(port int) *http.Server {
	mux := http.NewServeMux()
	mux.Handle("/metrics", promhttp.Handler())
	
	server := &http.Server{
		Addr:    fmt.Sprintf(":%d", port),
		Handler: mux,
	}

	go func() {
		log.Printf("监控服务器启动，监听端口: %d", port)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Printf("监控服务器启动失败: %v", err)
		}
	}()

	return server
}

var startTime = time.Now()

// waitForShutdown 等待关闭信号并优雅关闭服务
func waitForShutdown(ctx context.Context, cancel context.CancelFunc, 
	grpcServer *grpc.Server, httpServer *http.Server, metricsServer *http.Server) {
	
	// 创建信号通道
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 等待信号
	<-sigChan
	log.Println("收到关闭信号，开始优雅关闭...")

	// 取消上下文
	cancel()

	// 设置关闭超时
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer shutdownCancel()

	// 关闭HTTP服务器
	if err := httpServer.Shutdown(shutdownCtx); err != nil {
		log.Printf("HTTP服务器关闭失败: %v", err)
	} else {
		log.Println("HTTP服务器已关闭")
	}

	// 关闭监控服务器
	if err := metricsServer.Shutdown(shutdownCtx); err != nil {
		log.Printf("监控服务器关闭失败: %v", err)
	} else {
		log.Println("监控服务器已关闭")
	}

	// 关闭gRPC服务器
	grpcServer.GracefulStop()
	log.Println("gRPC服务器已关闭")

	log.Println("系统管理服务已完全关闭")
}
