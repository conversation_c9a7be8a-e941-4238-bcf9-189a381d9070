// 自动驾驶开发加速系统 - 用户服务单元测试
package tests

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"

	"autonomous-driving-platform/internal/models"
	"autonomous-driving-platform/internal/services"
	"autonomous-driving-platform/pkg/auth"
	"autonomous-driving-platform/pkg/logger"
)

// MockUserRepository 模拟用户仓库
type MockUserRepository struct {
	mock.Mock
}

func (m *MockUserRepository) Create(ctx context.Context, user *models.User) error {
	args := m.Called(ctx, user)
	return args.Error(0)
}

func (m *MockUserRepository) GetByID(ctx context.Context, id uint) (*models.User, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*models.User), args.Error(1)
}

func (m *MockUserRepository) GetByEmail(ctx context.Context, email string) (*models.User, error) {
	args := m.Called(ctx, email)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.User), args.Error(1)
}

func (m *MockUserRepository) GetByUsername(ctx context.Context, username string) (*models.User, error) {
	args := m.Called(ctx, username)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*models.User), args.Error(1)
}

func (m *MockUserRepository) Update(ctx context.Context, user *models.User) error {
	args := m.Called(ctx, user)
	return args.Error(0)
}

func (m *MockUserRepository) Delete(ctx context.Context, id uint) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockUserRepository) List(ctx context.Context, offset, limit int) ([]*models.User, int64, error) {
	args := m.Called(ctx, offset, limit)
	return args.Get(0).([]*models.User), args.Get(1).(int64), args.Error(2)
}

// MockJWTManager 模拟JWT管理器
type MockJWTManager struct {
	mock.Mock
}

func (m *MockJWTManager) GenerateToken(userID uint, username string, roles []string) (string, error) {
	args := m.Called(userID, username, roles)
	return args.String(0), args.Error(1)
}

func (m *MockJWTManager) ValidateToken(tokenString string) (*auth.Claims, error) {
	args := m.Called(tokenString)
	return args.Get(0).(*auth.Claims), args.Error(1)
}

// UserServiceTestSuite 用户服务测试套件
type UserServiceTestSuite struct {
	suite.Suite
	userRepo   *MockUserRepository
	jwtManager *MockJWTManager
	userService *services.UserService
	ctx        context.Context
}

func (suite *UserServiceTestSuite) SetupTest() {
	suite.userRepo = new(MockUserRepository)
	suite.jwtManager = new(MockJWTManager)
	suite.ctx = context.Background()
	
	// 初始化日志
	logger.Init("test", "debug")
	
	// 创建用户服务实例
	suite.userService = services.NewUserService(suite.userRepo, suite.jwtManager)
}

func (suite *UserServiceTestSuite) TearDownTest() {
	suite.userRepo.AssertExpectations(suite.T())
	suite.jwtManager.AssertExpectations(suite.T())
}

// TestCreateUser 测试创建用户
func (suite *UserServiceTestSuite) TestCreateUser_Success() {
	// 准备测试数据
	createReq := &services.CreateUserRequest{
		Username: "testuser",
		Email:    "<EMAIL>",
		Password: "password123",
		FullName: "Test User",
		Roles:    []string{"user"},
	}

	// 设置模拟期望
	suite.userRepo.On("GetByEmail", suite.ctx, createReq.Email).Return(nil, gorm.ErrRecordNotFound)
	suite.userRepo.On("GetByUsername", suite.ctx, createReq.Username).Return(nil, gorm.ErrRecordNotFound)
	suite.userRepo.On("Create", suite.ctx, mock.AnythingOfType("*models.User")).Return(nil)

	// 执行测试
	user, err := suite.userService.CreateUser(suite.ctx, createReq)

	// 验证结果
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), user)
	assert.Equal(suite.T(), createReq.Username, user.Username)
	assert.Equal(suite.T(), createReq.Email, user.Email)
	assert.Equal(suite.T(), createReq.FullName, user.FullName)
	assert.NotEmpty(suite.T(), user.PasswordHash)
	assert.NotEqual(suite.T(), createReq.Password, user.PasswordHash) // 密码应该被哈希
}

func (suite *UserServiceTestSuite) TestCreateUser_EmailExists() {
	// 准备测试数据
	createReq := &services.CreateUserRequest{
		Username: "testuser",
		Email:    "<EMAIL>",
		Password: "password123",
		FullName: "Test User",
		Roles:    []string{"user"},
	}

	existingUser := &models.User{
		ID:    1,
		Email: createReq.Email,
	}

	// 设置模拟期望
	suite.userRepo.On("GetByEmail", suite.ctx, createReq.Email).Return(existingUser, nil)

	// 执行测试
	user, err := suite.userService.CreateUser(suite.ctx, createReq)

	// 验证结果
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), user)
	assert.Contains(suite.T(), err.Error(), "邮箱已存在")
}

func (suite *UserServiceTestSuite) TestCreateUser_UsernameExists() {
	// 准备测试数据
	createReq := &services.CreateUserRequest{
		Username: "testuser",
		Email:    "<EMAIL>",
		Password: "password123",
		FullName: "Test User",
		Roles:    []string{"user"},
	}

	existingUser := &models.User{
		ID:       1,
		Username: createReq.Username,
	}

	// 设置模拟期望
	suite.userRepo.On("GetByEmail", suite.ctx, createReq.Email).Return(nil, gorm.ErrRecordNotFound)
	suite.userRepo.On("GetByUsername", suite.ctx, createReq.Username).Return(existingUser, nil)

	// 执行测试
	user, err := suite.userService.CreateUser(suite.ctx, createReq)

	// 验证结果
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), user)
	assert.Contains(suite.T(), err.Error(), "用户名已存在")
}

// TestAuthenticateUser 测试用户认证
func (suite *UserServiceTestSuite) TestAuthenticateUser_Success() {
	// 准备测试数据
	password := "password123"
	hashedPassword, _ := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	
	user := &models.User{
		ID:           1,
		Username:     "testuser",
		Email:        "<EMAIL>",
		PasswordHash: string(hashedPassword),
		Status:       models.UserStatusActive,
		Roles: []models.Role{
			{Name: "user"},
		},
	}

	loginReq := &services.LoginRequest{
		Username: "testuser",
		Password: password,
	}

	// 设置模拟期望
	suite.userRepo.On("GetByUsername", suite.ctx, loginReq.Username).Return(user, nil)
	suite.jwtManager.On("GenerateToken", user.ID, user.Username, []string{"user"}).Return("mock-jwt-token", nil)

	// 执行测试
	response, err := suite.userService.AuthenticateUser(suite.ctx, loginReq)

	// 验证结果
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), response)
	assert.Equal(suite.T(), "mock-jwt-token", response.Token)
	assert.Equal(suite.T(), user.ID, response.User.ID)
	assert.Equal(suite.T(), user.Username, response.User.Username)
}

func (suite *UserServiceTestSuite) TestAuthenticateUser_InvalidUsername() {
	// 准备测试数据
	loginReq := &services.LoginRequest{
		Username: "nonexistent",
		Password: "password123",
	}

	// 设置模拟期望
	suite.userRepo.On("GetByUsername", suite.ctx, loginReq.Username).Return(nil, gorm.ErrRecordNotFound)

	// 执行测试
	response, err := suite.userService.AuthenticateUser(suite.ctx, loginReq)

	// 验证结果
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), response)
	assert.Contains(suite.T(), err.Error(), "用户名或密码错误")
}

func (suite *UserServiceTestSuite) TestAuthenticateUser_InvalidPassword() {
	// 准备测试数据
	password := "password123"
	hashedPassword, _ := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	
	user := &models.User{
		ID:           1,
		Username:     "testuser",
		Email:        "<EMAIL>",
		PasswordHash: string(hashedPassword),
		Status:       models.UserStatusActive,
	}

	loginReq := &services.LoginRequest{
		Username: "testuser",
		Password: "wrongpassword",
	}

	// 设置模拟期望
	suite.userRepo.On("GetByUsername", suite.ctx, loginReq.Username).Return(user, nil)

	// 执行测试
	response, err := suite.userService.AuthenticateUser(suite.ctx, loginReq)

	// 验证结果
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), response)
	assert.Contains(suite.T(), err.Error(), "用户名或密码错误")
}

func (suite *UserServiceTestSuite) TestAuthenticateUser_InactiveUser() {
	// 准备测试数据
	password := "password123"
	hashedPassword, _ := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	
	user := &models.User{
		ID:           1,
		Username:     "testuser",
		Email:        "<EMAIL>",
		PasswordHash: string(hashedPassword),
		Status:       models.UserStatusInactive,
	}

	loginReq := &services.LoginRequest{
		Username: "testuser",
		Password: password,
	}

	// 设置模拟期望
	suite.userRepo.On("GetByUsername", suite.ctx, loginReq.Username).Return(user, nil)

	// 执行测试
	response, err := suite.userService.AuthenticateUser(suite.ctx, loginReq)

	// 验证结果
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), response)
	assert.Contains(suite.T(), err.Error(), "用户账户已被禁用")
}

// TestGetUser 测试获取用户
func (suite *UserServiceTestSuite) TestGetUser_Success() {
	// 准备测试数据
	user := &models.User{
		ID:       1,
		Username: "testuser",
		Email:    "<EMAIL>",
		FullName: "Test User",
		Status:   models.UserStatusActive,
		Roles: []models.Role{
			{Name: "user"},
		},
	}

	// 设置模拟期望
	suite.userRepo.On("GetByID", suite.ctx, uint(1)).Return(user, nil)

	// 执行测试
	result, err := suite.userService.GetUser(suite.ctx, 1)

	// 验证结果
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), user.ID, result.ID)
	assert.Equal(suite.T(), user.Username, result.Username)
}

func (suite *UserServiceTestSuite) TestGetUser_NotFound() {
	// 设置模拟期望
	suite.userRepo.On("GetByID", suite.ctx, uint(999)).Return(nil, gorm.ErrRecordNotFound)

	// 执行测试
	result, err := suite.userService.GetUser(suite.ctx, 999)

	// 验证结果
	assert.Error(suite.T(), err)
	assert.Nil(suite.T(), result)
	assert.Contains(suite.T(), err.Error(), "用户不存在")
}

// TestUpdateUser 测试更新用户
func (suite *UserServiceTestSuite) TestUpdateUser_Success() {
	// 准备测试数据
	user := &models.User{
		ID:       1,
		Username: "testuser",
		Email:    "<EMAIL>",
		FullName: "Test User",
		Status:   models.UserStatusActive,
	}

	updateReq := &services.UpdateUserRequest{
		FullName: "Updated Test User",
		Email:    "<EMAIL>",
	}

	// 设置模拟期望
	suite.userRepo.On("GetByID", suite.ctx, uint(1)).Return(user, nil)
	suite.userRepo.On("GetByEmail", suite.ctx, updateReq.Email).Return(nil, gorm.ErrRecordNotFound)
	suite.userRepo.On("Update", suite.ctx, mock.AnythingOfType("*models.User")).Return(nil)

	// 执行测试
	result, err := suite.userService.UpdateUser(suite.ctx, 1, updateReq)

	// 验证结果
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), updateReq.FullName, result.FullName)
	assert.Equal(suite.T(), updateReq.Email, result.Email)
}

// TestDeleteUser 测试删除用户
func (suite *UserServiceTestSuite) TestDeleteUser_Success() {
	// 准备测试数据
	user := &models.User{
		ID:       1,
		Username: "testuser",
		Email:    "<EMAIL>",
		Status:   models.UserStatusActive,
	}

	// 设置模拟期望
	suite.userRepo.On("GetByID", suite.ctx, uint(1)).Return(user, nil)
	suite.userRepo.On("Delete", suite.ctx, uint(1)).Return(nil)

	// 执行测试
	err := suite.userService.DeleteUser(suite.ctx, 1)

	// 验证结果
	assert.NoError(suite.T(), err)
}

// TestListUsers 测试获取用户列表
func (suite *UserServiceTestSuite) TestListUsers_Success() {
	// 准备测试数据
	users := []*models.User{
		{
			ID:       1,
			Username: "user1",
			Email:    "<EMAIL>",
			FullName: "User One",
			Status:   models.UserStatusActive,
		},
		{
			ID:       2,
			Username: "user2",
			Email:    "<EMAIL>",
			FullName: "User Two",
			Status:   models.UserStatusActive,
		},
	}

	// 设置模拟期望
	suite.userRepo.On("List", suite.ctx, 0, 10).Return(users, int64(2), nil)

	// 执行测试
	result, total, err := suite.userService.ListUsers(suite.ctx, 1, 10)

	// 验证结果
	assert.NoError(suite.T(), err)
	assert.NotNil(suite.T(), result)
	assert.Equal(suite.T(), 2, len(result))
	assert.Equal(suite.T(), int64(2), total)
	assert.Equal(suite.T(), "user1", result[0].Username)
	assert.Equal(suite.T(), "user2", result[1].Username)
}

// TestChangePassword 测试修改密码
func (suite *UserServiceTestSuite) TestChangePassword_Success() {
	// 准备测试数据
	oldPassword := "oldpassword123"
	newPassword := "newpassword123"
	hashedOldPassword, _ := bcrypt.GenerateFromPassword([]byte(oldPassword), bcrypt.DefaultCost)
	
	user := &models.User{
		ID:           1,
		Username:     "testuser",
		PasswordHash: string(hashedOldPassword),
		Status:       models.UserStatusActive,
	}

	changeReq := &services.ChangePasswordRequest{
		OldPassword: oldPassword,
		NewPassword: newPassword,
	}

	// 设置模拟期望
	suite.userRepo.On("GetByID", suite.ctx, uint(1)).Return(user, nil)
	suite.userRepo.On("Update", suite.ctx, mock.AnythingOfType("*models.User")).Return(nil)

	// 执行测试
	err := suite.userService.ChangePassword(suite.ctx, 1, changeReq)

	// 验证结果
	assert.NoError(suite.T(), err)
}

func (suite *UserServiceTestSuite) TestChangePassword_InvalidOldPassword() {
	// 准备测试数据
	oldPassword := "oldpassword123"
	hashedOldPassword, _ := bcrypt.GenerateFromPassword([]byte(oldPassword), bcrypt.DefaultCost)
	
	user := &models.User{
		ID:           1,
		Username:     "testuser",
		PasswordHash: string(hashedOldPassword),
		Status:       models.UserStatusActive,
	}

	changeReq := &services.ChangePasswordRequest{
		OldPassword: "wrongoldpassword",
		NewPassword: "newpassword123",
	}

	// 设置模拟期望
	suite.userRepo.On("GetByID", suite.ctx, uint(1)).Return(user, nil)

	// 执行测试
	err := suite.userService.ChangePassword(suite.ctx, 1, changeReq)

	// 验证结果
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "原密码错误")
}

// 性能测试
func (suite *UserServiceTestSuite) TestUserService_Performance() {
	// 准备大量测试数据
	users := make([]*models.User, 1000)
	for i := 0; i < 1000; i++ {
		users[i] = &models.User{
			ID:       uint(i + 1),
			Username: fmt.Sprintf("user%d", i+1),
			Email:    fmt.Sprintf("<EMAIL>", i+1),
			FullName: fmt.Sprintf("User %d", i+1),
			Status:   models.UserStatusActive,
		}
	}

	// 设置模拟期望
	suite.userRepo.On("List", suite.ctx, 0, 1000).Return(users, int64(1000), nil)

	// 测试性能
	start := time.Now()
	result, total, err := suite.userService.ListUsers(suite.ctx, 1, 1000)
	duration := time.Since(start)

	// 验证结果
	assert.NoError(suite.T(), err)
	assert.Equal(suite.T(), 1000, len(result))
	assert.Equal(suite.T(), int64(1000), total)
	
	// 验证性能（应该在合理时间内完成）
	assert.Less(suite.T(), duration, time.Second, "用户列表查询应该在1秒内完成")
}

// 并发测试
func (suite *UserServiceTestSuite) TestUserService_Concurrency() {
	// 准备测试数据
	user := &models.User{
		ID:       1,
		Username: "testuser",
		Email:    "<EMAIL>",
		Status:   models.UserStatusActive,
	}

	// 设置模拟期望（允许多次调用）
	suite.userRepo.On("GetByID", suite.ctx, uint(1)).Return(user, nil).Times(10)

	// 并发测试
	done := make(chan bool, 10)
	for i := 0; i < 10; i++ {
		go func() {
			defer func() { done <- true }()
			result, err := suite.userService.GetUser(suite.ctx, 1)
			assert.NoError(suite.T(), err)
			assert.NotNil(suite.T(), result)
		}()
	}

	// 等待所有goroutine完成
	for i := 0; i < 10; i++ {
		<-done
	}
}

// 运行测试套件
func TestUserServiceTestSuite(t *testing.T) {
	suite.Run(t, new(UserServiceTestSuite))
}
