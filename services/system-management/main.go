// 自动驾驶开发加速系统 - 系统管理服务
//
// 本服务负责整个系统的管理和监控，包括：
// - 服务注册和发现
// - 健康检查和监控
// - 配置管理
// - 用户认证和授权

package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/joho/godotenv"
	"go.uber.org/zap"

	"autodriving/services/system-management/internal/config"
	"autodriving/services/system-management/internal/database"
	"autodriving/services/system-management/internal/dds"
	"autodriving/services/system-management/internal/handlers"
	"autodriving/services/system-management/internal/middleware"
	"autodriving/services/system-management/internal/services"
)

// 应用程序结构体
type Application struct {
	config     *config.Config
	logger     *zap.Logger
	db         *database.Database
	ddsManager *dds.Manager
	router     *gin.Engine
	server     *http.Server
}

// 初始化应用程序
func NewApplication() (*Application, error) {
	// 加载环境变量
	if err := godotenv.Load(); err != nil {
		log.Printf("警告: 无法加载 .env 文件: %v", err)
	}

	// 初始化配置
	cfg, err := config.Load()
	if err != nil {
		return nil, fmt.Errorf("加载配置失败: %w", err)
	}

	// 初始化日志器
	logger, err := initLogger(cfg.LogLevel)
	if err != nil {
		return nil, fmt.Errorf("初始化日志器失败: %w", err)
	}

	// 初始化数据库连接
	db, err := database.New(cfg.Database, logger)
	if err != nil {
		return nil, fmt.Errorf("初始化数据库失败: %w", err)
	}

	// 初始化DDS管理器
	ddsManager, err := dds.NewManager(cfg.DDS, logger)
	if err != nil {
		return nil, fmt.Errorf("初始化DDS管理器失败: %w", err)
	}

	// 初始化Gin路由器
	if cfg.Environment == "production" {
		gin.SetMode(gin.ReleaseMode)
	}
	router := gin.New()

	app := &Application{
		config:     cfg,
		logger:     logger,
		db:         db,
		ddsManager: ddsManager,
		router:     router,
	}

	// 设置路由和中间件
	app.setupRoutes()

	// 创建HTTP服务器
	app.server = &http.Server{
		Addr:         fmt.Sprintf(":%d", cfg.Server.Port),
		Handler:      app.router,
		ReadTimeout:  time.Duration(cfg.Server.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(cfg.Server.WriteTimeout) * time.Second,
		IdleTimeout:  time.Duration(cfg.Server.IdleTimeout) * time.Second,
	}

	return app, nil
}

// 初始化日志器
func initLogger(level string) (*zap.Logger, error) {
	var config zap.Config

	switch level {
	case "debug":
		config = zap.NewDevelopmentConfig()
	case "production":
		config = zap.NewProductionConfig()
	default:
		config = zap.NewDevelopmentConfig()
	}

	// 设置中文时间格式
	config.EncoderConfig.TimeKey = "时间"
	config.EncoderConfig.LevelKey = "级别"
	config.EncoderConfig.MessageKey = "消息"
	config.EncoderConfig.CallerKey = "调用者"

	return config.Build()
}

// 设置路由和中间件
func (app *Application) setupRoutes() {
	// 全局中间件
	app.router.Use(middleware.Logger(app.logger))
	app.router.Use(middleware.Recovery(app.logger))
	app.router.Use(middleware.CORS())
	app.router.Use(middleware.RequestID())

	// 初始化服务层
	authService := services.NewAuthService(app.db, app.config.JWT, app.logger)
	serviceRegistryService := services.NewServiceRegistryService(app.db, app.ddsManager, app.logger)
	configService := services.NewConfigService(app.db, app.ddsManager, app.logger)
	healthService := services.NewHealthService(app.db, app.ddsManager, app.logger)

	// 初始化处理器
	authHandler := handlers.NewAuthHandler(authService, app.logger)
	serviceHandler := handlers.NewServiceHandler(serviceRegistryService, app.logger)
	configHandler := handlers.NewConfigHandler(configService, app.logger)
	healthHandler := handlers.NewHealthHandler(healthService, app.logger)

	// API版本分组
	v1 := app.router.Group("/api/v1")

	// 健康检查路由（无需认证）
	v1.GET("/health", healthHandler.GetHealth)
	v1.GET("/health/ready", healthHandler.GetReadiness)
	v1.GET("/health/live", healthHandler.GetLiveness)

	// 认证路由
	auth := v1.Group("/auth")
	{
		auth.POST("/login", authHandler.Login)
		auth.POST("/logout", authHandler.Logout)
		auth.POST("/refresh", authHandler.RefreshToken)
		auth.GET("/profile", middleware.Auth(authService), authHandler.GetProfile)
	}

	// 服务管理路由（需要认证）
	services := v1.Group("/services")
	services.Use(middleware.Auth(authService))
	{
		services.GET("", serviceHandler.ListServices)
		services.GET("/:id", serviceHandler.GetService)
		services.POST("", serviceHandler.RegisterService)
		services.PUT("/:id", serviceHandler.UpdateService)
		services.DELETE("/:id", serviceHandler.UnregisterService)
		services.POST("/:id/health", serviceHandler.UpdateServiceHealth)
	}

	// 配置管理路由（需要认证和权限）
	configs := v1.Group("/configs")
	configs.Use(middleware.Auth(authService))
	configs.Use(middleware.Permission("config:read"))
	{
		configs.GET("", configHandler.ListConfigs)
		configs.GET("/:key", configHandler.GetConfig)
		
		// 写操作需要额外权限
		writeConfigs := configs.Group("")
		writeConfigs.Use(middleware.Permission("config:write"))
		{
			writeConfigs.POST("", configHandler.CreateConfig)
			writeConfigs.PUT("/:key", configHandler.UpdateConfig)
			writeConfigs.DELETE("/:key", configHandler.DeleteConfig)
		}
	}

	// 系统监控路由
	monitoring := v1.Group("/monitoring")
	monitoring.Use(middleware.Auth(authService))
	{
		monitoring.GET("/metrics", healthHandler.GetMetrics)
		monitoring.GET("/logs", healthHandler.GetLogs)
		monitoring.GET("/events", healthHandler.GetEvents)
	}

	// WebSocket路由（用于实时通信）
	ws := v1.Group("/ws")
	ws.Use(middleware.Auth(authService))
	{
		ws.GET("/events", healthHandler.HandleWebSocket)
	}

	// 静态文件服务（开发环境）
	if app.config.Environment != "production" {
		app.router.Static("/static", "./static")
		app.router.StaticFile("/", "./static/index.html")
	}
}

// 启动应用程序
func (app *Application) Start() error {
	app.logger.Info("正在启动系统管理服务",
		zap.String("环境", app.config.Environment),
		zap.Int("端口", app.config.Server.Port),
	)

	// 启动DDS管理器
	if err := app.ddsManager.Start(); err != nil {
		return fmt.Errorf("启动DDS管理器失败: %w", err)
	}

	// 启动HTTP服务器
	go func() {
		if err := app.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			app.logger.Fatal("HTTP服务器启动失败", zap.Error(err))
		}
	}()

	app.logger.Info("系统管理服务启动成功",
		zap.String("地址", fmt.Sprintf("http://localhost:%d", app.config.Server.Port)),
	)

	return nil
}

// 优雅关闭应用程序
func (app *Application) Shutdown() error {
	app.logger.Info("正在关闭系统管理服务...")

	// 创建关闭上下文
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 关闭HTTP服务器
	if err := app.server.Shutdown(ctx); err != nil {
		app.logger.Error("HTTP服务器关闭失败", zap.Error(err))
	}

	// 关闭DDS管理器
	if err := app.ddsManager.Stop(); err != nil {
		app.logger.Error("DDS管理器关闭失败", zap.Error(err))
	}

	// 关闭数据库连接
	if err := app.db.Close(); err != nil {
		app.logger.Error("数据库连接关闭失败", zap.Error(err))
	}

	// 同步日志
	app.logger.Sync()

	app.logger.Info("系统管理服务已关闭")
	return nil
}

// 主函数
func main() {
	// 创建应用程序实例
	app, err := NewApplication()
	if err != nil {
		log.Fatalf("创建应用程序失败: %v", err)
	}

	// 启动应用程序
	if err := app.Start(); err != nil {
		app.logger.Fatal("启动应用程序失败", zap.Error(err))
	}

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	// 优雅关闭
	if err := app.Shutdown(); err != nil {
		log.Printf("关闭应用程序失败: %v", err)
		os.Exit(1)
	}
}
