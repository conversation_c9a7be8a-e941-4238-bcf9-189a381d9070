// 自动驾驶开发加速系统 - 系统管理服务配置
package config

import (
	"fmt"
	"os"
	"strconv"
	"time"
)

// Config 系统配置结构
type Config struct {
	// 基础配置
	Environment string `json:"environment" yaml:"environment"`
	Version     string `json:"version" yaml:"version"`
	
	// 服务配置
	HTTP    HTTPConfig    `json:"http" yaml:"http"`
	GRPC    GRPCConfig    `json:"grpc" yaml:"grpc"`
	Metrics MetricsConfig `json:"metrics" yaml:"metrics"`
	
	// 数据存储配置
	Database DatabaseConfig `json:"database" yaml:"database"`
	Redis    RedisConfig    `json:"redis" yaml:"redis"`
	
	// 认证配置
	JWT JWTConfig `json:"jwt" yaml:"jwt"`
	
	// 日志配置
	Log LogConfig `json:"log" yaml:"log"`
	
	// 服务发现配置
	ServiceDiscovery ServiceDiscoveryConfig `json:"service_discovery" yaml:"service_discovery"`
}

// HTTPConfig HTTP服务器配置
type HTTPConfig struct {
	Address      string `json:"address" yaml:"address"`
	Port         int    `json:"port" yaml:"port"`
	ReadTimeout  int    `json:"read_timeout" yaml:"read_timeout"`
	WriteTimeout int    `json:"write_timeout" yaml:"write_timeout"`
	IdleTimeout  int    `json:"idle_timeout" yaml:"idle_timeout"`
}

// GRPCConfig gRPC服务器配置
type GRPCConfig struct {
	Address string `json:"address" yaml:"address"`
	Port    int    `json:"port" yaml:"port"`
}

// MetricsConfig 监控配置
type MetricsConfig struct {
	Port int `json:"port" yaml:"port"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Host            string `json:"host" yaml:"host"`
	Port            int    `json:"port" yaml:"port"`
	Database        string `json:"database" yaml:"database"`
	Username        string `json:"username" yaml:"username"`
	Password        string `json:"password" yaml:"password"`
	SSLMode         string `json:"ssl_mode" yaml:"ssl_mode"`
	MaxOpenConns    int    `json:"max_open_conns" yaml:"max_open_conns"`
	MaxIdleConns    int    `json:"max_idle_conns" yaml:"max_idle_conns"`
	ConnMaxLifetime int    `json:"conn_max_lifetime" yaml:"conn_max_lifetime"`
	
	// 读写分离配置
	ReadOnlyHost string `json:"read_only_host" yaml:"read_only_host"`
	ReadOnlyPort int    `json:"read_only_port" yaml:"read_only_port"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host     string `json:"host" yaml:"host"`
	Port     int    `json:"port" yaml:"port"`
	Password string `json:"password" yaml:"password"`
	Database int    `json:"database" yaml:"database"`
	
	// 连接池配置
	MaxRetries      int           `json:"max_retries" yaml:"max_retries"`
	PoolSize        int           `json:"pool_size" yaml:"pool_size"`
	MinIdleConns    int           `json:"min_idle_conns" yaml:"min_idle_conns"`
	PoolTimeout     time.Duration `json:"pool_timeout" yaml:"pool_timeout"`
	IdleTimeout     time.Duration `json:"idle_timeout" yaml:"idle_timeout"`
	IdleCheckFreq   time.Duration `json:"idle_check_freq" yaml:"idle_check_freq"`
	
	// 哨兵配置（高可用）
	SentinelEnabled   bool     `json:"sentinel_enabled" yaml:"sentinel_enabled"`
	SentinelAddresses []string `json:"sentinel_addresses" yaml:"sentinel_addresses"`
	MasterName        string   `json:"master_name" yaml:"master_name"`
}

// JWTConfig JWT认证配置
type JWTConfig struct {
	SecretKey       string        `json:"secret_key" yaml:"secret_key"`
	AccessTokenTTL  time.Duration `json:"access_token_ttl" yaml:"access_token_ttl"`
	RefreshTokenTTL time.Duration `json:"refresh_token_ttl" yaml:"refresh_token_ttl"`
	Issuer          string        `json:"issuer" yaml:"issuer"`
	Algorithm       string        `json:"algorithm" yaml:"algorithm"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level      string `json:"level" yaml:"level"`
	Format     string `json:"format" yaml:"format"`
	Output     string `json:"output" yaml:"output"`
	MaxSize    int    `json:"max_size" yaml:"max_size"`
	MaxBackups int    `json:"max_backups" yaml:"max_backups"`
	MaxAge     int    `json:"max_age" yaml:"max_age"`
	Compress   bool   `json:"compress" yaml:"compress"`
}

// ServiceDiscoveryConfig 服务发现配置
type ServiceDiscoveryConfig struct {
	Enabled         bool          `json:"enabled" yaml:"enabled"`
	HeartbeatTTL    time.Duration `json:"heartbeat_ttl" yaml:"heartbeat_ttl"`
	CheckInterval   time.Duration `json:"check_interval" yaml:"check_interval"`
	CleanupInterval time.Duration `json:"cleanup_interval" yaml:"cleanup_interval"`
}

// Load 加载配置
func Load() (*Config, error) {
	config := &Config{
		Environment: getEnv("ENVIRONMENT", "development"),
		Version:     getEnv("VERSION", "1.0.0"),
		
		HTTP: HTTPConfig{
			Address:      getEnv("HTTP_ADDRESS", ":8080"),
			Port:         getEnvInt("HTTP_PORT", 8080),
			ReadTimeout:  getEnvInt("HTTP_READ_TIMEOUT", 30),
			WriteTimeout: getEnvInt("HTTP_WRITE_TIMEOUT", 30),
			IdleTimeout:  getEnvInt("HTTP_IDLE_TIMEOUT", 120),
		},
		
		GRPC: GRPCConfig{
			Address: getEnv("GRPC_ADDRESS", ":9090"),
			Port:    getEnvInt("GRPC_PORT", 9090),
		},
		
		Metrics: MetricsConfig{
			Port: getEnvInt("METRICS_PORT", 9100),
		},
		
		Database: DatabaseConfig{
			Host:            getEnv("DB_HOST", "localhost"),
			Port:            getEnvInt("DB_PORT", 5432),
			Database:        getEnv("DB_NAME", "autodriving"),
			Username:        getEnv("DB_USER", "autodriving"),
			Password:        getEnv("DB_PASSWORD", ""),
			SSLMode:         getEnv("DB_SSL_MODE", "disable"),
			MaxOpenConns:    getEnvInt("DB_MAX_OPEN_CONNS", 25),
			MaxIdleConns:    getEnvInt("DB_MAX_IDLE_CONNS", 5),
			ConnMaxLifetime: getEnvInt("DB_CONN_MAX_LIFETIME", 300),
			ReadOnlyHost:    getEnv("DB_READ_ONLY_HOST", ""),
			ReadOnlyPort:    getEnvInt("DB_READ_ONLY_PORT", 5432),
		},
		
		Redis: RedisConfig{
			Host:              getEnv("REDIS_HOST", "localhost"),
			Port:              getEnvInt("REDIS_PORT", 6379),
			Password:          getEnv("REDIS_PASSWORD", ""),
			Database:          getEnvInt("REDIS_DB", 0),
			MaxRetries:        getEnvInt("REDIS_MAX_RETRIES", 3),
			PoolSize:          getEnvInt("REDIS_POOL_SIZE", 10),
			MinIdleConns:      getEnvInt("REDIS_MIN_IDLE_CONNS", 2),
			PoolTimeout:       getEnvDuration("REDIS_POOL_TIMEOUT", 4*time.Second),
			IdleTimeout:       getEnvDuration("REDIS_IDLE_TIMEOUT", 5*time.Minute),
			IdleCheckFreq:     getEnvDuration("REDIS_IDLE_CHECK_FREQ", 1*time.Minute),
			SentinelEnabled:   getEnvBool("REDIS_SENTINEL_ENABLED", false),
			SentinelAddresses: getEnvStringSlice("REDIS_SENTINEL_ADDRESSES", []string{}),
			MasterName:        getEnv("REDIS_MASTER_NAME", "mymaster"),
		},
		
		JWT: JWTConfig{
			SecretKey:       getEnv("JWT_SECRET_KEY", "your-secret-key"),
			AccessTokenTTL:  getEnvDuration("JWT_ACCESS_TOKEN_TTL", 1*time.Hour),
			RefreshTokenTTL: getEnvDuration("JWT_REFRESH_TOKEN_TTL", 24*time.Hour),
			Issuer:          getEnv("JWT_ISSUER", "autodriving-system"),
			Algorithm:       getEnv("JWT_ALGORITHM", "HS256"),
		},
		
		Log: LogConfig{
			Level:      getEnv("LOG_LEVEL", "info"),
			Format:     getEnv("LOG_FORMAT", "json"),
			Output:     getEnv("LOG_OUTPUT", "stdout"),
			MaxSize:    getEnvInt("LOG_MAX_SIZE", 100),
			MaxBackups: getEnvInt("LOG_MAX_BACKUPS", 3),
			MaxAge:     getEnvInt("LOG_MAX_AGE", 28),
			Compress:   getEnvBool("LOG_COMPRESS", true),
		},
		
		ServiceDiscovery: ServiceDiscoveryConfig{
			Enabled:         getEnvBool("SERVICE_DISCOVERY_ENABLED", true),
			HeartbeatTTL:    getEnvDuration("SERVICE_DISCOVERY_HEARTBEAT_TTL", 30*time.Second),
			CheckInterval:   getEnvDuration("SERVICE_DISCOVERY_CHECK_INTERVAL", 10*time.Second),
			CleanupInterval: getEnvDuration("SERVICE_DISCOVERY_CLEANUP_INTERVAL", 1*time.Minute),
		},
	}
	
	// 验证配置
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("配置验证失败: %w", err)
	}
	
	return config, nil
}

// Validate 验证配置
func (c *Config) Validate() error {
	if c.Database.Password == "" {
		return fmt.Errorf("数据库密码不能为空")
	}
	
	if c.JWT.SecretKey == "" || c.JWT.SecretKey == "your-secret-key" {
		return fmt.Errorf("JWT密钥必须设置且不能使用默认值")
	}
	
	if c.JWT.AccessTokenTTL <= 0 {
		return fmt.Errorf("JWT访问令牌TTL必须大于0")
	}
	
	return nil
}

// GetDSN 获取数据库连接字符串
func (c *Config) GetDSN() string {
	return fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
		c.Database.Host, c.Database.Port, c.Database.Username,
		c.Database.Password, c.Database.Database, c.Database.SSLMode)
}

// GetReadOnlyDSN 获取只读数据库连接字符串
func (c *Config) GetReadOnlyDSN() string {
	host := c.Database.ReadOnlyHost
	port := c.Database.ReadOnlyPort
	
	if host == "" {
		host = c.Database.Host
		port = c.Database.Port
	}
	
	return fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
		host, port, c.Database.Username,
		c.Database.Password, c.Database.Database, c.Database.SSLMode)
}

// 辅助函数

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getEnvInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getEnvBool(key string, defaultValue bool) bool {
	if value := os.Getenv(key); value != "" {
		if boolValue, err := strconv.ParseBool(value); err == nil {
			return boolValue
		}
	}
	return defaultValue
}

func getEnvDuration(key string, defaultValue time.Duration) time.Duration {
	if value := os.Getenv(key); value != "" {
		if duration, err := time.ParseDuration(value); err == nil {
			return duration
		}
	}
	return defaultValue
}

func getEnvStringSlice(key string, defaultValue []string) []string {
	// 简化实现，实际可以支持逗号分隔的字符串解析
	if value := os.Getenv(key); value != "" {
		// 这里可以实现字符串分割逻辑
		return []string{value}
	}
	return defaultValue
}
