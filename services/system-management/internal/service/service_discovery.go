// 自动驾驶开发加速系统 - 服务发现
package service

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"sync"
	"time"

	"autodriving/services/system-management/internal/config"
	"autodriving/services/system-management/internal/model"
	"autodriving/services/system-management/internal/repository"
)

// ServiceDiscovery 服务发现接口
type ServiceDiscovery interface {
	// 注册服务
	RegisterService(ctx context.Context, service *model.ServiceInfo) error
	
	// 注销服务
	UnregisterService(ctx context.Context, serviceID string) error
	
	// 获取服务列表
	GetServices(ctx context.Context) ([]*model.ServiceInfo, error)
	
	// 获取特定类型的服务
	GetServicesByType(ctx context.Context, serviceType string) ([]*model.ServiceInfo, error)
	
	// 获取健康的服务
	GetHealthyServices(ctx context.Context, serviceType string) ([]*model.ServiceInfo, error)
	
	// 检查服务健康状态
	CheckServiceHealth(ctx context.Context, serviceID string) (*model.HealthStatus, error)
	
	// 启动服务发现
	Start(ctx context.Context) error
	
	// 停止服务发现
	Stop() error
}

// serviceDiscovery 服务发现实现
type serviceDiscovery struct {
	serviceRepo repository.ServiceRepository
	config      config.ServiceDiscoveryConfig
	
	// 内部状态
	mu       sync.RWMutex
	services map[string]*model.ServiceInfo
	running  bool
	stopCh   chan struct{}
}

// NewServiceDiscovery 创建服务发现实例
func NewServiceDiscovery(serviceRepo repository.ServiceRepository) ServiceDiscovery {
	return &serviceDiscovery{
		serviceRepo: serviceRepo,
		services:    make(map[string]*model.ServiceInfo),
		stopCh:      make(chan struct{}),
	}
}

// RegisterService 注册服务
func (sd *serviceDiscovery) RegisterService(ctx context.Context, service *model.ServiceInfo) error {
	// 验证服务信息
	if err := sd.validateServiceInfo(service); err != nil {
		return fmt.Errorf("服务信息验证失败: %w", err)
	}
	
	// 设置注册时间
	service.RegisteredAt = time.Now()
	service.LastHeartbeat = time.Now()
	service.Status = "healthy"
	
	// 保存到存储
	if err := sd.serviceRepo.Register(ctx, service); err != nil {
		return fmt.Errorf("注册服务失败: %w", err)
	}
	
	// 更新内存缓存
	sd.mu.Lock()
	sd.services[service.ID] = service
	sd.mu.Unlock()
	
	log.Printf("服务注册成功: %s (%s)", service.Name, service.ID)
	return nil
}

// UnregisterService 注销服务
func (sd *serviceDiscovery) UnregisterService(ctx context.Context, serviceID string) error {
	// 从存储中删除
	if err := sd.serviceRepo.Unregister(ctx, serviceID); err != nil {
		return fmt.Errorf("注销服务失败: %w", err)
	}
	
	// 从内存缓存中删除
	sd.mu.Lock()
	delete(sd.services, serviceID)
	sd.mu.Unlock()
	
	log.Printf("服务注销成功: %s", serviceID)
	return nil
}

// GetServices 获取所有服务
func (sd *serviceDiscovery) GetServices(ctx context.Context) ([]*model.ServiceInfo, error) {
	services, err := sd.serviceRepo.GetAll(ctx)
	if err != nil {
		return nil, fmt.Errorf("获取服务列表失败: %w", err)
	}
	
	// 更新内存缓存
	sd.mu.Lock()
	sd.services = make(map[string]*model.ServiceInfo)
	for _, service := range services {
		sd.services[service.ID] = service
	}
	sd.mu.Unlock()
	
	return services, nil
}

// GetServicesByType 获取特定类型的服务
func (sd *serviceDiscovery) GetServicesByType(ctx context.Context, serviceType string) ([]*model.ServiceInfo, error) {
	services, err := sd.serviceRepo.GetByType(ctx, serviceType)
	if err != nil {
		return nil, fmt.Errorf("获取服务列表失败: %w", err)
	}
	
	return services, nil
}

// GetHealthyServices 获取健康的服务
func (sd *serviceDiscovery) GetHealthyServices(ctx context.Context, serviceType string) ([]*model.ServiceInfo, error) {
	services, err := sd.GetServicesByType(ctx, serviceType)
	if err != nil {
		return nil, err
	}
	
	var healthyServices []*model.ServiceInfo
	for _, service := range services {
		if service.Status == "healthy" {
			healthyServices = append(healthyServices, service)
		}
	}
	
	return healthyServices, nil
}

// CheckServiceHealth 检查服务健康状态
func (sd *serviceDiscovery) CheckServiceHealth(ctx context.Context, serviceID string) (*model.HealthStatus, error) {
	// 从缓存获取服务信息
	sd.mu.RLock()
	service, exists := sd.services[serviceID]
	sd.mu.RUnlock()
	
	if !exists {
		// 从存储获取
		var err error
		service, err = sd.serviceRepo.GetByID(ctx, serviceID)
		if err != nil {
			return nil, fmt.Errorf("服务不存在: %w", err)
		}
	}
	
	// 执行健康检查
	healthStatus := &model.HealthStatus{
		ServiceID:   serviceID,
		Status:      "unknown",
		CheckedAt:   time.Now(),
		ResponseTime: 0,
	}
	
	// 检查心跳超时
	if time.Since(service.LastHeartbeat) > sd.config.HeartbeatTTL {
		healthStatus.Status = "unhealthy"
		healthStatus.Message = "心跳超时"
	} else {
		// 这里可以添加更复杂的健康检查逻辑
		// 比如HTTP健康检查、TCP连接检查等
		healthStatus.Status = "healthy"
		healthStatus.Message = "服务正常"
	}
	
	// 更新服务状态
	service.Status = healthStatus.Status
	service.LastHealthCheck = healthStatus.CheckedAt
	
	// 保存状态更新
	if err := sd.serviceRepo.UpdateStatus(ctx, serviceID, healthStatus.Status); err != nil {
		log.Printf("更新服务状态失败: %v", err)
	}
	
	return healthStatus, nil
}

// Start 启动服务发现
func (sd *serviceDiscovery) Start(ctx context.Context) error {
	if !sd.config.Enabled {
		log.Println("服务发现已禁用")
		return nil
	}
	
	sd.mu.Lock()
	if sd.running {
		sd.mu.Unlock()
		return fmt.Errorf("服务发现已在运行")
	}
	sd.running = true
	sd.mu.Unlock()
	
	log.Println("启动服务发现...")
	
	// 启动健康检查协程
	go sd.healthCheckLoop(ctx)
	
	// 启动清理协程
	go sd.cleanupLoop(ctx)
	
	log.Println("服务发现启动成功")
	return nil
}

// Stop 停止服务发现
func (sd *serviceDiscovery) Stop() error {
	sd.mu.Lock()
	if !sd.running {
		sd.mu.Unlock()
		return nil
	}
	sd.running = false
	sd.mu.Unlock()
	
	close(sd.stopCh)
	log.Println("服务发现已停止")
	return nil
}

// healthCheckLoop 健康检查循环
func (sd *serviceDiscovery) healthCheckLoop(ctx context.Context) {
	ticker := time.NewTicker(sd.config.CheckInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-ctx.Done():
			return
		case <-sd.stopCh:
			return
		case <-ticker.C:
			sd.performHealthChecks(ctx)
		}
	}
}

// cleanupLoop 清理循环
func (sd *serviceDiscovery) cleanupLoop(ctx context.Context) {
	ticker := time.NewTicker(sd.config.CleanupInterval)
	defer ticker.Stop()
	
	for {
		select {
		case <-ctx.Done():
			return
		case <-sd.stopCh:
			return
		case <-ticker.C:
			sd.cleanupExpiredServices(ctx)
		}
	}
}

// performHealthChecks 执行健康检查
func (sd *serviceDiscovery) performHealthChecks(ctx context.Context) {
	services, err := sd.GetServices(ctx)
	if err != nil {
		log.Printf("获取服务列表失败: %v", err)
		return
	}
	
	for _, service := range services {
		// 异步检查每个服务的健康状态
		go func(svc *model.ServiceInfo) {
			if _, err := sd.CheckServiceHealth(ctx, svc.ID); err != nil {
				log.Printf("健康检查失败 [%s]: %v", svc.ID, err)
			}
		}(service)
	}
}

// cleanupExpiredServices 清理过期服务
func (sd *serviceDiscovery) cleanupExpiredServices(ctx context.Context) {
	services, err := sd.GetServices(ctx)
	if err != nil {
		log.Printf("获取服务列表失败: %v", err)
		return
	}
	
	expiredThreshold := time.Now().Add(-sd.config.HeartbeatTTL * 3) // 3倍心跳时间
	
	for _, service := range services {
		if service.LastHeartbeat.Before(expiredThreshold) {
			log.Printf("清理过期服务: %s (%s)", service.Name, service.ID)
			if err := sd.UnregisterService(ctx, service.ID); err != nil {
				log.Printf("清理过期服务失败: %v", err)
			}
		}
	}
}

// validateServiceInfo 验证服务信息
func (sd *serviceDiscovery) validateServiceInfo(service *model.ServiceInfo) error {
	if service.ID == "" {
		return fmt.Errorf("服务ID不能为空")
	}
	
	if service.Name == "" {
		return fmt.Errorf("服务名称不能为空")
	}
	
	if service.Type == "" {
		return fmt.Errorf("服务类型不能为空")
	}
	
	if service.Host == "" {
		return fmt.Errorf("服务主机不能为空")
	}
	
	if service.Port <= 0 || service.Port > 65535 {
		return fmt.Errorf("服务端口无效")
	}
	
	return nil
}

// UpdateHeartbeat 更新服务心跳
func (sd *serviceDiscovery) UpdateHeartbeat(ctx context.Context, serviceID string) error {
	now := time.Now()
	
	// 更新存储中的心跳时间
	if err := sd.serviceRepo.UpdateHeartbeat(ctx, serviceID, now); err != nil {
		return fmt.Errorf("更新心跳失败: %w", err)
	}
	
	// 更新内存缓存
	sd.mu.Lock()
	if service, exists := sd.services[serviceID]; exists {
		service.LastHeartbeat = now
		service.Status = "healthy"
	}
	sd.mu.Unlock()
	
	return nil
}

// GetServiceEndpoints 获取服务端点
func (sd *serviceDiscovery) GetServiceEndpoints(ctx context.Context, serviceType string) ([]string, error) {
	services, err := sd.GetHealthyServices(ctx, serviceType)
	if err != nil {
		return nil, err
	}
	
	var endpoints []string
	for _, service := range services {
		endpoint := fmt.Sprintf("%s:%d", service.Host, service.Port)
		endpoints = append(endpoints, endpoint)
	}
	
	return endpoints, nil
}
