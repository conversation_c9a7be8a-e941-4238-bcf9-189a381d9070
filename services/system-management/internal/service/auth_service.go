// 自动驾驶开发加速系统 - 认证服务
package service

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"golang.org/x/crypto/bcrypt"

	"autodriving/services/system-management/internal/config"
	"autodriving/services/system-management/internal/model"
	"autodriving/services/system-management/internal/repository"
)

// AuthService 认证服务接口
type AuthService interface {
	// 用户登录
	Login(ctx context.Context, username, password string) (*LoginResponse, error)
	
	// 用户登出
	Logout(ctx context.Context, token string) error
	
	// 刷新令牌
	RefreshToken(ctx context.Context, refreshToken string) (*LoginResponse, error)
	
	// 验证令牌
	ValidateToken(ctx context.Context, token string) (*model.User, error)
	
	// 生成令牌
	GenerateTokens(ctx context.Context, user *model.User) (*LoginResponse, error)
}

// authService 认证服务实现
type authService struct {
	userRepo    repository.UserRepository
	sessionRepo repository.SessionRepository
	jwtConfig   config.JWTConfig
}

// LoginResponse 登录响应
type LoginResponse struct {
	AccessToken  string    `json:"access_token"`
	RefreshToken string    `json:"refresh_token"`
	TokenType    string    `json:"token_type"`
	ExpiresIn    int64     `json:"expires_in"`
	User         *UserInfo `json:"user"`
}

// UserInfo 用户信息
type UserInfo struct {
	ID       string `json:"id"`
	Username string `json:"username"`
	Email    string `json:"email"`
	FullName string `json:"full_name"`
	Role     string `json:"role"`
	Status   string `json:"status"`
}

// JWTClaims JWT声明
type JWTClaims struct {
	UserID   string `json:"user_id"`
	Username string `json:"username"`
	Email    string `json:"email"`
	Role     string `json:"role"`
	jwt.RegisteredClaims
}

// NewAuthService 创建认证服务
func NewAuthService(userRepo repository.UserRepository, sessionRepo repository.SessionRepository, 
	jwtConfig config.JWTConfig) AuthService {
	return &authService{
		userRepo:    userRepo,
		sessionRepo: sessionRepo,
		jwtConfig:   jwtConfig,
	}
}

// Login 用户登录
func (s *authService) Login(ctx context.Context, username, password string) (*LoginResponse, error) {
	// 查找用户
	user, err := s.userRepo.GetByUsername(ctx, username)
	if err != nil {
		return nil, fmt.Errorf("用户名或密码错误")
	}
	
	// 检查用户状态
	if user.Status != "active" {
		return nil, fmt.Errorf("用户账户已被禁用")
	}
	
	// 验证密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.PasswordHash), []byte(password)); err != nil {
		return nil, fmt.Errorf("用户名或密码错误")
	}
	
	// 更新最后登录时间
	user.LastLoginAt = time.Now()
	if err := s.userRepo.Update(ctx, user); err != nil {
		// 记录错误但不影响登录流程
		fmt.Printf("更新用户最后登录时间失败: %v", err)
	}
	
	// 生成令牌
	response, err := s.GenerateTokens(ctx, user)
	if err != nil {
		return nil, fmt.Errorf("生成令牌失败: %w", err)
	}
	
	return response, nil
}

// Logout 用户登出
func (s *authService) Logout(ctx context.Context, token string) error {
	// 解析令牌获取会话信息
	claims, err := s.parseToken(token)
	if err != nil {
		return fmt.Errorf("无效的令牌")
	}
	
	// 删除会话
	sessionID := claims.ID
	if err := s.sessionRepo.Delete(ctx, sessionID); err != nil {
		return fmt.Errorf("删除会话失败: %w", err)
	}
	
	return nil
}

// RefreshToken 刷新令牌
func (s *authService) RefreshToken(ctx context.Context, refreshToken string) (*LoginResponse, error) {
	// 解析刷新令牌
	claims, err := s.parseToken(refreshToken)
	if err != nil {
		return nil, fmt.Errorf("无效的刷新令牌")
	}
	
	// 检查是否为刷新令牌
	if claims.Subject != "refresh" {
		return nil, fmt.Errorf("令牌类型错误")
	}
	
	// 获取用户信息
	user, err := s.userRepo.GetByID(ctx, claims.UserID)
	if err != nil {
		return nil, fmt.Errorf("用户不存在")
	}
	
	// 检查用户状态
	if user.Status != "active" {
		return nil, fmt.Errorf("用户账户已被禁用")
	}
	
	// 删除旧的会话
	if err := s.sessionRepo.Delete(ctx, claims.ID); err != nil {
		// 记录错误但继续执行
		fmt.Printf("删除旧会话失败: %v", err)
	}
	
	// 生成新的令牌
	response, err := s.GenerateTokens(ctx, user)
	if err != nil {
		return nil, fmt.Errorf("生成新令牌失败: %w", err)
	}
	
	return response, nil
}

// ValidateToken 验证令牌
func (s *authService) ValidateToken(ctx context.Context, token string) (*model.User, error) {
	// 解析令牌
	claims, err := s.parseToken(token)
	if err != nil {
		return nil, fmt.Errorf("无效的令牌: %w", err)
	}
	
	// 检查是否为访问令牌
	if claims.Subject != "access" {
		return nil, fmt.Errorf("令牌类型错误")
	}
	
	// 检查会话是否存在
	exists, err := s.sessionRepo.Exists(ctx, claims.ID)
	if err != nil {
		return nil, fmt.Errorf("检查会话失败: %w", err)
	}
	if !exists {
		return nil, fmt.Errorf("会话已过期")
	}
	
	// 获取用户信息
	user, err := s.userRepo.GetByID(ctx, claims.UserID)
	if err != nil {
		return nil, fmt.Errorf("用户不存在: %w", err)
	}
	
	// 检查用户状态
	if user.Status != "active" {
		return nil, fmt.Errorf("用户账户已被禁用")
	}
	
	return user, nil
}

// GenerateTokens 生成令牌
func (s *authService) GenerateTokens(ctx context.Context, user *model.User) (*LoginResponse, error) {
	// 生成会话ID
	sessionID, err := s.generateSessionID()
	if err != nil {
		return nil, fmt.Errorf("生成会话ID失败: %w", err)
	}
	
	now := time.Now()
	
	// 生成访问令牌
	accessClaims := &JWTClaims{
		UserID:   user.ID,
		Username: user.Username,
		Email:    user.Email,
		Role:     user.Role,
		RegisteredClaims: jwt.RegisteredClaims{
			ID:        sessionID,
			Subject:   "access",
			Issuer:    s.jwtConfig.Issuer,
			IssuedAt:  jwt.NewNumericDate(now),
			ExpiresAt: jwt.NewNumericDate(now.Add(s.jwtConfig.AccessTokenTTL)),
		},
	}
	
	accessToken, err := s.signToken(accessClaims)
	if err != nil {
		return nil, fmt.Errorf("生成访问令牌失败: %w", err)
	}
	
	// 生成刷新令牌
	refreshClaims := &JWTClaims{
		UserID:   user.ID,
		Username: user.Username,
		Email:    user.Email,
		Role:     user.Role,
		RegisteredClaims: jwt.RegisteredClaims{
			ID:        sessionID,
			Subject:   "refresh",
			Issuer:    s.jwtConfig.Issuer,
			IssuedAt:  jwt.NewNumericDate(now),
			ExpiresAt: jwt.NewNumericDate(now.Add(s.jwtConfig.RefreshTokenTTL)),
		},
	}
	
	refreshToken, err := s.signToken(refreshClaims)
	if err != nil {
		return nil, fmt.Errorf("生成刷新令牌失败: %w", err)
	}
	
	// 保存会话信息
	session := &model.Session{
		ID:           sessionID,
		UserID:       user.ID,
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		ExpiresAt:    now.Add(s.jwtConfig.RefreshTokenTTL),
		CreatedAt:    now,
	}
	
	if err := s.sessionRepo.Create(ctx, session); err != nil {
		return nil, fmt.Errorf("保存会话失败: %w", err)
	}
	
	// 构造响应
	response := &LoginResponse{
		AccessToken:  accessToken,
		RefreshToken: refreshToken,
		TokenType:    "Bearer",
		ExpiresIn:    int64(s.jwtConfig.AccessTokenTTL.Seconds()),
		User: &UserInfo{
			ID:       user.ID,
			Username: user.Username,
			Email:    user.Email,
			FullName: user.FullName,
			Role:     user.Role,
			Status:   user.Status,
		},
	}
	
	return response, nil
}

// signToken 签名令牌
func (s *authService) signToken(claims *JWTClaims) (string, error) {
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(s.jwtConfig.SecretKey))
}

// parseToken 解析令牌
func (s *authService) parseToken(tokenString string) (*JWTClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &JWTClaims{}, func(token *jwt.Token) (interface{}, error) {
		// 验证签名方法
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("意外的签名方法: %v", token.Header["alg"])
		}
		return []byte(s.jwtConfig.SecretKey), nil
	})
	
	if err != nil {
		return nil, err
	}
	
	if claims, ok := token.Claims.(*JWTClaims); ok && token.Valid {
		return claims, nil
	}
	
	return nil, fmt.Errorf("无效的令牌")
}

// generateSessionID 生成会话ID
func (s *authService) generateSessionID() (string, error) {
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// HashPassword 哈希密码
func HashPassword(password string) (string, error) {
	bytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	return string(bytes), err
}
