# 自动驾驶开发加速系统 - 系统管理服务Docker镜像
# 多阶段构建，优化镜像大小和安全性

# 构建阶段
FROM golang:1.21-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装必要的包
RUN apk add --no-cache git ca-certificates tzdata

# 复制go mod文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY . .

# 构建应用
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
    -ldflags='-w -s -extldflags "-static"' \
    -a -installsuffix cgo \
    -o system-management \
    ./cmd/main.go

# 运行阶段
FROM scratch

# 维护者信息
LABEL maintainer="Autonomous Driving Team <<EMAIL>>"
LABEL description="自动驾驶开发加速系统 - 系统管理服务"
LABEL version="1.0.0"

# 从构建阶段复制必要文件
COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/
COPY --from=builder /usr/share/zoneinfo /usr/share/zoneinfo
COPY --from=builder /app/system-management /system-management

# 设置时区
ENV TZ=Asia/Shanghai

# 创建非root用户
USER 65534:65534

# 暴露端口
EXPOSE 8080 9090 9100

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD ["/system-management", "health-check"] || exit 1

# 启动命令
ENTRYPOINT ["/system-management"]
