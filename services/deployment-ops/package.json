{"name": "deployment-ops-service", "version": "1.0.0", "description": "自动驾驶开发加速系统 - 部署运维服务", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "nodemon src/index.ts", "build": "tsc", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "docker:build": "docker build -t deployment-ops-service .", "docker:run": "docker run -p 8080:8080 deployment-ops-service"}, "keywords": ["autonomous-driving", "deployment", "devops", "container", "harbor", "kubernetes"], "author": "Autonomous Driving Development Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0", "winston": "^3.11.0", "joi": "^17.11.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "axios": "^1.6.2", "ws": "^8.14.2", "node-cron": "^3.0.3", "dockerode": "^4.0.0", "kubernetes-client": "^10.0.0", "tar": "^6.2.0", "archiver": "^6.0.1", "yaml": "^2.3.4", "semver": "^7.5.4", "node-vault": "^0.10.2", "pg": "^8.11.3", "redis": "^4.6.10", "ioredis": "^5.3.2", "bull": "^4.12.0", "prometheus-api-metrics": "^3.2.2", "prom-client": "^15.0.0"}, "devDependencies": {"@types/node": "^20.9.0", "@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/ws": "^8.5.9", "@types/node-cron": "^3.0.11", "@types/dockerode": "^3.3.23", "@types/tar": "^6.1.8", "@types/archiver": "^6.0.2", "@types/semver": "^7.5.6", "@types/pg": "^8.10.7", "@types/jest": "^29.5.8", "@types/supertest": "^2.0.16", "typescript": "^5.2.2", "ts-node": "^10.9.1", "nodemon": "^3.0.1", "jest": "^29.7.0", "ts-jest": "^29.1.1", "supertest": "^6.3.3", "eslint": "^8.54.0", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "prettier": "^3.1.0", "husky": "^8.0.3", "lint-staged": "^15.1.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/company/autonomous-driving-platform.git"}, "bugs": {"url": "https://github.com/company/autonomous-driving-platform/issues"}, "homepage": "https://github.com/company/autonomous-driving-platform#readme", "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src", "<rootDir>/tests"], "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"], "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/index.ts"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}, "lint-staged": {"*.ts": ["eslint --fix", "prettier --write"]}, "husky": {"hooks": {"pre-commit": "lint-staged", "pre-push": "npm test"}}}