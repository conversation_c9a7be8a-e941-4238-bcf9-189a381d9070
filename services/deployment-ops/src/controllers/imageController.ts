// 自动驾驶开发加速系统 - 镜像管理控制器
import { Request, Response } from 'express';
import { ImageService, BuildConfig, PushConfig } from '../services/imageService';
import { logger } from '../utils/logger';
import { validateRequest } from '../middleware/validation';
import Jo<PERSON> from 'joi';

export class ImageController {
  private imageService: ImageService;

  constructor() {
    this.imageService = new ImageService();
  }

  /**
   * 构建镜像
   */
  buildImage = async (req: Request, res: Response): Promise<void> => {
    try {
      const { error, value } = this.validateBuildRequest(req.body);
      if (error) {
        res.status(400).json({
          success: false,
          message: '请求参数验证失败',
          errors: error.details.map(detail => detail.message),
        });
        return;
      }

      const { buildConfig, imageName, tag } = value;
      
      logger.info(`收到镜像构建请求: ${imageName}:${tag}`);

      const imageId = await this.imageService.buildImage(buildConfig, imageName, tag);

      res.json({
        success: true,
        message: '镜像构建成功',
        data: {
          imageId,
          imageName,
          tag,
        },
      });
    } catch (error: any) {
      logger.error('镜像构建失败:', error);
      res.status(500).json({
        success: false,
        message: '镜像构建失败',
        error: error.message,
      });
    }
  };

  /**
   * 推送镜像
   */
  pushImage = async (req: Request, res: Response): Promise<void> => {
    try {
      const { error, value } = this.validatePushRequest(req.body);
      if (error) {
        res.status(400).json({
          success: false,
          message: '请求参数验证失败',
          errors: error.details.map(detail => detail.message),
        });
        return;
      }

      const pushConfig: PushConfig = value;
      
      logger.info(`收到镜像推送请求: ${pushConfig.registry}/${pushConfig.repository}:${pushConfig.tag}`);

      await this.imageService.pushImage(pushConfig);

      res.json({
        success: true,
        message: '镜像推送成功',
        data: {
          registry: pushConfig.registry,
          repository: pushConfig.repository,
          tag: pushConfig.tag,
        },
      });
    } catch (error: any) {
      logger.error('镜像推送失败:', error);
      res.status(500).json({
        success: false,
        message: '镜像推送失败',
        error: error.message,
      });
    }
  };

  /**
   * 获取镜像列表
   */
  getImages = async (req: Request, res: Response): Promise<void> => {
    try {
      const { projectName } = req.params;
      const { repository, tag, page = 1, pageSize = 20 } = req.query;

      logger.info(`获取镜像列表: project=${projectName}, repository=${repository}, tag=${tag}`);

      const images = await this.imageService.getImages(
        projectName,
        repository as string,
        tag as string
      );

      // 分页处理
      const startIndex = (Number(page) - 1) * Number(pageSize);
      const endIndex = startIndex + Number(pageSize);
      const paginatedImages = images.slice(startIndex, endIndex);

      res.json({
        success: true,
        message: '获取镜像列表成功',
        data: {
          images: paginatedImages,
          pagination: {
            page: Number(page),
            pageSize: Number(pageSize),
            total: images.length,
            totalPages: Math.ceil(images.length / Number(pageSize)),
          },
        },
      });
    } catch (error: any) {
      logger.error('获取镜像列表失败:', error);
      res.status(500).json({
        success: false,
        message: '获取镜像列表失败',
        error: error.message,
      });
    }
  };

  /**
   * 删除镜像
   */
  deleteImage = async (req: Request, res: Response): Promise<void> => {
    try {
      const { projectName, repository, tag } = req.params;

      logger.info(`删除镜像: ${projectName}/${repository}:${tag}`);

      await this.imageService.deleteImage(projectName, repository, tag);

      res.json({
        success: true,
        message: '镜像删除成功',
        data: {
          projectName,
          repository,
          tag,
        },
      });
    } catch (error: any) {
      logger.error('镜像删除失败:', error);
      res.status(500).json({
        success: false,
        message: '镜像删除失败',
        error: error.message,
      });
    }
  };

  /**
   * 扫描镜像漏洞
   */
  scanImage = async (req: Request, res: Response): Promise<void> => {
    try {
      const { projectName, repository, tag } = req.params;

      logger.info(`扫描镜像漏洞: ${projectName}/${repository}:${tag}`);

      const scanLocation = await this.imageService.scanImage(projectName, repository, tag);

      res.json({
        success: true,
        message: '镜像扫描已启动',
        data: {
          projectName,
          repository,
          tag,
          scanLocation,
        },
      });
    } catch (error: any) {
      logger.error('镜像扫描失败:', error);
      res.status(500).json({
        success: false,
        message: '镜像扫描失败',
        error: error.message,
      });
    }
  };

  /**
   * 获取漏洞报告
   */
  getVulnerabilityReport = async (req: Request, res: Response): Promise<void> => {
    try {
      const { projectName, repository, tag } = req.params;

      logger.info(`获取漏洞报告: ${projectName}/${repository}:${tag}`);

      const report = await this.imageService.getVulnerabilityReport(projectName, repository, tag);

      res.json({
        success: true,
        message: '获取漏洞报告成功',
        data: {
          projectName,
          repository,
          tag,
          report,
        },
      });
    } catch (error: any) {
      logger.error('获取漏洞报告失败:', error);
      res.status(500).json({
        success: false,
        message: '获取漏洞报告失败',
        error: error.message,
      });
    }
  };

  /**
   * 获取镜像标签
   */
  getImageTags = async (req: Request, res: Response): Promise<void> => {
    try {
      const { projectName, repository } = req.params;

      logger.info(`获取镜像标签: ${projectName}/${repository}`);

      const tags = await this.imageService.getImageTags(projectName, repository);

      res.json({
        success: true,
        message: '获取镜像标签成功',
        data: {
          projectName,
          repository,
          tags,
        },
      });
    } catch (error: any) {
      logger.error('获取镜像标签失败:', error);
      res.status(500).json({
        success: false,
        message: '获取镜像标签失败',
        error: error.message,
      });
    }
  };

  /**
   * 复制镜像
   */
  copyImage = async (req: Request, res: Response): Promise<void> => {
    try {
      const { error, value } = this.validateCopyRequest(req.body);
      if (error) {
        res.status(400).json({
          success: false,
          message: '请求参数验证失败',
          errors: error.details.map(detail => detail.message),
        });
        return;
      }

      const {
        sourceProject,
        sourceRepository,
        sourceTag,
        targetProject,
        targetRepository,
        targetTag,
      } = value;

      logger.info(`复制镜像: ${sourceProject}/${sourceRepository}:${sourceTag} -> ${targetProject}/${targetRepository}:${targetTag}`);

      await this.imageService.copyImage(
        sourceProject,
        sourceRepository,
        sourceTag,
        targetProject,
        targetRepository,
        targetTag
      );

      res.json({
        success: true,
        message: '镜像复制成功',
        data: {
          source: `${sourceProject}/${sourceRepository}:${sourceTag}`,
          target: `${targetProject}/${targetRepository}:${targetTag}`,
        },
      });
    } catch (error: any) {
      logger.error('镜像复制失败:', error);
      res.status(500).json({
        success: false,
        message: '镜像复制失败',
        error: error.message,
      });
    }
  };

  /**
   * 清理旧镜像
   */
  cleanupOldImages = async (req: Request, res: Response): Promise<void> => {
    try {
      const { projectName, repository } = req.params;
      const { keepCount = 10 } = req.query;

      logger.info(`清理旧镜像: ${projectName}/${repository}, 保留数量: ${keepCount}`);

      const deletedCount = await this.imageService.cleanupOldImages(
        projectName,
        repository,
        Number(keepCount)
      );

      res.json({
        success: true,
        message: '清理旧镜像成功',
        data: {
          projectName,
          repository,
          deletedCount,
          keepCount: Number(keepCount),
        },
      });
    } catch (error: any) {
      logger.error('清理旧镜像失败:', error);
      res.status(500).json({
        success: false,
        message: '清理旧镜像失败',
        error: error.message,
      });
    }
  };

  /**
   * 验证构建请求
   */
  private validateBuildRequest(data: any) {
    const schema = Joi.object({
      buildConfig: Joi.object({
        dockerfile: Joi.string().required(),
        context: Joi.string().required(),
        buildArgs: Joi.object().pattern(Joi.string(), Joi.string()).optional(),
        labels: Joi.object().pattern(Joi.string(), Joi.string()).optional(),
        target: Joi.string().optional(),
        platform: Joi.string().optional(),
        noCache: Joi.boolean().optional(),
      }).required(),
      imageName: Joi.string().required(),
      tag: Joi.string().default('latest'),
    });

    return schema.validate(data);
  }

  /**
   * 验证推送请求
   */
  private validatePushRequest(data: any) {
    const schema = Joi.object({
      registry: Joi.string().required(),
      repository: Joi.string().required(),
      tag: Joi.string().required(),
      username: Joi.string().optional(),
      password: Joi.string().optional(),
    });

    return schema.validate(data);
  }

  /**
   * 验证复制请求
   */
  private validateCopyRequest(data: any) {
    const schema = Joi.object({
      sourceProject: Joi.string().required(),
      sourceRepository: Joi.string().required(),
      sourceTag: Joi.string().required(),
      targetProject: Joi.string().required(),
      targetRepository: Joi.string().required(),
      targetTag: Joi.string().required(),
    });

    return schema.validate(data);
  }
}
