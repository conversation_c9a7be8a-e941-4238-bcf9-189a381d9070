// 自动驾驶开发加速系统 - 配置管理控制器
import { Request, Response } from 'express';
import { ConfigService, ConfigTemplate, ConfigInstance, SyncConfig } from '../services/configService';
import { logger } from '../utils/logger';
import Jo<PERSON> from 'joi';

export class ConfigController {
  private configService: ConfigService;

  constructor() {
    this.configService = new ConfigService();
  }

  /**
   * 创建配置模板
   */
  createTemplate = async (req: Request, res: Response): Promise<void> => {
    try {
      const { error, value } = this.validateTemplateRequest(req.body);
      if (error) {
        res.status(400).json({
          success: false,
          message: '请求参数验证失败',
          errors: error.details.map(detail => detail.message),
        });
        return;
      }

      const template = await this.configService.createTemplate({
        ...value,
        createdBy: req.user?.id || 'system',
      });

      res.status(201).json({
        success: true,
        message: '配置模板创建成功',
        data: template,
      });
    } catch (error: any) {
      logger.error('创建配置模板失败:', error);
      res.status(500).json({
        success: false,
        message: '创建配置模板失败',
        error: error.message,
      });
    }
  };

  /**
   * 更新配置模板
   */
  updateTemplate = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const { error, value } = this.validateTemplateRequest(req.body);
      if (error) {
        res.status(400).json({
          success: false,
          message: '请求参数验证失败',
          errors: error.details.map(detail => detail.message),
        });
        return;
      }

      const template = await this.configService.updateTemplate(id, value);

      res.json({
        success: true,
        message: '配置模板更新成功',
        data: template,
      });
    } catch (error: any) {
      logger.error('更新配置模板失败:', error);
      res.status(500).json({
        success: false,
        message: '更新配置模板失败',
        error: error.message,
      });
    }
  };

  /**
   * 获取配置模板
   */
  getTemplate = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const template = await this.configService.getTemplate(id);

      if (!template) {
        res.status(404).json({
          success: false,
          message: '配置模板不存在',
        });
        return;
      }

      res.json({
        success: true,
        message: '获取配置模板成功',
        data: template,
      });
    } catch (error: any) {
      logger.error('获取配置模板失败:', error);
      res.status(500).json({
        success: false,
        message: '获取配置模板失败',
        error: error.message,
      });
    }
  };

  /**
   * 获取模板列表
   */
  getTemplates = async (req: Request, res: Response): Promise<void> => {
    try {
      const { environment } = req.query;
      const templates = await this.configService.getTemplates(environment as string);

      res.json({
        success: true,
        message: '获取模板列表成功',
        data: {
          templates,
          total: templates.length,
        },
      });
    } catch (error: any) {
      logger.error('获取模板列表失败:', error);
      res.status(500).json({
        success: false,
        message: '获取模板列表失败',
        error: error.message,
      });
    }
  };

  /**
   * 创建配置实例
   */
  createInstance = async (req: Request, res: Response): Promise<void> => {
    try {
      const { error, value } = this.validateInstanceRequest(req.body);
      if (error) {
        res.status(400).json({
          success: false,
          message: '请求参数验证失败',
          errors: error.details.map(detail => detail.message),
        });
        return;
      }

      const instance = await this.configService.createInstance({
        ...value,
        createdBy: req.user?.id || 'system',
      });

      res.status(201).json({
        success: true,
        message: '配置实例创建成功',
        data: instance,
      });
    } catch (error: any) {
      logger.error('创建配置实例失败:', error);
      res.status(500).json({
        success: false,
        message: '创建配置实例失败',
        error: error.message,
      });
    }
  };

  /**
   * 更新配置实例
   */
  updateInstance = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const { reason, ...updates } = req.body;

      if (!reason) {
        res.status(400).json({
          success: false,
          message: '更新原因不能为空',
        });
        return;
      }

      const instance = await this.configService.updateInstance(
        id,
        {
          ...updates,
          createdBy: req.user?.id || 'system',
        },
        reason
      );

      res.json({
        success: true,
        message: '配置实例更新成功',
        data: instance,
      });
    } catch (error: any) {
      logger.error('更新配置实例失败:', error);
      res.status(500).json({
        success: false,
        message: '更新配置实例失败',
        error: error.message,
      });
    }
  };

  /**
   * 获取配置实例
   */
  getInstance = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const instance = await this.configService.getInstance(id);

      if (!instance) {
        res.status(404).json({
          success: false,
          message: '配置实例不存在',
        });
        return;
      }

      res.json({
        success: true,
        message: '获取配置实例成功',
        data: instance,
      });
    } catch (error: any) {
      logger.error('获取配置实例失败:', error);
      res.status(500).json({
        success: false,
        message: '获取配置实例失败',
        error: error.message,
      });
    }
  };

  /**
   * 获取实例列表
   */
  getInstances = async (req: Request, res: Response): Promise<void> => {
    try {
      const { environment, namespace } = req.query;
      const instances = await this.configService.getInstances(
        environment as string,
        namespace as string
      );

      res.json({
        success: true,
        message: '获取实例列表成功',
        data: {
          instances,
          total: instances.length,
        },
      });
    } catch (error: any) {
      logger.error('获取实例列表失败:', error);
      res.status(500).json({
        success: false,
        message: '获取实例列表失败',
        error: error.message,
      });
    }
  };

  /**
   * 渲染配置
   */
  renderConfig = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      const renderedConfig = await this.configService.renderConfig(id);

      res.json({
        success: true,
        message: '配置渲染成功',
        data: {
          config: renderedConfig,
        },
      });
    } catch (error: any) {
      logger.error('配置渲染失败:', error);
      res.status(500).json({
        success: false,
        message: '配置渲染失败',
        error: error.message,
      });
    }
  };

  /**
   * 部署配置
   */
  deployConfig = async (req: Request, res: Response): Promise<void> => {
    try {
      const { id } = req.params;
      await this.configService.deployConfig(id, req.user?.id || 'system');

      res.json({
        success: true,
        message: '配置部署成功',
        data: {
          instanceId: id,
          deployedAt: new Date(),
        },
      });
    } catch (error: any) {
      logger.error('配置部署失败:', error);
      res.status(500).json({
        success: false,
        message: '配置部署失败',
        error: error.message,
      });
    }
  };

  /**
   * 同步环境配置
   */
  syncEnvironments = async (req: Request, res: Response): Promise<void> => {
    try {
      const { error, value } = this.validateSyncRequest(req.body);
      if (error) {
        res.status(400).json({
          success: false,
          message: '请求参数验证失败',
          errors: error.details.map(detail => detail.message),
        });
        return;
      }

      await this.configService.syncEnvironments(value, req.user?.id || 'system');

      res.json({
        success: true,
        message: '环境配置同步成功',
        data: {
          sourceEnvironment: value.sourceEnvironment,
          targetEnvironment: value.targetEnvironment,
          syncedAt: new Date(),
        },
      });
    } catch (error: any) {
      logger.error('环境配置同步失败:', error);
      res.status(500).json({
        success: false,
        message: '环境配置同步失败',
        error: error.message,
      });
    }
  };

  /**
   * 获取变更历史
   */
  getChangeHistory = async (req: Request, res: Response): Promise<void> => {
    try {
      const { configId } = req.params;
      const { limit = 100 } = req.query;

      const changes = await this.configService.getChangeHistory(
        configId,
        Number(limit)
      );

      res.json({
        success: true,
        message: '获取变更历史成功',
        data: {
          changes,
          total: changes.length,
        },
      });
    } catch (error: any) {
      logger.error('获取变更历史失败:', error);
      res.status(500).json({
        success: false,
        message: '获取变更历史失败',
        error: error.message,
      });
    }
  };

  // 验证方法

  private validateTemplateRequest(data: any) {
    const schema = Joi.object({
      name: Joi.string().required(),
      description: Joi.string().required(),
      version: Joi.string().required(),
      environment: Joi.string().required(),
      template: Joi.string().required(),
      variables: Joi.array().items(
        Joi.object({
          name: Joi.string().required(),
          type: Joi.string().valid('string', 'number', 'boolean', 'secret').required(),
          description: Joi.string().required(),
          defaultValue: Joi.any().optional(),
          required: Joi.boolean().required(),
          validation: Joi.object({
            pattern: Joi.string().optional(),
            min: Joi.number().optional(),
            max: Joi.number().optional(),
            enum: Joi.array().items(Joi.string()).optional(),
          }).optional(),
        })
      ).required(),
    });

    return schema.validate(data);
  }

  private validateInstanceRequest(data: any) {
    const schema = Joi.object({
      templateId: Joi.string().required(),
      environment: Joi.string().required(),
      namespace: Joi.string().required(),
      name: Joi.string().required(),
      values: Joi.object().required(),
      secrets: Joi.array().items(Joi.string()).default([]),
      status: Joi.string().valid('draft', 'active', 'deprecated').default('draft'),
      version: Joi.string().required(),
    });

    return schema.validate(data);
  }

  private validateSyncRequest(data: any) {
    const schema = Joi.object({
      sourceEnvironment: Joi.string().required(),
      targetEnvironment: Joi.string().required(),
      configTemplates: Joi.array().items(Joi.string()).default([]),
      excludeSecrets: Joi.boolean().default(false),
      transformRules: Joi.array().items(
        Joi.object({
          field: Joi.string().required(),
          operation: Joi.string().valid('replace', 'prefix', 'suffix', 'transform').required(),
          value: Joi.string().required(),
          condition: Joi.string().optional(),
        })
      ).default([]),
    });

    return schema.validate(data);
  }
}
