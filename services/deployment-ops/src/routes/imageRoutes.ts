// 自动驾驶开发加速系统 - 镜像管理路由
import { Router } from 'express';
import { ImageController } from '../controllers/imageController';
import { authMiddleware } from '../middleware/auth';
import { rateLimitMiddleware } from '../middleware/rateLimit';

const router = Router();
const imageController = new ImageController();

// 应用认证中间件
router.use(authMiddleware);

// 应用限流中间件
router.use(rateLimitMiddleware);

/**
 * @swagger
 * /api/v1/images/build:
 *   post:
 *     summary: 构建Docker镜像
 *     tags: [Images]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - buildConfig
 *               - imageName
 *             properties:
 *               buildConfig:
 *                 type: object
 *                 required:
 *                   - dockerfile
 *                   - context
 *                 properties:
 *                   dockerfile:
 *                     type: string
 *                     description: Dockerfile路径
 *                   context:
 *                     type: string
 *                     description: 构建上下文路径
 *                   buildArgs:
 *                     type: object
 *                     description: 构建参数
 *                   labels:
 *                     type: object
 *                     description: 镜像标签
 *                   target:
 *                     type: string
 *                     description: 构建目标
 *                   platform:
 *                     type: string
 *                     description: 目标平台
 *                   noCache:
 *                     type: boolean
 *                     description: 是否禁用缓存
 *               imageName:
 *                 type: string
 *                 description: 镜像名称
 *               tag:
 *                 type: string
 *                 description: 镜像标签
 *                 default: latest
 *     responses:
 *       200:
 *         description: 镜像构建成功
 *       400:
 *         description: 请求参数错误
 *       500:
 *         description: 服务器内部错误
 */
router.post('/build', imageController.buildImage);

/**
 * @swagger
 * /api/v1/images/push:
 *   post:
 *     summary: 推送镜像到Harbor仓库
 *     tags: [Images]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - registry
 *               - repository
 *               - tag
 *             properties:
 *               registry:
 *                 type: string
 *                 description: 镜像仓库地址
 *               repository:
 *                 type: string
 *                 description: 仓库名称
 *               tag:
 *                 type: string
 *                 description: 镜像标签
 *               username:
 *                 type: string
 *                 description: 用户名
 *               password:
 *                 type: string
 *                 description: 密码
 *     responses:
 *       200:
 *         description: 镜像推送成功
 *       400:
 *         description: 请求参数错误
 *       500:
 *         description: 服务器内部错误
 */
router.post('/push', imageController.pushImage);

/**
 * @swagger
 * /api/v1/images/projects/{projectName}:
 *   get:
 *     summary: 获取项目下的镜像列表
 *     tags: [Images]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: projectName
 *         required: true
 *         schema:
 *           type: string
 *         description: 项目名称
 *       - in: query
 *         name: repository
 *         schema:
 *           type: string
 *         description: 仓库名称
 *       - in: query
 *         name: tag
 *         schema:
 *           type: string
 *         description: 镜像标签
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: pageSize
 *         schema:
 *           type: integer
 *           default: 20
 *         description: 每页数量
 *     responses:
 *       200:
 *         description: 获取镜像列表成功
 *       500:
 *         description: 服务器内部错误
 */
router.get('/projects/:projectName', imageController.getImages);

/**
 * @swagger
 * /api/v1/images/projects/{projectName}/repositories/{repository}/tags:
 *   get:
 *     summary: 获取仓库的标签列表
 *     tags: [Images]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: projectName
 *         required: true
 *         schema:
 *           type: string
 *         description: 项目名称
 *       - in: path
 *         name: repository
 *         required: true
 *         schema:
 *           type: string
 *         description: 仓库名称
 *     responses:
 *       200:
 *         description: 获取标签列表成功
 *       500:
 *         description: 服务器内部错误
 */
router.get('/projects/:projectName/repositories/:repository/tags', imageController.getImageTags);

/**
 * @swagger
 * /api/v1/images/projects/{projectName}/repositories/{repository}/artifacts/{tag}:
 *   delete:
 *     summary: 删除指定镜像
 *     tags: [Images]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: projectName
 *         required: true
 *         schema:
 *           type: string
 *         description: 项目名称
 *       - in: path
 *         name: repository
 *         required: true
 *         schema:
 *           type: string
 *         description: 仓库名称
 *       - in: path
 *         name: tag
 *         required: true
 *         schema:
 *           type: string
 *         description: 镜像标签
 *     responses:
 *       200:
 *         description: 镜像删除成功
 *       500:
 *         description: 服务器内部错误
 */
router.delete('/projects/:projectName/repositories/:repository/artifacts/:tag', imageController.deleteImage);

/**
 * @swagger
 * /api/v1/images/projects/{projectName}/repositories/{repository}/artifacts/{tag}/scan:
 *   post:
 *     summary: 扫描镜像漏洞
 *     tags: [Images]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: projectName
 *         required: true
 *         schema:
 *           type: string
 *         description: 项目名称
 *       - in: path
 *         name: repository
 *         required: true
 *         schema:
 *           type: string
 *         description: 仓库名称
 *       - in: path
 *         name: tag
 *         required: true
 *         schema:
 *           type: string
 *         description: 镜像标签
 *     responses:
 *       200:
 *         description: 扫描已启动
 *       500:
 *         description: 服务器内部错误
 */
router.post('/projects/:projectName/repositories/:repository/artifacts/:tag/scan', imageController.scanImage);

/**
 * @swagger
 * /api/v1/images/projects/{projectName}/repositories/{repository}/artifacts/{tag}/vulnerability:
 *   get:
 *     summary: 获取镜像漏洞报告
 *     tags: [Images]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: projectName
 *         required: true
 *         schema:
 *           type: string
 *         description: 项目名称
 *       - in: path
 *         name: repository
 *         required: true
 *         schema:
 *           type: string
 *         description: 仓库名称
 *       - in: path
 *         name: tag
 *         required: true
 *         schema:
 *           type: string
 *         description: 镜像标签
 *     responses:
 *       200:
 *         description: 获取漏洞报告成功
 *       500:
 *         description: 服务器内部错误
 */
router.get('/projects/:projectName/repositories/:repository/artifacts/:tag/vulnerability', imageController.getVulnerabilityReport);

/**
 * @swagger
 * /api/v1/images/copy:
 *   post:
 *     summary: 复制镜像
 *     tags: [Images]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - sourceProject
 *               - sourceRepository
 *               - sourceTag
 *               - targetProject
 *               - targetRepository
 *               - targetTag
 *             properties:
 *               sourceProject:
 *                 type: string
 *                 description: 源项目名称
 *               sourceRepository:
 *                 type: string
 *                 description: 源仓库名称
 *               sourceTag:
 *                 type: string
 *                 description: 源镜像标签
 *               targetProject:
 *                 type: string
 *                 description: 目标项目名称
 *               targetRepository:
 *                 type: string
 *                 description: 目标仓库名称
 *               targetTag:
 *                 type: string
 *                 description: 目标镜像标签
 *     responses:
 *       200:
 *         description: 镜像复制成功
 *       400:
 *         description: 请求参数错误
 *       500:
 *         description: 服务器内部错误
 */
router.post('/copy', imageController.copyImage);

/**
 * @swagger
 * /api/v1/images/projects/{projectName}/repositories/{repository}/cleanup:
 *   post:
 *     summary: 清理旧镜像
 *     tags: [Images]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: projectName
 *         required: true
 *         schema:
 *           type: string
 *         description: 项目名称
 *       - in: path
 *         name: repository
 *         required: true
 *         schema:
 *           type: string
 *         description: 仓库名称
 *       - in: query
 *         name: keepCount
 *         schema:
 *           type: integer
 *           default: 10
 *         description: 保留的镜像数量
 *     responses:
 *       200:
 *         description: 清理成功
 *       500:
 *         description: 服务器内部错误
 */
router.post('/projects/:projectName/repositories/:repository/cleanup', imageController.cleanupOldImages);

export default router;
