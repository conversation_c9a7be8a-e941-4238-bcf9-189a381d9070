// 自动驾驶开发加速系统 - 配置管理路由
import { Router } from 'express';
import { ConfigController } from '../controllers/configController';
import { authMiddleware } from '../middleware/auth';
import { rateLimitMiddleware } from '../middleware/rateLimit';

const router = Router();
const configController = new ConfigController();

// 应用认证中间件
router.use(authMiddleware);

// 应用限流中间件
router.use(rateLimitMiddleware);

/**
 * @swagger
 * /api/v1/config/templates:
 *   post:
 *     summary: 创建配置模板
 *     tags: [Config]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - name
 *               - description
 *               - version
 *               - environment
 *               - template
 *               - variables
 *             properties:
 *               name:
 *                 type: string
 *                 description: 模板名称
 *               description:
 *                 type: string
 *                 description: 模板描述
 *               version:
 *                 type: string
 *                 description: 模板版本
 *               environment:
 *                 type: string
 *                 description: 目标环境
 *               template:
 *                 type: string
 *                 description: 配置模板内容
 *               variables:
 *                 type: array
 *                 description: 模板变量定义
 *                 items:
 *                   type: object
 *                   properties:
 *                     name:
 *                       type: string
 *                     type:
 *                       type: string
 *                       enum: [string, number, boolean, secret]
 *                     description:
 *                       type: string
 *                     defaultValue:
 *                       type: string
 *                     required:
 *                       type: boolean
 *                     validation:
 *                       type: object
 *     responses:
 *       201:
 *         description: 配置模板创建成功
 *       400:
 *         description: 请求参数错误
 *       500:
 *         description: 服务器内部错误
 */
router.post('/templates', configController.createTemplate);

/**
 * @swagger
 * /api/v1/config/templates:
 *   get:
 *     summary: 获取配置模板列表
 *     tags: [Config]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: environment
 *         schema:
 *           type: string
 *         description: 环境过滤
 *     responses:
 *       200:
 *         description: 获取模板列表成功
 *       500:
 *         description: 服务器内部错误
 */
router.get('/templates', configController.getTemplates);

/**
 * @swagger
 * /api/v1/config/templates/{id}:
 *   get:
 *     summary: 获取配置模板详情
 *     tags: [Config]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 模板ID
 *     responses:
 *       200:
 *         description: 获取模板详情成功
 *       404:
 *         description: 模板不存在
 *       500:
 *         description: 服务器内部错误
 */
router.get('/templates/:id', configController.getTemplate);

/**
 * @swagger
 * /api/v1/config/templates/{id}:
 *   put:
 *     summary: 更新配置模板
 *     tags: [Config]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 模板ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *               description:
 *                 type: string
 *               version:
 *                 type: string
 *               template:
 *                 type: string
 *               variables:
 *                 type: array
 *     responses:
 *       200:
 *         description: 模板更新成功
 *       400:
 *         description: 请求参数错误
 *       404:
 *         description: 模板不存在
 *       500:
 *         description: 服务器内部错误
 */
router.put('/templates/:id', configController.updateTemplate);

/**
 * @swagger
 * /api/v1/config/instances:
 *   post:
 *     summary: 创建配置实例
 *     tags: [Config]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - templateId
 *               - environment
 *               - namespace
 *               - name
 *               - values
 *               - version
 *             properties:
 *               templateId:
 *                 type: string
 *                 description: 模板ID
 *               environment:
 *                 type: string
 *                 description: 环境名称
 *               namespace:
 *                 type: string
 *                 description: 命名空间
 *               name:
 *                 type: string
 *                 description: 实例名称
 *               values:
 *                 type: object
 *                 description: 配置值
 *               version:
 *                 type: string
 *                 description: 版本号
 *               status:
 *                 type: string
 *                 enum: [draft, active, deprecated]
 *                 default: draft
 *     responses:
 *       201:
 *         description: 配置实例创建成功
 *       400:
 *         description: 请求参数错误
 *       500:
 *         description: 服务器内部错误
 */
router.post('/instances', configController.createInstance);

/**
 * @swagger
 * /api/v1/config/instances:
 *   get:
 *     summary: 获取配置实例列表
 *     tags: [Config]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: environment
 *         schema:
 *           type: string
 *         description: 环境过滤
 *       - in: query
 *         name: namespace
 *         schema:
 *           type: string
 *         description: 命名空间过滤
 *     responses:
 *       200:
 *         description: 获取实例列表成功
 *       500:
 *         description: 服务器内部错误
 */
router.get('/instances', configController.getInstances);

/**
 * @swagger
 * /api/v1/config/instances/{id}:
 *   get:
 *     summary: 获取配置实例详情
 *     tags: [Config]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 实例ID
 *     responses:
 *       200:
 *         description: 获取实例详情成功
 *       404:
 *         description: 实例不存在
 *       500:
 *         description: 服务器内部错误
 */
router.get('/instances/:id', configController.getInstance);

/**
 * @swagger
 * /api/v1/config/instances/{id}:
 *   put:
 *     summary: 更新配置实例
 *     tags: [Config]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 实例ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - reason
 *             properties:
 *               reason:
 *                 type: string
 *                 description: 更新原因
 *               values:
 *                 type: object
 *                 description: 配置值
 *               status:
 *                 type: string
 *                 enum: [draft, active, deprecated]
 *     responses:
 *       200:
 *         description: 实例更新成功
 *       400:
 *         description: 请求参数错误
 *       404:
 *         description: 实例不存在
 *       500:
 *         description: 服务器内部错误
 */
router.put('/instances/:id', configController.updateInstance);

/**
 * @swagger
 * /api/v1/config/instances/{id}/render:
 *   get:
 *     summary: 渲染配置实例
 *     tags: [Config]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 实例ID
 *     responses:
 *       200:
 *         description: 配置渲染成功
 *       404:
 *         description: 实例不存在
 *       500:
 *         description: 服务器内部错误
 */
router.get('/instances/:id/render', configController.renderConfig);

/**
 * @swagger
 * /api/v1/config/instances/{id}/deploy:
 *   post:
 *     summary: 部署配置实例
 *     tags: [Config]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: 实例ID
 *     responses:
 *       200:
 *         description: 配置部署成功
 *       404:
 *         description: 实例不存在
 *       500:
 *         description: 服务器内部错误
 */
router.post('/instances/:id/deploy', configController.deployConfig);

/**
 * @swagger
 * /api/v1/config/sync:
 *   post:
 *     summary: 同步环境配置
 *     tags: [Config]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - sourceEnvironment
 *               - targetEnvironment
 *             properties:
 *               sourceEnvironment:
 *                 type: string
 *                 description: 源环境
 *               targetEnvironment:
 *                 type: string
 *                 description: 目标环境
 *               configTemplates:
 *                 type: array
 *                 items:
 *                   type: string
 *                 description: 要同步的模板ID列表
 *               excludeSecrets:
 *                 type: boolean
 *                 default: false
 *                 description: 是否排除密钥
 *               transformRules:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     field:
 *                       type: string
 *                     operation:
 *                       type: string
 *                       enum: [replace, prefix, suffix, transform]
 *                     value:
 *                       type: string
 *                     condition:
 *                       type: string
 *                 description: 转换规则
 *     responses:
 *       200:
 *         description: 环境配置同步成功
 *       400:
 *         description: 请求参数错误
 *       500:
 *         description: 服务器内部错误
 */
router.post('/sync', configController.syncEnvironments);

/**
 * @swagger
 * /api/v1/config/changes:
 *   get:
 *     summary: 获取全局变更历史
 *     tags: [Config]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 100
 *         description: 返回记录数量限制
 *     responses:
 *       200:
 *         description: 获取变更历史成功
 *       500:
 *         description: 服务器内部错误
 */
router.get('/changes', (req, res) => configController.getChangeHistory(req, res));

/**
 * @swagger
 * /api/v1/config/instances/{configId}/changes:
 *   get:
 *     summary: 获取配置实例变更历史
 *     tags: [Config]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: configId
 *         required: true
 *         schema:
 *           type: string
 *         description: 配置实例ID
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 100
 *         description: 返回记录数量限制
 *     responses:
 *       200:
 *         description: 获取变更历史成功
 *       500:
 *         description: 服务器内部错误
 */
router.get('/instances/:configId/changes', configController.getChangeHistory);

export default router;
