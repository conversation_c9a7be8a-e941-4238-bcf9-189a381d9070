// 自动驾驶开发加速系统 - 容器镜像管理服务
import axios, { AxiosInstance } from 'axios';
import Docker from 'dockerode';
import * as fs from 'fs';
import * as path from 'path';
import * as tar from 'tar';
import * as archiver from 'archiver';
import * as semver from 'semver';
import { logger } from '../utils/logger';
import { config } from '../config';

// 镜像信息接口
export interface ImageInfo {
  id: string;
  name: string;
  tag: string;
  repository: string;
  digest: string;
  size: number;
  createdAt: Date;
  pushedAt: Date;
  labels: Record<string, string>;
  vulnerabilities?: VulnerabilityReport;
}

// 漏洞报告接口
export interface VulnerabilityReport {
  scanId: string;
  scanTime: Date;
  severity: {
    critical: number;
    high: number;
    medium: number;
    low: number;
    unknown: number;
  };
  vulnerabilities: Vulnerability[];
}

// 漏洞详情接口
export interface Vulnerability {
  id: string;
  severity: 'Critical' | 'High' | 'Medium' | 'Low' | 'Unknown';
  package: string;
  version: string;
  fixedVersion?: string;
  description: string;
  links: string[];
}

// 构建配置接口
export interface BuildConfig {
  dockerfile: string;
  context: string;
  buildArgs?: Record<string, string>;
  labels?: Record<string, string>;
  target?: string;
  platform?: string;
  noCache?: boolean;
}

// 推送配置接口
export interface PushConfig {
  registry: string;
  repository: string;
  tag: string;
  username?: string;
  password?: string;
}

export class ImageService {
  private docker: Docker;
  private harborClient: AxiosInstance;

  constructor() {
    // 初始化Docker客户端
    this.docker = new Docker({
      socketPath: config.docker.socketPath || '/var/run/docker.sock',
      host: config.docker.host,
      port: config.docker.port,
      ca: config.docker.ca,
      cert: config.docker.cert,
      key: config.docker.key,
    });

    // 初始化Harbor客户端
    this.harborClient = axios.create({
      baseURL: config.harbor.baseUrl,
      auth: {
        username: config.harbor.username,
        password: config.harbor.password,
      },
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }

  /**
   * 构建Docker镜像
   */
  async buildImage(
    buildConfig: BuildConfig,
    imageName: string,
    tag: string = 'latest'
  ): Promise<string> {
    logger.info(`开始构建镜像: ${imageName}:${tag}`);

    try {
      // 创建构建上下文tar包
      const contextPath = await this.createBuildContext(buildConfig.context);

      // 构建选项
      const buildOptions = {
        t: `${imageName}:${tag}`,
        dockerfile: buildConfig.dockerfile,
        buildargs: buildConfig.buildArgs || {},
        labels: {
          'build.timestamp': new Date().toISOString(),
          'build.version': tag,
          ...buildConfig.labels,
        },
        target: buildConfig.target,
        platform: buildConfig.platform,
        nocache: buildConfig.noCache || false,
      };

      // 执行构建
      const stream = await this.docker.buildImage(contextPath, buildOptions);

      return new Promise((resolve, reject) => {
        let imageId = '';

        this.docker.modem.followProgress(
          stream,
          (err: any, res: any[]) => {
            if (err) {
              logger.error('镜像构建失败:', err);
              reject(err);
            } else {
              // 从构建输出中提取镜像ID
              const lastOutput = res[res.length - 1];
              if (lastOutput && lastOutput.aux && lastOutput.aux.ID) {
                imageId = lastOutput.aux.ID;
              }
              logger.info(`镜像构建成功: ${imageName}:${tag}, ID: ${imageId}`);
              resolve(imageId);
            }
          },
          (event: any) => {
            // 处理构建进度事件
            if (event.stream) {
              logger.debug(event.stream.trim());
            }
            if (event.error) {
              logger.error('构建错误:', event.error);
            }
          }
        );
      });
    } catch (error) {
      logger.error('镜像构建失败:', error);
      throw error;
    }
  }

  /**
   * 推送镜像到Harbor仓库
   */
  async pushImage(pushConfig: PushConfig): Promise<void> {
    const fullImageName = `${pushConfig.registry}/${pushConfig.repository}:${pushConfig.tag}`;
    logger.info(`开始推送镜像: ${fullImageName}`);

    try {
      // 获取镜像
      const image = this.docker.getImage(fullImageName);

      // 推送镜像
      const stream = await image.push({
        authconfig: {
          username: pushConfig.username || config.harbor.username,
          password: pushConfig.password || config.harbor.password,
          serveraddress: pushConfig.registry,
        },
      });

      return new Promise((resolve, reject) => {
        this.docker.modem.followProgress(
          stream,
          (err: any, res: any[]) => {
            if (err) {
              logger.error('镜像推送失败:', err);
              reject(err);
            } else {
              logger.info(`镜像推送成功: ${fullImageName}`);
              resolve();
            }
          },
          (event: any) => {
            // 处理推送进度事件
            if (event.status) {
              logger.debug(`${event.status}: ${event.progress || ''}`);
            }
            if (event.error) {
              logger.error('推送错误:', event.error);
            }
          }
        );
      });
    } catch (error) {
      logger.error('镜像推送失败:', error);
      throw error;
    }
  }

  /**
   * 从Harbor获取镜像列表
   */
  async getImages(
    projectName: string,
    repository?: string,
    tag?: string
  ): Promise<ImageInfo[]> {
    try {
      let url = `/api/v2.0/projects/${projectName}/repositories`;
      
      if (repository) {
        url += `/${encodeURIComponent(repository)}/artifacts`;
      }

      const response = await this.harborClient.get(url, {
        params: {
          page: 1,
          page_size: 100,
          with_tag: true,
          with_scan_overview: true,
          ...(tag && { q: `tag=${tag}` }),
        },
      });

      const images: ImageInfo[] = [];

      for (const artifact of response.data) {
        if (artifact.tags && artifact.tags.length > 0) {
          for (const tagInfo of artifact.tags) {
            images.push({
              id: artifact.digest,
              name: repository || artifact.repository_name,
              tag: tagInfo.name,
              repository: `${projectName}/${repository || artifact.repository_name}`,
              digest: artifact.digest,
              size: artifact.size,
              createdAt: new Date(artifact.push_time),
              pushedAt: new Date(artifact.push_time),
              labels: artifact.labels || {},
              vulnerabilities: this.parseVulnerabilityReport(artifact.scan_overview),
            });
          }
        }
      }

      return images;
    } catch (error) {
      logger.error('获取镜像列表失败:', error);
      throw error;
    }
  }

  /**
   * 删除镜像
   */
  async deleteImage(
    projectName: string,
    repository: string,
    tag: string
  ): Promise<void> {
    try {
      const url = `/api/v2.0/projects/${projectName}/repositories/${encodeURIComponent(repository)}/artifacts/${tag}`;
      
      await this.harborClient.delete(url);
      
      logger.info(`镜像删除成功: ${projectName}/${repository}:${tag}`);
    } catch (error) {
      logger.error('镜像删除失败:', error);
      throw error;
    }
  }

  /**
   * 扫描镜像漏洞
   */
  async scanImage(
    projectName: string,
    repository: string,
    tag: string
  ): Promise<string> {
    try {
      const url = `/api/v2.0/projects/${projectName}/repositories/${encodeURIComponent(repository)}/artifacts/${tag}/scan`;
      
      const response = await this.harborClient.post(url);
      
      logger.info(`镜像扫描已启动: ${projectName}/${repository}:${tag}`);
      return response.headers['location'] || '';
    } catch (error) {
      logger.error('镜像扫描失败:', error);
      throw error;
    }
  }

  /**
   * 获取镜像漏洞报告
   */
  async getVulnerabilityReport(
    projectName: string,
    repository: string,
    tag: string
  ): Promise<VulnerabilityReport | null> {
    try {
      const url = `/api/v2.0/projects/${projectName}/repositories/${encodeURIComponent(repository)}/artifacts/${tag}`;
      
      const response = await this.harborClient.get(url, {
        params: {
          with_scan_overview: true,
        },
      });

      return this.parseVulnerabilityReport(response.data.scan_overview);
    } catch (error) {
      logger.error('获取漏洞报告失败:', error);
      throw error;
    }
  }

  /**
   * 获取镜像标签列表
   */
  async getImageTags(
    projectName: string,
    repository: string
  ): Promise<string[]> {
    try {
      const url = `/api/v2.0/projects/${projectName}/repositories/${encodeURIComponent(repository)}/artifacts`;
      
      const response = await this.harborClient.get(url, {
        params: {
          with_tag: true,
        },
      });

      const tags: string[] = [];
      for (const artifact of response.data) {
        if (artifact.tags) {
          tags.push(...artifact.tags.map((tag: any) => tag.name));
        }
      }

      return tags.sort((a, b) => {
        // 使用semver排序，如果不是有效的semver则按字符串排序
        if (semver.valid(a) && semver.valid(b)) {
          return semver.rcompare(a, b);
        }
        return b.localeCompare(a);
      });
    } catch (error) {
      logger.error('获取镜像标签失败:', error);
      throw error;
    }
  }

  /**
   * 复制镜像
   */
  async copyImage(
    sourceProject: string,
    sourceRepository: string,
    sourceTag: string,
    targetProject: string,
    targetRepository: string,
    targetTag: string
  ): Promise<void> {
    try {
      const url = `/api/v2.0/projects/${targetProject}/repositories/${encodeURIComponent(targetRepository)}/artifacts`;
      
      await this.harborClient.post(url, {
        from: `${sourceProject}/${sourceRepository}:${sourceTag}`,
        tag: targetTag,
      });

      logger.info(`镜像复制成功: ${sourceProject}/${sourceRepository}:${sourceTag} -> ${targetProject}/${targetRepository}:${targetTag}`);
    } catch (error) {
      logger.error('镜像复制失败:', error);
      throw error;
    }
  }

  /**
   * 清理旧镜像
   */
  async cleanupOldImages(
    projectName: string,
    repository: string,
    keepCount: number = 10
  ): Promise<number> {
    try {
      const images = await this.getImages(projectName, repository);
      
      // 按创建时间排序，保留最新的镜像
      const sortedImages = images.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
      
      const imagesToDelete = sortedImages.slice(keepCount);
      let deletedCount = 0;

      for (const image of imagesToDelete) {
        try {
          await this.deleteImage(projectName, repository, image.tag);
          deletedCount++;
        } catch (error) {
          logger.warn(`删除镜像失败: ${image.repository}:${image.tag}`, error);
        }
      }

      logger.info(`清理完成，删除了 ${deletedCount} 个旧镜像`);
      return deletedCount;
    } catch (error) {
      logger.error('清理旧镜像失败:', error);
      throw error;
    }
  }

  /**
   * 创建构建上下文
   */
  private async createBuildContext(contextPath: string): Promise<NodeJS.ReadableStream> {
    return new Promise((resolve, reject) => {
      const archive = archiver('tar', {
        gzip: false,
      });

      archive.on('error', reject);
      archive.on('end', () => {
        logger.debug('构建上下文创建完成');
      });

      // 添加构建上下文目录
      archive.directory(contextPath, false);
      archive.finalize();

      resolve(archive);
    });
  }

  /**
   * 解析漏洞报告
   */
  private parseVulnerabilityReport(scanOverview: any): VulnerabilityReport | undefined {
    if (!scanOverview || !scanOverview['application/vnd.security.vulnerability.report; version=1.1']) {
      return undefined;
    }

    const report = scanOverview['application/vnd.security.vulnerability.report; version=1.1'];
    
    if (!report.report_id || report.scan_status !== 'Success') {
      return undefined;
    }

    return {
      scanId: report.report_id,
      scanTime: new Date(report.end_time),
      severity: {
        critical: report.summary?.total || 0,
        high: 0,
        medium: 0,
        low: 0,
        unknown: 0,
      },
      vulnerabilities: [], // 需要额外的API调用来获取详细信息
    };
  }
}
