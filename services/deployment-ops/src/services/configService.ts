// 自动驾驶开发加速系统 - 环境配置管理服务
import * as vault from 'node-vault';
import * as yaml from 'yaml';
import * as fs from 'fs/promises';
import * as path from 'path';
import { logger } from '../utils/logger';
import { config } from '../config';

// 配置模板接口
export interface ConfigTemplate {
  id: string;
  name: string;
  description: string;
  version: string;
  environment: string;
  template: string;
  variables: ConfigVariable[];
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
}

// 配置变量接口
export interface ConfigVariable {
  name: string;
  type: 'string' | 'number' | 'boolean' | 'secret';
  description: string;
  defaultValue?: any;
  required: boolean;
  validation?: {
    pattern?: string;
    min?: number;
    max?: number;
    enum?: string[];
  };
}

// 配置实例接口
export interface ConfigInstance {
  id: string;
  templateId: string;
  environment: string;
  namespace: string;
  name: string;
  values: Record<string, any>;
  secrets: string[];
  status: 'draft' | 'active' | 'deprecated';
  version: string;
  createdAt: Date;
  updatedAt: Date;
  deployedAt?: Date;
  createdBy: string;
}

// 配置变更记录接口
export interface ConfigChange {
  id: string;
  configId: string;
  changeType: 'create' | 'update' | 'delete' | 'deploy';
  beforeValues?: Record<string, any>;
  afterValues?: Record<string, any>;
  reason: string;
  approvedBy?: string;
  appliedAt?: Date;
  createdAt: Date;
  createdBy: string;
}

// 环境同步配置接口
export interface SyncConfig {
  sourceEnvironment: string;
  targetEnvironment: string;
  configTemplates: string[];
  excludeSecrets: boolean;
  transformRules: TransformRule[];
}

// 转换规则接口
export interface TransformRule {
  field: string;
  operation: 'replace' | 'prefix' | 'suffix' | 'transform';
  value: string;
  condition?: string;
}

export class ConfigService {
  private vaultClient: any;
  private configBasePath: string;

  constructor() {
    // 初始化Vault客户端
    this.vaultClient = vault({
      apiVersion: 'v1',
      endpoint: config.vault.endpoint,
      token: config.vault.token,
    });

    this.configBasePath = config.configBasePath || './configs';
  }

  /**
   * 创建配置模板
   */
  async createTemplate(template: Omit<ConfigTemplate, 'id' | 'createdAt' | 'updatedAt'>): Promise<ConfigTemplate> {
    const newTemplate: ConfigTemplate = {
      id: this.generateId(),
      ...template,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // 验证模板
    await this.validateTemplate(newTemplate);

    // 保存模板到文件系统
    await this.saveTemplate(newTemplate);

    logger.info(`配置模板创建成功: ${newTemplate.name} (${newTemplate.id})`);
    return newTemplate;
  }

  /**
   * 更新配置模板
   */
  async updateTemplate(id: string, updates: Partial<ConfigTemplate>): Promise<ConfigTemplate> {
    const existingTemplate = await this.getTemplate(id);
    if (!existingTemplate) {
      throw new Error(`配置模板不存在: ${id}`);
    }

    const updatedTemplate: ConfigTemplate = {
      ...existingTemplate,
      ...updates,
      id,
      updatedAt: new Date(),
    };

    // 验证模板
    await this.validateTemplate(updatedTemplate);

    // 保存模板
    await this.saveTemplate(updatedTemplate);

    logger.info(`配置模板更新成功: ${updatedTemplate.name} (${id})`);
    return updatedTemplate;
  }

  /**
   * 获取配置模板
   */
  async getTemplate(id: string): Promise<ConfigTemplate | null> {
    try {
      const templatePath = path.join(this.configBasePath, 'templates', `${id}.yaml`);
      const content = await fs.readFile(templatePath, 'utf-8');
      return yaml.parse(content);
    } catch (error) {
      if ((error as any).code === 'ENOENT') {
        return null;
      }
      throw error;
    }
  }

  /**
   * 获取模板列表
   */
  async getTemplates(environment?: string): Promise<ConfigTemplate[]> {
    try {
      const templatesDir = path.join(this.configBasePath, 'templates');
      const files = await fs.readdir(templatesDir);
      
      const templates: ConfigTemplate[] = [];
      for (const file of files) {
        if (file.endsWith('.yaml')) {
          const content = await fs.readFile(path.join(templatesDir, file), 'utf-8');
          const template = yaml.parse(content);
          
          if (!environment || template.environment === environment) {
            templates.push(template);
          }
        }
      }

      return templates.sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());
    } catch (error) {
      logger.error('获取模板列表失败:', error);
      return [];
    }
  }

  /**
   * 创建配置实例
   */
  async createInstance(instance: Omit<ConfigInstance, 'id' | 'createdAt' | 'updatedAt'>): Promise<ConfigInstance> {
    const template = await this.getTemplate(instance.templateId);
    if (!template) {
      throw new Error(`配置模板不存在: ${instance.templateId}`);
    }

    // 验证配置值
    await this.validateInstanceValues(template, instance.values);

    const newInstance: ConfigInstance = {
      id: this.generateId(),
      ...instance,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // 处理密钥
    await this.processSecrets(newInstance, template);

    // 保存实例
    await this.saveInstance(newInstance);

    // 记录变更
    await this.recordChange({
      id: this.generateId(),
      configId: newInstance.id,
      changeType: 'create',
      afterValues: instance.values,
      reason: '创建配置实例',
      createdAt: new Date(),
      createdBy: instance.createdBy,
    });

    logger.info(`配置实例创建成功: ${newInstance.name} (${newInstance.id})`);
    return newInstance;
  }

  /**
   * 更新配置实例
   */
  async updateInstance(id: string, updates: Partial<ConfigInstance>, reason: string): Promise<ConfigInstance> {
    const existingInstance = await this.getInstance(id);
    if (!existingInstance) {
      throw new Error(`配置实例不存在: ${id}`);
    }

    const template = await this.getTemplate(existingInstance.templateId);
    if (!template) {
      throw new Error(`配置模板不存在: ${existingInstance.templateId}`);
    }

    // 验证更新的配置值
    if (updates.values) {
      await this.validateInstanceValues(template, updates.values);
    }

    const beforeValues = existingInstance.values;
    const updatedInstance: ConfigInstance = {
      ...existingInstance,
      ...updates,
      id,
      updatedAt: new Date(),
    };

    // 处理密钥
    if (updates.values) {
      await this.processSecrets(updatedInstance, template);
    }

    // 保存实例
    await this.saveInstance(updatedInstance);

    // 记录变更
    await this.recordChange({
      id: this.generateId(),
      configId: id,
      changeType: 'update',
      beforeValues,
      afterValues: updatedInstance.values,
      reason,
      createdAt: new Date(),
      createdBy: updates.createdBy || existingInstance.createdBy,
    });

    logger.info(`配置实例更新成功: ${updatedInstance.name} (${id})`);
    return updatedInstance;
  }

  /**
   * 获取配置实例
   */
  async getInstance(id: string): Promise<ConfigInstance | null> {
    try {
      const instancePath = path.join(this.configBasePath, 'instances', `${id}.yaml`);
      const content = await fs.readFile(instancePath, 'utf-8');
      return yaml.parse(content);
    } catch (error) {
      if ((error as any).code === 'ENOENT') {
        return null;
      }
      throw error;
    }
  }

  /**
   * 获取实例列表
   */
  async getInstances(environment?: string, namespace?: string): Promise<ConfigInstance[]> {
    try {
      const instancesDir = path.join(this.configBasePath, 'instances');
      const files = await fs.readdir(instancesDir);
      
      const instances: ConfigInstance[] = [];
      for (const file of files) {
        if (file.endsWith('.yaml')) {
          const content = await fs.readFile(path.join(instancesDir, file), 'utf-8');
          const instance = yaml.parse(content);
          
          if ((!environment || instance.environment === environment) &&
              (!namespace || instance.namespace === namespace)) {
            instances.push(instance);
          }
        }
      }

      return instances.sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime());
    } catch (error) {
      logger.error('获取实例列表失败:', error);
      return [];
    }
  }

  /**
   * 渲染配置
   */
  async renderConfig(instanceId: string): Promise<string> {
    const instance = await this.getInstance(instanceId);
    if (!instance) {
      throw new Error(`配置实例不存在: ${instanceId}`);
    }

    const template = await this.getTemplate(instance.templateId);
    if (!template) {
      throw new Error(`配置模板不存在: ${instance.templateId}`);
    }

    // 获取密钥值
    const secretValues = await this.getSecretValues(instance.secrets);

    // 合并配置值和密钥值
    const allValues = {
      ...instance.values,
      ...secretValues,
    };

    // 渲染模板
    const renderedConfig = this.renderTemplate(template.template, allValues);

    return renderedConfig;
  }

  /**
   * 部署配置
   */
  async deployConfig(instanceId: string, deployedBy: string): Promise<void> {
    const instance = await this.getInstance(instanceId);
    if (!instance) {
      throw new Error(`配置实例不存在: ${instanceId}`);
    }

    // 渲染配置
    const renderedConfig = await this.renderConfig(instanceId);

    // 部署到目标环境（这里简化为保存到文件）
    const deployPath = path.join(
      this.configBasePath,
      'deployed',
      instance.environment,
      instance.namespace,
      `${instance.name}.yaml`
    );

    await fs.mkdir(path.dirname(deployPath), { recursive: true });
    await fs.writeFile(deployPath, renderedConfig);

    // 更新实例状态
    await this.updateInstance(instanceId, {
      status: 'active',
      deployedAt: new Date(),
    }, '部署配置');

    // 记录变更
    await this.recordChange({
      id: this.generateId(),
      configId: instanceId,
      changeType: 'deploy',
      reason: '部署配置到环境',
      appliedAt: new Date(),
      createdAt: new Date(),
      createdBy: deployedBy,
    });

    logger.info(`配置部署成功: ${instance.name} -> ${instance.environment}/${instance.namespace}`);
  }

  /**
   * 同步环境配置
   */
  async syncEnvironments(syncConfig: SyncConfig, syncBy: string): Promise<void> {
    logger.info(`开始同步环境配置: ${syncConfig.sourceEnvironment} -> ${syncConfig.targetEnvironment}`);

    const sourceInstances = await this.getInstances(syncConfig.sourceEnvironment);
    
    for (const sourceInstance of sourceInstances) {
      // 检查是否在同步列表中
      if (syncConfig.configTemplates.length > 0 && 
          !syncConfig.configTemplates.includes(sourceInstance.templateId)) {
        continue;
      }

      // 应用转换规则
      const transformedValues = this.applyTransformRules(
        sourceInstance.values,
        syncConfig.transformRules
      );

      // 创建或更新目标环境的配置实例
      const targetInstanceId = `${sourceInstance.name}-${syncConfig.targetEnvironment}`;
      const existingTargetInstance = await this.getInstance(targetInstanceId);

      if (existingTargetInstance) {
        await this.updateInstance(targetInstanceId, {
          values: transformedValues,
          environment: syncConfig.targetEnvironment,
        }, `从 ${syncConfig.sourceEnvironment} 环境同步`);
      } else {
        await this.createInstance({
          templateId: sourceInstance.templateId,
          environment: syncConfig.targetEnvironment,
          namespace: sourceInstance.namespace,
          name: sourceInstance.name,
          values: transformedValues,
          secrets: syncConfig.excludeSecrets ? [] : sourceInstance.secrets,
          status: 'draft',
          version: sourceInstance.version,
          createdBy: syncBy,
        });
      }
    }

    logger.info(`环境配置同步完成: ${syncConfig.sourceEnvironment} -> ${syncConfig.targetEnvironment}`);
  }

  /**
   * 获取变更历史
   */
  async getChangeHistory(configId?: string, limit: number = 100): Promise<ConfigChange[]> {
    try {
      const changesDir = path.join(this.configBasePath, 'changes');
      const files = await fs.readdir(changesDir);
      
      const changes: ConfigChange[] = [];
      for (const file of files) {
        if (file.endsWith('.yaml')) {
          const content = await fs.readFile(path.join(changesDir, file), 'utf-8');
          const change = yaml.parse(content);
          
          if (!configId || change.configId === configId) {
            changes.push(change);
          }
        }
      }

      return changes
        .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
        .slice(0, limit);
    } catch (error) {
      logger.error('获取变更历史失败:', error);
      return [];
    }
  }

  // 私有方法

  private async validateTemplate(template: ConfigTemplate): Promise<void> {
    // 验证模板语法
    try {
      this.renderTemplate(template.template, {});
    } catch (error) {
      throw new Error(`模板语法错误: ${error}`);
    }

    // 验证变量定义
    for (const variable of template.variables) {
      if (variable.validation) {
        if (variable.validation.pattern) {
          try {
            new RegExp(variable.validation.pattern);
          } catch (error) {
            throw new Error(`变量 ${variable.name} 的正则表达式无效: ${variable.validation.pattern}`);
          }
        }
      }
    }
  }

  private async validateInstanceValues(template: ConfigTemplate, values: Record<string, any>): Promise<void> {
    for (const variable of template.variables) {
      const value = values[variable.name];

      // 检查必填字段
      if (variable.required && (value === undefined || value === null)) {
        throw new Error(`必填字段缺失: ${variable.name}`);
      }

      // 类型检查
      if (value !== undefined && value !== null) {
        if (variable.type === 'number' && typeof value !== 'number') {
          throw new Error(`字段 ${variable.name} 应为数字类型`);
        }
        if (variable.type === 'boolean' && typeof value !== 'boolean') {
          throw new Error(`字段 ${variable.name} 应为布尔类型`);
        }
      }

      // 验证规则检查
      if (variable.validation && value !== undefined && value !== null) {
        const validation = variable.validation;

        if (validation.pattern && typeof value === 'string') {
          const regex = new RegExp(validation.pattern);
          if (!regex.test(value)) {
            throw new Error(`字段 ${variable.name} 不符合格式要求: ${validation.pattern}`);
          }
        }

        if (validation.min !== undefined && typeof value === 'number' && value < validation.min) {
          throw new Error(`字段 ${variable.name} 不能小于 ${validation.min}`);
        }

        if (validation.max !== undefined && typeof value === 'number' && value > validation.max) {
          throw new Error(`字段 ${variable.name} 不能大于 ${validation.max}`);
        }

        if (validation.enum && !validation.enum.includes(value)) {
          throw new Error(`字段 ${variable.name} 必须是以下值之一: ${validation.enum.join(', ')}`);
        }
      }
    }
  }

  private async processSecrets(instance: ConfigInstance, template: ConfigTemplate): Promise<void> {
    const secrets: string[] = [];

    for (const variable of template.variables) {
      if (variable.type === 'secret' && instance.values[variable.name]) {
        const secretPath = `secret/data/${instance.environment}/${instance.namespace}/${variable.name}`;
        
        // 存储密钥到Vault
        await this.vaultClient.write(secretPath, {
          data: {
            value: instance.values[variable.name],
          },
        });

        secrets.push(secretPath);
        
        // 从配置值中移除密钥
        delete instance.values[variable.name];
      }
    }

    instance.secrets = secrets;
  }

  private async getSecretValues(secretPaths: string[]): Promise<Record<string, any>> {
    const secretValues: Record<string, any> = {};

    for (const secretPath of secretPaths) {
      try {
        const result = await this.vaultClient.read(secretPath);
        const secretName = path.basename(secretPath);
        secretValues[secretName] = result.data.data.value;
      } catch (error) {
        logger.warn(`获取密钥失败: ${secretPath}`, error);
      }
    }

    return secretValues;
  }

  private renderTemplate(template: string, values: Record<string, any>): string {
    // 简单的模板渲染实现（实际应该使用更强大的模板引擎）
    let rendered = template;
    
    for (const [key, value] of Object.entries(values)) {
      const placeholder = new RegExp(`\\{\\{\\s*${key}\\s*\\}\\}`, 'g');
      rendered = rendered.replace(placeholder, String(value));
    }

    return rendered;
  }

  private applyTransformRules(values: Record<string, any>, rules: TransformRule[]): Record<string, any> {
    const transformed = { ...values };

    for (const rule of rules) {
      if (transformed[rule.field] !== undefined) {
        switch (rule.operation) {
          case 'replace':
            transformed[rule.field] = rule.value;
            break;
          case 'prefix':
            transformed[rule.field] = rule.value + transformed[rule.field];
            break;
          case 'suffix':
            transformed[rule.field] = transformed[rule.field] + rule.value;
            break;
          case 'transform':
            // 简单的字符串替换
            if (typeof transformed[rule.field] === 'string') {
              transformed[rule.field] = transformed[rule.field].replace(
                new RegExp(rule.condition || '', 'g'),
                rule.value
              );
            }
            break;
        }
      }
    }

    return transformed;
  }

  private async saveTemplate(template: ConfigTemplate): Promise<void> {
    const templatePath = path.join(this.configBasePath, 'templates', `${template.id}.yaml`);
    await fs.mkdir(path.dirname(templatePath), { recursive: true });
    await fs.writeFile(templatePath, yaml.stringify(template));
  }

  private async saveInstance(instance: ConfigInstance): Promise<void> {
    const instancePath = path.join(this.configBasePath, 'instances', `${instance.id}.yaml`);
    await fs.mkdir(path.dirname(instancePath), { recursive: true });
    await fs.writeFile(instancePath, yaml.stringify(instance));
  }

  private async recordChange(change: ConfigChange): Promise<void> {
    const changePath = path.join(this.configBasePath, 'changes', `${change.id}.yaml`);
    await fs.mkdir(path.dirname(changePath), { recursive: true });
    await fs.writeFile(changePath, yaml.stringify(change));
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }
}
