# 自动驾驶开发加速系统 - 开发工具服务主入口
import asyncio
import logging
import sys
from contextlib import asynccontextmanager
from pathlib import Path

import uvicorn
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.gzip import GZipMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
from prometheus_fastapi_instrumentator import Instrumentator

from app.core.config import settings
from app.core.database import init_db, close_db
from app.core.logging import setup_logging
from app.core.exceptions import CustomException
from app.middleware.auth import AuthMiddleware
from app.middleware.request_id import RequestIDMiddleware
from app.middleware.timing import TimingMiddleware
from app.api.v1 import api_router

# 设置日志
setup_logging()
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("🚀 开发工具服务启动中...")
    
    # 初始化数据库
    await init_db()
    logger.info("✅ 数据库初始化完成")
    
    # 创建必要的目录
    Path(settings.TEMPLATE_STORAGE_PATH).mkdir(parents=True, exist_ok=True)
    Path(settings.PROJECT_STORAGE_PATH).mkdir(parents=True, exist_ok=True)
    Path(settings.UPLOAD_PATH).mkdir(parents=True, exist_ok=True)
    logger.info("✅ 存储目录创建完成")
    
    logger.info("🎉 开发工具服务启动成功")
    
    yield
    
    # 关闭时执行
    logger.info("🛑 开发工具服务关闭中...")
    await close_db()
    logger.info("✅ 数据库连接已关闭")
    logger.info("👋 开发工具服务已关闭")

# 创建FastAPI应用
app = FastAPI(
    title="自动驾驶开发加速系统 - 开发工具服务",
    description="提供代码模板管理、项目脚手架生成、版本控制集成等开发工具功能",
    version="1.0.0",
    docs_url="/docs" if settings.DEBUG else None,
    redoc_url="/redoc" if settings.DEBUG else None,
    openapi_url="/openapi.json" if settings.DEBUG else None,
    lifespan=lifespan
)

# 添加中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_HOSTS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(GZipMiddleware, minimum_size=1000)
app.add_middleware(RequestIDMiddleware)
app.add_middleware(TimingMiddleware)
app.add_middleware(AuthMiddleware)

# 设置Prometheus监控
if settings.ENABLE_METRICS:
    instrumentator = Instrumentator(
        should_group_status_codes=False,
        should_ignore_untemplated=True,
        should_respect_env_var=True,
        should_instrument_requests_inprogress=True,
        excluded_handlers=["/health", "/metrics"],
        env_var_name="ENABLE_METRICS",
        inprogress_name="inprogress",
        inprogress_labels=True,
    )
    instrumentator.instrument(app).expose(app)

# 静态文件服务
if settings.DEBUG:
    app.mount("/static", StaticFiles(directory="static"), name="static")

# 注册路由
app.include_router(api_router, prefix="/api/v1")

# 全局异常处理器
@app.exception_handler(CustomException)
async def custom_exception_handler(request: Request, exc: CustomException):
    """自定义异常处理器"""
    logger.error(f"自定义异常: {exc.message}", extra={
        "error_code": exc.error_code,
        "request_id": getattr(request.state, "request_id", None),
        "path": request.url.path,
        "method": request.method,
    })
    
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "code": exc.error_code,
            "message": exc.message,
            "details": exc.details,
            "timestamp": exc.timestamp,
            "request_id": getattr(request.state, "request_id", None),
        }
    )

@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理器"""
    logger.error(f"未处理的异常: {str(exc)}", exc_info=True, extra={
        "request_id": getattr(request.state, "request_id", None),
        "path": request.url.path,
        "method": request.method,
    })
    
    return JSONResponse(
        status_code=500,
        content={
            "code": 50000,
            "message": "服务器内部错误" if not settings.DEBUG else str(exc),
            "details": None,
            "timestamp": asyncio.get_event_loop().time(),
            "request_id": getattr(request.state, "request_id", None),
        }
    )

# 健康检查端点
@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "development-tools",
        "version": "1.0.0",
        "timestamp": asyncio.get_event_loop().time(),
    }

# 就绪检查端点
@app.get("/ready")
async def readiness_check():
    """就绪检查"""
    # 这里可以添加数据库连接检查等
    return {
        "status": "ready",
        "service": "development-tools",
        "version": "1.0.0",
        "timestamp": asyncio.get_event_loop().time(),
    }

# 根路径
@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "自动驾驶开发加速系统 - 开发工具服务",
        "version": "1.0.0",
        "docs": "/docs" if settings.DEBUG else None,
    }

def main():
    """主函数"""
    try:
        # 检查Python版本
        if sys.version_info < (3, 8):
            logger.error("Python 3.8+ 是必需的")
            sys.exit(1)
        
        # 启动服务器
        uvicorn.run(
            "app.main:app",
            host=settings.HOST,
            port=settings.PORT,
            reload=settings.DEBUG,
            workers=1 if settings.DEBUG else settings.WORKERS,
            log_config=None,  # 使用自定义日志配置
            access_log=False,  # 禁用默认访问日志
        )
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭服务...")
    except Exception as e:
        logger.error(f"启动服务失败: {e}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    main()
