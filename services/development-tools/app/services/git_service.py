# 自动驾驶开发加速系统 - Git版本控制服务
import os
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from uuid import UUID

import git
from git import Repo, InvalidGitRepositoryError, GitCommandError
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.core.exceptions import CustomException
from app.models.git_repository import GitRepository, GitBranch, GitCommit, GitRemote
from app.utils.file_utils import ensure_directory

class GitService:
    """Git版本控制服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.repositories_path = Path(settings.GIT_REPOSITORIES_PATH)
        self.repositories_path.mkdir(parents=True, exist_ok=True)
    
    async def init_repository(self, 
                            project_path: str,
                            user_id: UUID,
                            config: Dict[str, Any]) -> GitRepository:
        """初始化Git仓库"""
        try:
            repo_path = Path(project_path)
            
            # 检查目录是否存在
            if not repo_path.exists():
                raise CustomException(
                    message="项目目录不存在",
                    error_code=40401
                )
            
            # 初始化Git仓库
            repo = git.Repo.init(str(repo_path))
            
            # 配置用户信息
            if config.get("user_name"):
                repo.config_writer().set_value("user", "name", config["user_name"]).release()
            if config.get("user_email"):
                repo.config_writer().set_value("user", "email", config["user_email"]).release()
            
            # 配置其他Git设置
            repo.config_writer().set_value("init", "defaultBranch", config.get("default_branch", "main")).release()
            repo.config_writer().set_value("core", "autocrlf", "input").release()
            repo.config_writer().set_value("core", "ignorecase", "false").release()
            
            # 创建.gitignore文件
            gitignore_content = self._generate_gitignore(config.get("language", ""), config.get("framework", ""))
            gitignore_path = repo_path / ".gitignore"
            with open(gitignore_path, "w", encoding="utf-8") as f:
                f.write(gitignore_content)
            
            # 创建README.md文件
            readme_content = self._generate_readme(config)
            readme_path = repo_path / "README.md"
            with open(readme_path, "w", encoding="utf-8") as f:
                f.write(readme_content)
            
            # 添加所有文件到暂存区
            repo.git.add(".")
            
            # 初始提交
            initial_commit_message = config.get("initial_commit_message", "Initial commit")
            initial_commit = repo.index.commit(initial_commit_message)
            
            # 创建数据库记录
            git_repo = GitRepository(
                name=config.get("repository_name", repo_path.name),
                path=str(repo_path),
                owner_id=user_id,
                default_branch=config.get("default_branch", "main"),
                description=config.get("description", ""),
                is_private=config.get("is_private", True),
                config_data=config
            )
            
            self.db.add(git_repo)
            await self.db.flush()
            
            # 记录初始分支
            main_branch = GitBranch(
                repository_id=git_repo.id,
                name=config.get("default_branch", "main"),
                commit_hash=initial_commit.hexsha,
                is_default=True,
                is_protected=True
            )
            
            self.db.add(main_branch)
            
            # 记录初始提交
            git_commit = GitCommit(
                repository_id=git_repo.id,
                hash=initial_commit.hexsha,
                message=initial_commit_message,
                author_name=config.get("user_name", "Unknown"),
                author_email=config.get("user_email", "<EMAIL>"),
                committed_at=datetime.fromtimestamp(initial_commit.committed_date)
            )
            
            self.db.add(git_commit)
            await self.db.commit()
            
            return git_repo
            
        except GitCommandError as e:
            raise CustomException(
                message=f"Git操作失败: {str(e)}",
                error_code=50001
            )
        except Exception as e:
            raise CustomException(
                message=f"初始化仓库失败: {str(e)}",
                error_code=50002
            )
    
    async def clone_repository(self, 
                             remote_url: str,
                             local_path: str,
                             user_id: UUID,
                             config: Dict[str, Any]) -> GitRepository:
        """克隆远程仓库"""
        try:
            local_repo_path = Path(local_path)
            
            # 确保父目录存在
            local_repo_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 克隆仓库
            clone_kwargs = {
                "branch": config.get("branch", "main"),
                "depth": config.get("depth", None),
                "single_branch": config.get("single_branch", False)
            }
            
            # 移除None值
            clone_kwargs = {k: v for k, v in clone_kwargs.items() if v is not None}
            
            repo = git.Repo.clone_from(remote_url, str(local_repo_path), **clone_kwargs)
            
            # 配置用户信息
            if config.get("user_name"):
                repo.config_writer().set_value("user", "name", config["user_name"]).release()
            if config.get("user_email"):
                repo.config_writer().set_value("user", "email", config["user_email"]).release()
            
            # 创建数据库记录
            git_repo = GitRepository(
                name=config.get("repository_name", local_repo_path.name),
                path=str(local_repo_path),
                owner_id=user_id,
                remote_url=remote_url,
                default_branch=repo.active_branch.name,
                description=config.get("description", ""),
                is_private=config.get("is_private", True),
                config_data=config
            )
            
            self.db.add(git_repo)
            await self.db.flush()
            
            # 记录远程仓库
            for remote in repo.remotes:
                git_remote = GitRemote(
                    repository_id=git_repo.id,
                    name=remote.name,
                    url=list(remote.urls)[0],
                    is_default=remote.name == "origin"
                )
                self.db.add(git_remote)
            
            # 记录分支信息
            for branch in repo.branches:
                git_branch = GitBranch(
                    repository_id=git_repo.id,
                    name=branch.name,
                    commit_hash=branch.commit.hexsha,
                    is_default=branch.name == repo.active_branch.name
                )
                self.db.add(git_branch)
            
            await self.db.commit()
            
            return git_repo
            
        except GitCommandError as e:
            raise CustomException(
                message=f"克隆仓库失败: {str(e)}",
                error_code=50003
            )
        except Exception as e:
            raise CustomException(
                message=f"克隆操作失败: {str(e)}",
                error_code=50004
            )
    
    async def create_branch(self, 
                          repository_id: UUID,
                          branch_name: str,
                          source_branch: str = "main") -> GitBranch:
        """创建新分支"""
        try:
            # 获取仓库信息
            git_repo = await self.db.get(GitRepository, repository_id)
            if not git_repo:
                raise CustomException(
                    message="仓库不存在",
                    error_code=40401
                )
            
            repo = git.Repo(git_repo.path)
            
            # 检查分支是否已存在
            if branch_name in [b.name for b in repo.branches]:
                raise CustomException(
                    message=f"分支 '{branch_name}' 已存在",
                    error_code=40001
                )
            
            # 切换到源分支
            repo.git.checkout(source_branch)
            
            # 创建新分支
            new_branch = repo.create_head(branch_name)
            repo.git.checkout(new_branch)
            
            # 创建数据库记录
            git_branch = GitBranch(
                repository_id=repository_id,
                name=branch_name,
                commit_hash=new_branch.commit.hexsha,
                source_branch=source_branch,
                is_default=False,
                is_protected=False
            )
            
            self.db.add(git_branch)
            await self.db.commit()
            
            return git_branch
            
        except GitCommandError as e:
            raise CustomException(
                message=f"创建分支失败: {str(e)}",
                error_code=50005
            )
        except Exception as e:
            raise CustomException(
                message=f"分支操作失败: {str(e)}",
                error_code=50006
            )
    
    async def commit_changes(self, 
                           repository_id: UUID,
                           message: str,
                           files: Optional[List[str]] = None,
                           author_name: Optional[str] = None,
                           author_email: Optional[str] = None) -> GitCommit:
        """提交更改"""
        try:
            # 获取仓库信息
            git_repo = await self.db.get(GitRepository, repository_id)
            if not git_repo:
                raise CustomException(
                    message="仓库不存在",
                    error_code=40401
                )
            
            repo = git.Repo(git_repo.path)
            
            # 检查是否有更改
            if not repo.is_dirty() and not repo.untracked_files:
                raise CustomException(
                    message="没有需要提交的更改",
                    error_code=40002
                )
            
            # 添加文件到暂存区
            if files:
                for file_path in files:
                    repo.git.add(file_path)
            else:
                repo.git.add(".")
            
            # 设置作者信息
            commit_kwargs = {}
            if author_name and author_email:
                commit_kwargs["author"] = git.Actor(author_name, author_email)
            
            # 提交更改
            commit = repo.index.commit(message, **commit_kwargs)
            
            # 创建数据库记录
            git_commit = GitCommit(
                repository_id=repository_id,
                hash=commit.hexsha,
                message=message,
                author_name=commit.author.name,
                author_email=commit.author.email,
                committed_at=datetime.fromtimestamp(commit.committed_date),
                files_changed=len(commit.stats.files) if commit.stats else 0,
                insertions=commit.stats.total["insertions"] if commit.stats else 0,
                deletions=commit.stats.total["deletions"] if commit.stats else 0
            )
            
            self.db.add(git_commit)
            
            # 更新分支的最新提交
            current_branch = repo.active_branch.name
            await self.db.execute(
                update(GitBranch)
                .where(GitBranch.repository_id == repository_id)
                .where(GitBranch.name == current_branch)
                .values(commit_hash=commit.hexsha, updated_at=datetime.utcnow())
            )
            
            await self.db.commit()
            
            return git_commit
            
        except GitCommandError as e:
            raise CustomException(
                message=f"提交失败: {str(e)}",
                error_code=50007
            )
        except Exception as e:
            raise CustomException(
                message=f"提交操作失败: {str(e)}",
                error_code=50008
            )
    
    async def push_changes(self, 
                         repository_id: UUID,
                         remote_name: str = "origin",
                         branch_name: Optional[str] = None) -> bool:
        """推送更改到远程仓库"""
        try:
            # 获取仓库信息
            git_repo = await self.db.get(GitRepository, repository_id)
            if not git_repo:
                raise CustomException(
                    message="仓库不存在",
                    error_code=40401
                )
            
            repo = git.Repo(git_repo.path)
            
            # 获取远程仓库
            if remote_name not in [r.name for r in repo.remotes]:
                raise CustomException(
                    message=f"远程仓库 '{remote_name}' 不存在",
                    error_code=40402
                )
            
            remote = repo.remote(remote_name)
            
            # 确定要推送的分支
            if not branch_name:
                branch_name = repo.active_branch.name
            
            # 推送更改
            push_info = remote.push(f"{branch_name}:{branch_name}")
            
            # 检查推送结果
            for info in push_info:
                if info.flags & info.ERROR:
                    raise CustomException(
                        message=f"推送失败: {info.summary}",
                        error_code=50009
                    )
            
            return True
            
        except GitCommandError as e:
            raise CustomException(
                message=f"推送失败: {str(e)}",
                error_code=50010
            )
        except Exception as e:
            raise CustomException(
                message=f"推送操作失败: {str(e)}",
                error_code=50011
            )
    
    async def pull_changes(self, 
                         repository_id: UUID,
                         remote_name: str = "origin",
                         branch_name: Optional[str] = None) -> bool:
        """拉取远程更改"""
        try:
            # 获取仓库信息
            git_repo = await self.db.get(GitRepository, repository_id)
            if not git_repo:
                raise CustomException(
                    message="仓库不存在",
                    error_code=40401
                )
            
            repo = git.Repo(git_repo.path)
            
            # 获取远程仓库
            if remote_name not in [r.name for r in repo.remotes]:
                raise CustomException(
                    message=f"远程仓库 '{remote_name}' 不存在",
                    error_code=40402
                )
            
            remote = repo.remote(remote_name)
            
            # 确定要拉取的分支
            if not branch_name:
                branch_name = repo.active_branch.name
            
            # 拉取更改
            pull_info = remote.pull(branch_name)
            
            return True
            
        except GitCommandError as e:
            raise CustomException(
                message=f"拉取失败: {str(e)}",
                error_code=50012
            )
        except Exception as e:
            raise CustomException(
                message=f"拉取操作失败: {str(e)}",
                error_code=50013
            )
    
    def _generate_gitignore(self, language: str, framework: str) -> str:
        """生成.gitignore文件内容"""
        gitignore_templates = {
            "python": """
# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db
""",
            "javascript": """
# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.test

# Build outputs
dist/
build/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db
""",
            "go": """
# Go
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool
*.out

# Dependency directories
vendor/

# Go workspace file
go.work

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db
""",
            "rust": """
# Rust
/target/
**/*.rs.bk
Cargo.lock

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db
""",
            "java": """
# Java
*.class
*.log
*.ctxt
.mtj.tmp/
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# Gradle
.gradle
build/
!gradle/wrapper/gradle-wrapper.jar
!**/src/main/**/build/
!**/src/test/**/build/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db
"""
        }
        
        base_gitignore = """
# General
*.log
*.tmp
*.temp
.cache/
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDEs and editors
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
"""
        
        language_gitignore = gitignore_templates.get(language.lower(), "")
        
        return base_gitignore + language_gitignore
    
    def _generate_readme(self, config: Dict[str, Any]) -> str:
        """生成README.md文件内容"""
        project_name = config.get("project_name", "New Project")
        description = config.get("description", "A new project created with the autonomous driving development platform.")
        language = config.get("language", "")
        framework = config.get("framework", "")
        
        readme_content = f"""# {project_name}

{description}

## 技术栈

"""
        
        if language:
            readme_content += f"- **编程语言**: {language}\n"
        
        if framework:
            readme_content += f"- **框架**: {framework}\n"
        
        readme_content += """
## 快速开始

### 环境要求

请确保您的开发环境满足以下要求：

### 安装依赖

```bash
# 安装项目依赖
```

### 运行项目

```bash
# 启动项目
```

## 项目结构

```
.
├── README.md
└── ...
```

## 贡献指南

1. Fork 本仓库
2. 创建您的特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交您的更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开一个 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

如有问题，请联系项目维护者。
"""
        
        return readme_content
