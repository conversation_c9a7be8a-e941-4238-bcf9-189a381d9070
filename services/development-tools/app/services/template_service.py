# 自动驾驶开发加速系统 - 模板服务
import os
import json
import shutil
import zipfile
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
from uuid import UUID, uuid4

import aiofiles
from jinja2 import Environment, FileSystemLoader, Template as Jinja2Template
from jinja2.exceptions import TemplateError, TemplateSyntaxError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, and_, or_, func
from sqlalchemy.orm import selectinload

from app.core.config import settings
from app.core.exceptions import CustomException
from app.models.template import (
    Template, TemplateVersion, TemplateFile, TemplateParameter,
    TemplateReview, TemplateUsage, TemplateType, TemplateStatus
)
from app.schemas.template import (
    TemplateCreate, TemplateUpdate, TemplateQuery,
    TemplateFileCreate, TemplateParameterCreate
)
from app.utils.file_utils import calculate_file_hash, get_file_type
from app.utils.validation import validate_template_config

class TemplateService:
    """模板管理服务"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.template_storage = Path(settings.TEMPLATE_STORAGE_PATH)
        self.jinja_env = Environment(
            loader=FileSystemLoader(str(self.template_storage)),
            autoescape=False,
            trim_blocks=True,
            lstrip_blocks=True,
        )
    
    async def create_template(self, template_data: TemplateCreate, author_id: UUID) -> Template:
        """创建新模板"""
        # 检查模板名称是否已存在
        existing = await self.db.execute(
            select(Template).where(Template.name == template_data.name)
        )
        if existing.scalar_one_or_none():
            raise CustomException(
                message=f"模板名称 '{template_data.name}' 已存在",
                error_code=40001
            )
        
        # 创建模板记录
        template = Template(
            name=template_data.name,
            display_name=template_data.display_name,
            description=template_data.description,
            type=template_data.type,
            category=template_data.category,
            tags=template_data.tags or [],
            language=template_data.language,
            framework=template_data.framework,
            version=template_data.version or "1.0.0",
            config_schema=template_data.config_schema or {},
            default_config=template_data.default_config or {},
            author_id=author_id,
            author_name=template_data.author_name,
            license=template_data.license,
            homepage=template_data.homepage,
            repository=template_data.repository,
            is_public=template_data.is_public,
        )
        
        self.db.add(template)
        await self.db.flush()
        
        # 创建模板存储目录
        template_dir = self.template_storage / str(template.id)
        template_dir.mkdir(parents=True, exist_ok=True)
        
        # 更新模板路径
        template.template_path = str(template_dir)
        
        await self.db.commit()
        await self.db.refresh(template)
        
        return template
    
    async def get_template(self, template_id: UUID, include_files: bool = False) -> Optional[Template]:
        """获取模板详情"""
        query = select(Template).where(Template.id == template_id)
        
        if include_files:
            query = query.options(
                selectinload(Template.files),
                selectinload(Template.versions),
                selectinload(Template.reviews)
            )
        
        result = await self.db.execute(query)
        return result.scalar_one_or_none()
    
    async def list_templates(self, query_params: TemplateQuery) -> Tuple[List[Template], int]:
        """获取模板列表"""
        # 构建查询条件
        conditions = []
        
        if query_params.type:
            conditions.append(Template.type == query_params.type)
        
        if query_params.category:
            conditions.append(Template.category == query_params.category)
        
        if query_params.language:
            conditions.append(Template.language == query_params.language)
        
        if query_params.framework:
            conditions.append(Template.framework == query_params.framework)
        
        if query_params.status:
            conditions.append(Template.status == query_params.status)
        
        if query_params.is_public is not None:
            conditions.append(Template.is_public == query_params.is_public)
        
        if query_params.is_featured is not None:
            conditions.append(Template.is_featured == query_params.is_featured)
        
        if query_params.author_id:
            conditions.append(Template.author_id == query_params.author_id)
        
        if query_params.search:
            search_condition = or_(
                Template.name.ilike(f"%{query_params.search}%"),
                Template.display_name.ilike(f"%{query_params.search}%"),
                Template.description.ilike(f"%{query_params.search}%"),
                Template.tags.op("@>")(json.dumps([query_params.search]))
            )
            conditions.append(search_condition)
        
        # 构建基础查询
        base_query = select(Template)
        if conditions:
            base_query = base_query.where(and_(*conditions))
        
        # 获取总数
        count_query = select(func.count()).select_from(base_query.subquery())
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()
        
        # 添加排序
        if query_params.sort_by:
            sort_column = getattr(Template, query_params.sort_by, None)
            if sort_column:
                if query_params.sort_order == "desc":
                    base_query = base_query.order_by(sort_column.desc())
                else:
                    base_query = base_query.order_by(sort_column.asc())
        else:
            base_query = base_query.order_by(Template.created_at.desc())
        
        # 添加分页
        if query_params.page and query_params.page_size:
            offset = (query_params.page - 1) * query_params.page_size
            base_query = base_query.offset(offset).limit(query_params.page_size)
        
        # 执行查询
        result = await self.db.execute(base_query)
        templates = result.scalars().all()
        
        return list(templates), total
    
    async def update_template(self, template_id: UUID, template_data: TemplateUpdate) -> Template:
        """更新模板"""
        template = await self.get_template(template_id)
        if not template:
            raise CustomException(
                message="模板不存在",
                error_code=40401
            )
        
        # 更新字段
        update_data = template_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(template, field, value)
        
        template.updated_at = datetime.utcnow()
        
        await self.db.commit()
        await self.db.refresh(template)
        
        return template
    
    async def delete_template(self, template_id: UUID) -> bool:
        """删除模板"""
        template = await self.get_template(template_id)
        if not template:
            return False
        
        # 删除模板文件
        template_dir = Path(template.template_path)
        if template_dir.exists():
            shutil.rmtree(template_dir)
        
        # 删除数据库记录
        await self.db.delete(template)
        await self.db.commit()
        
        return True
    
    async def upload_template_files(self, template_id: UUID, files: List[Dict[str, Any]]) -> List[TemplateFile]:
        """上传模板文件"""
        template = await self.get_template(template_id)
        if not template:
            raise CustomException(
                message="模板不存在",
                error_code=40401
            )
        
        template_dir = Path(template.template_path)
        template_files = []
        
        for file_data in files:
            filename = file_data["filename"]
            content = file_data["content"]
            relative_path = file_data.get("relative_path", filename)
            
            # 创建文件路径
            file_path = template_dir / relative_path
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 写入文件内容
            if isinstance(content, bytes):
                async with aiofiles.open(file_path, "wb") as f:
                    await f.write(content)
                is_binary = True
                content_str = None
            else:
                async with aiofiles.open(file_path, "w", encoding="utf-8") as f:
                    await f.write(content)
                is_binary = False
                content_str = content
            
            # 计算文件哈希
            content_hash = calculate_file_hash(file_path)
            file_size = file_path.stat().st_size
            file_type = get_file_type(filename)
            
            # 创建文件记录
            template_file = TemplateFile(
                template_id=template_id,
                filename=filename,
                filepath=str(file_path),
                relative_path=relative_path,
                file_type=file_type,
                content=content_str,
                content_hash=content_hash,
                file_size=file_size,
                is_binary=is_binary,
                is_template=file_data.get("is_template", True),
                encoding=file_data.get("encoding", "utf-8"),
                process_options=file_data.get("process_options", {})
            )
            
            self.db.add(template_file)
            template_files.append(template_file)
        
        await self.db.commit()
        
        return template_files
    
    async def render_template(self, template_id: UUID, config: Dict[str, Any]) -> Dict[str, Any]:
        """渲染模板"""
        template = await self.get_template(template_id, include_files=True)
        if not template:
            raise CustomException(
                message="模板不存在",
                error_code=40401
            )
        
        # 验证配置参数
        if template.config_schema:
            validation_result = validate_template_config(config, template.config_schema)
            if not validation_result.is_valid:
                raise CustomException(
                    message="配置参数验证失败",
                    error_code=40002,
                    details=validation_result.errors
                )
        
        # 合并默认配置
        merged_config = {**(template.default_config or {}), **config}
        
        # 渲染文件
        rendered_files = {}
        
        for template_file in template.files:
            if not template_file.is_template or template_file.is_binary:
                # 非模板文件或二进制文件直接复制
                rendered_files[template_file.relative_path] = {
                    "content": template_file.content,
                    "is_binary": template_file.is_binary,
                    "encoding": template_file.encoding
                }
                continue
            
            try:
                # 渲染Jinja2模板
                jinja_template = self.jinja_env.from_string(template_file.content)
                rendered_content = jinja_template.render(**merged_config)
                
                rendered_files[template_file.relative_path] = {
                    "content": rendered_content,
                    "is_binary": False,
                    "encoding": template_file.encoding
                }
            except TemplateError as e:
                raise CustomException(
                    message=f"模板渲染失败: {str(e)}",
                    error_code=40003,
                    details={"file": template_file.relative_path, "error": str(e)}
                )
        
        return {
            "template_id": str(template_id),
            "template_name": template.name,
            "config": merged_config,
            "files": rendered_files
        }
    
    async def preview_template(self, template_id: UUID, config: Dict[str, Any]) -> Dict[str, Any]:
        """预览模板渲染结果"""
        return await self.render_template(template_id, config)
    
    async def validate_template(self, template_id: UUID) -> Dict[str, Any]:
        """验证模板"""
        template = await self.get_template(template_id, include_files=True)
        if not template:
            raise CustomException(
                message="模板不存在",
                error_code=40401
            )
        
        validation_result = {
            "template_id": str(template_id),
            "is_valid": True,
            "errors": [],
            "warnings": []
        }
        
        # 验证模板文件语法
        for template_file in template.files:
            if not template_file.is_template or template_file.is_binary:
                continue
            
            try:
                # 验证Jinja2语法
                self.jinja_env.from_string(template_file.content)
            except TemplateSyntaxError as e:
                validation_result["is_valid"] = False
                validation_result["errors"].append({
                    "file": template_file.relative_path,
                    "type": "syntax_error",
                    "message": str(e),
                    "line": e.lineno
                })
            except Exception as e:
                validation_result["warnings"].append({
                    "file": template_file.relative_path,
                    "type": "warning",
                    "message": str(e)
                })
        
        # 验证配置架构
        if template.config_schema:
            try:
                # 这里可以添加JSON Schema验证
                pass
            except Exception as e:
                validation_result["warnings"].append({
                    "type": "config_schema",
                    "message": f"配置架构验证警告: {str(e)}"
                })
        
        return validation_result
    
    async def publish_template(self, template_id: UUID) -> Template:
        """发布模板"""
        template = await self.get_template(template_id)
        if not template:
            raise CustomException(
                message="模板不存在",
                error_code=40401
            )
        
        # 验证模板
        validation_result = await self.validate_template(template_id)
        if not validation_result["is_valid"]:
            raise CustomException(
                message="模板验证失败，无法发布",
                error_code=40004,
                details=validation_result["errors"]
            )
        
        # 更新状态
        template.status = TemplateStatus.PUBLISHED
        template.published_at = datetime.utcnow()
        
        await self.db.commit()
        await self.db.refresh(template)
        
        return template
    
    async def record_template_usage(self, template_id: UUID, user_id: UUID, 
                                  usage_data: Dict[str, Any]) -> TemplateUsage:
        """记录模板使用"""
        usage = TemplateUsage(
            template_id=template_id,
            user_id=user_id,
            project_name=usage_data.get("project_name"),
            usage_type=usage_data.get("usage_type", "generate"),
            config_data=usage_data.get("config_data", {}),
            generated_files=usage_data.get("generated_files", []),
            success=usage_data.get("success", True),
            error_message=usage_data.get("error_message")
        )
        
        self.db.add(usage)
        
        # 更新模板使用统计
        await self.db.execute(
            update(Template)
            .where(Template.id == template_id)
            .values(usage_count=Template.usage_count + 1)
        )
        
        await self.db.commit()
        
        return usage
