# 自动驾驶开发加速系统 - 项目模型
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Any
from uuid import uuid4

from sqlalchemy import Column, String, Text, DateTime, Boolean, Integer, JSON, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.core.database import Base

class ProjectStatus(str, Enum):
    """项目状态枚举"""
    PENDING = "pending"          # 等待中
    GENERATING = "generating"    # 生成中
    COMPLETED = "completed"      # 已完成
    FAILED = "failed"           # 失败
    ARCHIVED = "archived"       # 已归档

class Project(Base):
    """项目模型"""
    __tablename__ = "projects"

    # 基础字段
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4, comment="项目ID")
    name = Column(String(200), nullable=False, comment="项目名称")
    description = Column(Text, comment="项目描述")
    
    # 模板信息
    template_id = Column(UUID(as_uuid=True), nullable=False, comment="模板ID")
    template_name = Column(String(100), comment="模板名称")
    template_version = Column(String(20), comment="模板版本")
    
    # 项目配置
    config_data = Column(JSON, comment="项目配置数据")
    generated_files = Column(JSON, default=list, comment="生成的文件列表")
    
    # 项目路径
    project_path = Column(String(500), comment="项目路径")
    
    # 状态信息
    status = Column(String(20), nullable=False, default=ProjectStatus.PENDING, comment="项目状态")
    error_message = Column(Text, comment="错误信息")
    
    # 所有者信息
    owner_id = Column(UUID(as_uuid=True), nullable=False, comment="所有者ID")
    
    # 统计信息
    file_count = Column(Integer, default=0, comment="文件数量")
    total_size = Column(Integer, default=0, comment="总大小(字节)")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
    completed_at = Column(DateTime, comment="完成时间")
    
    # 关联关系
    files = relationship("ProjectFile", back_populates="project", cascade="all, delete-orphan")

class ProjectFile(Base):
    """项目文件模型"""
    __tablename__ = "project_files"

    # 基础字段
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4, comment="文件ID")
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False, comment="项目ID")
    
    # 文件信息
    filename = Column(String(255), nullable=False, comment="文件名")
    filepath = Column(String(500), nullable=False, comment="文件路径")
    relative_path = Column(String(500), comment="相对路径")
    
    # 文件属性
    file_size = Column(Integer, comment="文件大小")
    content_hash = Column(String(64), comment="内容哈希")
    is_binary = Column(Boolean, default=False, comment="是否为二进制文件")
    encoding = Column(String(20), default="utf-8", comment="文件编码")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    
    # 关联关系
    project = relationship("Project", back_populates="files")
