# 自动驾驶开发加速系统 - 模板管理模型
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Any
from uuid import uuid4

from sqlalchemy import Column, String, Text, DateTime, Boolean, Integer, JSON, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.ext.declarative import declarative_base

from app.core.database import Base

class TemplateType(str, Enum):
    """模板类型枚举"""
    PROJECT = "project"          # 项目模板
    COMPONENT = "component"      # 组件模板
    SERVICE = "service"          # 服务模板
    LIBRARY = "library"          # 库模板
    CONFIGURATION = "config"     # 配置模板
    DOCUMENTATION = "docs"       # 文档模板

class TemplateStatus(str, Enum):
    """模板状态枚举"""
    DRAFT = "draft"              # 草稿
    PUBLISHED = "published"      # 已发布
    DEPRECATED = "deprecated"    # 已废弃
    ARCHIVED = "archived"        # 已归档

class Template(Base):
    """代码模板模型"""
    __tablename__ = "templates"

    # 基础字段
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4, comment="模板ID")
    name = Column(String(100), nullable=False, comment="模板名称")
    display_name = Column(String(200), nullable=False, comment="显示名称")
    description = Column(Text, comment="模板描述")
    
    # 模板属性
    type = Column(String(20), nullable=False, default=TemplateType.PROJECT, comment="模板类型")
    category = Column(String(50), comment="模板分类")
    tags = Column(JSON, default=list, comment="模板标签")
    language = Column(String(50), comment="编程语言")
    framework = Column(String(100), comment="框架名称")
    
    # 版本信息
    version = Column(String(20), nullable=False, default="1.0.0", comment="模板版本")
    status = Column(String(20), nullable=False, default=TemplateStatus.DRAFT, comment="模板状态")
    
    # 模板内容
    template_path = Column(String(500), comment="模板文件路径")
    config_schema = Column(JSON, comment="配置参数架构")
    default_config = Column(JSON, comment="默认配置参数")
    
    # 元数据
    author_id = Column(UUID(as_uuid=True), nullable=False, comment="作者ID")
    author_name = Column(String(100), comment="作者名称")
    license = Column(String(50), comment="许可证")
    homepage = Column(String(500), comment="主页地址")
    repository = Column(String(500), comment="仓库地址")
    
    # 统计信息
    download_count = Column(Integer, default=0, comment="下载次数")
    usage_count = Column(Integer, default=0, comment="使用次数")
    star_count = Column(Integer, default=0, comment="收藏次数")
    
    # 可见性和权限
    is_public = Column(Boolean, default=True, comment="是否公开")
    is_featured = Column(Boolean, default=False, comment="是否推荐")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
    published_at = Column(DateTime, comment="发布时间")
    
    # 关联关系
    versions = relationship("TemplateVersion", back_populates="template", cascade="all, delete-orphan")
    files = relationship("TemplateFile", back_populates="template", cascade="all, delete-orphan")
    reviews = relationship("TemplateReview", back_populates="template", cascade="all, delete-orphan")

class TemplateVersion(Base):
    """模板版本模型"""
    __tablename__ = "template_versions"

    # 基础字段
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4, comment="版本ID")
    template_id = Column(UUID(as_uuid=True), ForeignKey("templates.id"), nullable=False, comment="模板ID")
    version = Column(String(20), nullable=False, comment="版本号")
    
    # 版本信息
    title = Column(String(200), comment="版本标题")
    description = Column(Text, comment="版本描述")
    changelog = Column(Text, comment="变更日志")
    
    # 版本内容
    template_path = Column(String(500), comment="模板文件路径")
    config_schema = Column(JSON, comment="配置参数架构")
    default_config = Column(JSON, comment="默认配置参数")
    
    # 版本状态
    status = Column(String(20), nullable=False, default=TemplateStatus.DRAFT, comment="版本状态")
    is_latest = Column(Boolean, default=False, comment="是否最新版本")
    
    # 兼容性信息
    min_platform_version = Column(String(20), comment="最小平台版本")
    max_platform_version = Column(String(20), comment="最大平台版本")
    dependencies = Column(JSON, default=list, comment="依赖列表")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    published_at = Column(DateTime, comment="发布时间")
    
    # 关联关系
    template = relationship("Template", back_populates="versions")

class TemplateFile(Base):
    """模板文件模型"""
    __tablename__ = "template_files"

    # 基础字段
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4, comment="文件ID")
    template_id = Column(UUID(as_uuid=True), ForeignKey("templates.id"), nullable=False, comment="模板ID")
    
    # 文件信息
    filename = Column(String(255), nullable=False, comment="文件名")
    filepath = Column(String(500), nullable=False, comment="文件路径")
    relative_path = Column(String(500), comment="相对路径")
    file_type = Column(String(50), comment="文件类型")
    
    # 文件内容
    content = Column(Text, comment="文件内容")
    content_hash = Column(String(64), comment="内容哈希")
    file_size = Column(Integer, comment="文件大小")
    
    # 模板属性
    is_template = Column(Boolean, default=True, comment="是否为模板文件")
    is_binary = Column(Boolean, default=False, comment="是否为二进制文件")
    encoding = Column(String(20), default="utf-8", comment="文件编码")
    
    # 处理选项
    process_options = Column(JSON, comment="处理选项")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
    
    # 关联关系
    template = relationship("Template", back_populates="files")

class TemplateParameter(Base):
    """模板参数模型"""
    __tablename__ = "template_parameters"

    # 基础字段
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4, comment="参数ID")
    template_id = Column(UUID(as_uuid=True), ForeignKey("templates.id"), nullable=False, comment="模板ID")
    
    # 参数信息
    name = Column(String(100), nullable=False, comment="参数名称")
    display_name = Column(String(200), comment="显示名称")
    description = Column(Text, comment="参数描述")
    
    # 参数类型
    type = Column(String(50), nullable=False, comment="参数类型")
    default_value = Column(JSON, comment="默认值")
    required = Column(Boolean, default=False, comment="是否必需")
    
    # 验证规则
    validation_rules = Column(JSON, comment="验证规则")
    options = Column(JSON, comment="选项列表")
    
    # 显示属性
    group = Column(String(100), comment="参数分组")
    order = Column(Integer, default=0, comment="显示顺序")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

class TemplateReview(Base):
    """模板评价模型"""
    __tablename__ = "template_reviews"

    # 基础字段
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4, comment="评价ID")
    template_id = Column(UUID(as_uuid=True), ForeignKey("templates.id"), nullable=False, comment="模板ID")
    
    # 评价信息
    user_id = Column(UUID(as_uuid=True), nullable=False, comment="用户ID")
    user_name = Column(String(100), comment="用户名称")
    rating = Column(Integer, comment="评分(1-5)")
    title = Column(String(200), comment="评价标题")
    content = Column(Text, comment="评价内容")
    
    # 状态信息
    is_verified = Column(Boolean, default=False, comment="是否已验证")
    is_helpful = Column(Integer, default=0, comment="有用票数")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
    
    # 关联关系
    template = relationship("Template", back_populates="reviews")

class TemplateUsage(Base):
    """模板使用记录模型"""
    __tablename__ = "template_usage"

    # 基础字段
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4, comment="记录ID")
    template_id = Column(UUID(as_uuid=True), ForeignKey("templates.id"), nullable=False, comment="模板ID")
    
    # 使用信息
    user_id = Column(UUID(as_uuid=True), nullable=False, comment="用户ID")
    project_name = Column(String(200), comment="项目名称")
    usage_type = Column(String(50), comment="使用类型")
    
    # 配置信息
    config_data = Column(JSON, comment="配置数据")
    generated_files = Column(JSON, comment="生成的文件列表")
    
    # 结果信息
    success = Column(Boolean, default=True, comment="是否成功")
    error_message = Column(Text, comment="错误信息")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    
    # 索引
    __table_args__ = (
        {"comment": "模板使用记录表"}
    )
