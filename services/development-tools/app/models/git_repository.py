# 自动驾驶开发加速系统 - Git仓库模型
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Any
from uuid import uuid4

from sqlalchemy import Column, String, Text, DateTime, Boolean, Integer, JSON, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from app.core.database import Base

class GitRepository(Base):
    """Git仓库模型"""
    __tablename__ = "git_repositories"

    # 基础字段
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4, comment="仓库ID")
    name = Column(String(200), nullable=False, comment="仓库名称")
    description = Column(Text, comment="仓库描述")
    
    # 路径信息
    path = Column(String(500), nullable=False, comment="本地路径")
    remote_url = Column(String(500), comment="远程仓库URL")
    
    # 仓库配置
    default_branch = Column(String(100), default="main", comment="默认分支")
    is_private = Column(Boolean, default=True, comment="是否私有")
    is_bare = Column(Boolean, default=False, comment="是否为裸仓库")
    
    # 所有者信息
    owner_id = Column(UUID(as_uuid=True), nullable=False, comment="所有者ID")
    
    # 配置数据
    config_data = Column(JSON, comment="仓库配置数据")
    
    # 统计信息
    commit_count = Column(Integer, default=0, comment="提交数量")
    branch_count = Column(Integer, default=0, comment="分支数量")
    tag_count = Column(Integer, default=0, comment="标签数量")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
    last_commit_at = Column(DateTime, comment="最后提交时间")
    
    # 关联关系
    branches = relationship("GitBranch", back_populates="repository", cascade="all, delete-orphan")
    commits = relationship("GitCommit", back_populates="repository", cascade="all, delete-orphan")
    remotes = relationship("GitRemote", back_populates="repository", cascade="all, delete-orphan")
    tags = relationship("GitTag", back_populates="repository", cascade="all, delete-orphan")

class GitBranch(Base):
    """Git分支模型"""
    __tablename__ = "git_branches"

    # 基础字段
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4, comment="分支ID")
    repository_id = Column(UUID(as_uuid=True), ForeignKey("git_repositories.id"), nullable=False, comment="仓库ID")
    
    # 分支信息
    name = Column(String(200), nullable=False, comment="分支名称")
    commit_hash = Column(String(40), comment="最新提交哈希")
    source_branch = Column(String(200), comment="源分支")
    
    # 分支属性
    is_default = Column(Boolean, default=False, comment="是否为默认分支")
    is_protected = Column(Boolean, default=False, comment="是否受保护")
    is_merged = Column(Boolean, default=False, comment="是否已合并")
    
    # 统计信息
    commit_count = Column(Integer, default=0, comment="提交数量")
    ahead_count = Column(Integer, default=0, comment="领先提交数")
    behind_count = Column(Integer, default=0, comment="落后提交数")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
    
    # 关联关系
    repository = relationship("GitRepository", back_populates="branches")

class GitCommit(Base):
    """Git提交模型"""
    __tablename__ = "git_commits"

    # 基础字段
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4, comment="提交ID")
    repository_id = Column(UUID(as_uuid=True), ForeignKey("git_repositories.id"), nullable=False, comment="仓库ID")
    
    # 提交信息
    hash = Column(String(40), nullable=False, unique=True, comment="提交哈希")
    message = Column(Text, nullable=False, comment="提交消息")
    author_name = Column(String(100), comment="作者姓名")
    author_email = Column(String(200), comment="作者邮箱")
    committer_name = Column(String(100), comment="提交者姓名")
    committer_email = Column(String(200), comment="提交者邮箱")
    
    # 父提交
    parent_hashes = Column(JSON, default=list, comment="父提交哈希列表")
    
    # 统计信息
    files_changed = Column(Integer, default=0, comment="更改文件数")
    insertions = Column(Integer, default=0, comment="插入行数")
    deletions = Column(Integer, default=0, comment="删除行数")
    
    # 时间戳
    committed_at = Column(DateTime, nullable=False, comment="提交时间")
    created_at = Column(DateTime, default=datetime.utcnow, comment="记录创建时间")
    
    # 关联关系
    repository = relationship("GitRepository", back_populates="commits")

class GitRemote(Base):
    """Git远程仓库模型"""
    __tablename__ = "git_remotes"

    # 基础字段
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4, comment="远程仓库ID")
    repository_id = Column(UUID(as_uuid=True), ForeignKey("git_repositories.id"), nullable=False, comment="仓库ID")
    
    # 远程仓库信息
    name = Column(String(100), nullable=False, comment="远程仓库名称")
    url = Column(String(500), nullable=False, comment="远程仓库URL")
    fetch_url = Column(String(500), comment="拉取URL")
    push_url = Column(String(500), comment="推送URL")
    
    # 属性
    is_default = Column(Boolean, default=False, comment="是否为默认远程仓库")
    
    # 认证信息
    auth_type = Column(String(50), comment="认证类型")
    username = Column(String(100), comment="用户名")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
    
    # 关联关系
    repository = relationship("GitRepository", back_populates="remotes")

class GitTag(Base):
    """Git标签模型"""
    __tablename__ = "git_tags"

    # 基础字段
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4, comment="标签ID")
    repository_id = Column(UUID(as_uuid=True), ForeignKey("git_repositories.id"), nullable=False, comment="仓库ID")
    
    # 标签信息
    name = Column(String(200), nullable=False, comment="标签名称")
    commit_hash = Column(String(40), nullable=False, comment="提交哈希")
    message = Column(Text, comment="标签消息")
    
    # 标签类型
    is_annotated = Column(Boolean, default=False, comment="是否为注释标签")
    
    # 创建者信息
    tagger_name = Column(String(100), comment="标签创建者姓名")
    tagger_email = Column(String(200), comment="标签创建者邮箱")
    
    # 时间戳
    tagged_at = Column(DateTime, comment="标签创建时间")
    created_at = Column(DateTime, default=datetime.utcnow, comment="记录创建时间")
    
    # 关联关系
    repository = relationship("GitRepository", back_populates="tags")

class GitMergeRequest(Base):
    """Git合并请求模型"""
    __tablename__ = "git_merge_requests"

    # 基础字段
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid4, comment="合并请求ID")
    repository_id = Column(UUID(as_uuid=True), ForeignKey("git_repositories.id"), nullable=False, comment="仓库ID")
    
    # 合并请求信息
    title = Column(String(500), nullable=False, comment="标题")
    description = Column(Text, comment="描述")
    source_branch = Column(String(200), nullable=False, comment="源分支")
    target_branch = Column(String(200), nullable=False, comment="目标分支")
    
    # 状态信息
    status = Column(String(50), default="open", comment="状态")
    is_draft = Column(Boolean, default=False, comment="是否为草稿")
    
    # 创建者信息
    author_id = Column(UUID(as_uuid=True), nullable=False, comment="创建者ID")
    assignee_id = Column(UUID(as_uuid=True), comment="指派者ID")
    
    # 审核信息
    reviewers = Column(JSON, default=list, comment="审核者列表")
    approved_by = Column(JSON, default=list, comment="批准者列表")
    
    # 合并信息
    merged_by = Column(UUID(as_uuid=True), comment="合并者ID")
    merged_at = Column(DateTime, comment="合并时间")
    merge_commit_hash = Column(String(40), comment="合并提交哈希")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
    closed_at = Column(DateTime, comment="关闭时间")
