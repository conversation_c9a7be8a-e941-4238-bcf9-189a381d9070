# 自动驾驶开发加速系统 - 模板管理API
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, Query
from fastapi.responses import StreamingResponse
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.auth import get_current_user
from app.core.exceptions import CustomException
from app.models.user import User
from app.schemas.template import (
    TemplateCreate, TemplateUpdate, TemplateResponse, TemplateListResponse,
    TemplateQuery, TemplateRenderRequest, TemplateRenderResponse,
    TemplateValidationResponse, TemplateFileResponse
)
from app.schemas.common import PaginatedResponse
from app.services.template_service import TemplateService
from app.utils.response import success_response, error_response

router = APIRouter(prefix="/templates", tags=["模板管理"])

@router.post("/", response_model=TemplateResponse, summary="创建模板")
async def create_template(
    template_data: TemplateCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    创建新的代码模板
    
    - **name**: 模板名称（唯一）
    - **display_name**: 显示名称
    - **description**: 模板描述
    - **type**: 模板类型
    - **category**: 模板分类
    - **language**: 编程语言
    - **framework**: 框架名称
    """
    try:
        service = TemplateService(db)
        template = await service.create_template(template_data, current_user.id)
        return success_response(data=template, message="模板创建成功")
    except CustomException as e:
        raise HTTPException(status_code=e.status_code, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/", response_model=PaginatedResponse[TemplateListResponse], summary="获取模板列表")
async def list_templates(
    type: Optional[str] = Query(None, description="模板类型"),
    category: Optional[str] = Query(None, description="模板分类"),
    language: Optional[str] = Query(None, description="编程语言"),
    framework: Optional[str] = Query(None, description="框架名称"),
    status: Optional[str] = Query(None, description="模板状态"),
    is_public: Optional[bool] = Query(None, description="是否公开"),
    is_featured: Optional[bool] = Query(None, description="是否推荐"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    sort_by: Optional[str] = Query("created_at", description="排序字段"),
    sort_order: Optional[str] = Query("desc", description="排序方向"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页大小"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取模板列表，支持分页、搜索和过滤
    """
    try:
        query_params = TemplateQuery(
            type=type,
            category=category,
            language=language,
            framework=framework,
            status=status,
            is_public=is_public,
            is_featured=is_featured,
            search=search,
            sort_by=sort_by,
            sort_order=sort_order,
            page=page,
            page_size=page_size
        )
        
        service = TemplateService(db)
        templates, total = await service.list_templates(query_params)
        
        return success_response(
            data={
                "items": templates,
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": (total + page_size - 1) // page_size
            },
            message="获取模板列表成功"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{template_id}", response_model=TemplateResponse, summary="获取模板详情")
async def get_template(
    template_id: UUID,
    include_files: bool = Query(False, description="是否包含文件列表"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取指定模板的详细信息
    """
    try:
        service = TemplateService(db)
        template = await service.get_template(template_id, include_files=include_files)
        
        if not template:
            raise HTTPException(status_code=404, detail="模板不存在")
        
        return success_response(data=template, message="获取模板详情成功")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.put("/{template_id}", response_model=TemplateResponse, summary="更新模板")
async def update_template(
    template_id: UUID,
    template_data: TemplateUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    更新模板信息
    """
    try:
        service = TemplateService(db)
        template = await service.update_template(template_id, template_data)
        return success_response(data=template, message="模板更新成功")
    except CustomException as e:
        raise HTTPException(status_code=e.status_code, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/{template_id}", summary="删除模板")
async def delete_template(
    template_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    删除指定模板
    """
    try:
        service = TemplateService(db)
        success = await service.delete_template(template_id)
        
        if not success:
            raise HTTPException(status_code=404, detail="模板不存在")
        
        return success_response(message="模板删除成功")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{template_id}/files", response_model=List[TemplateFileResponse], summary="上传模板文件")
async def upload_template_files(
    template_id: UUID,
    files: List[UploadFile] = File(...),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    上传模板文件
    """
    try:
        service = TemplateService(db)
        
        # 处理上传的文件
        file_data_list = []
        for file in files:
            content = await file.read()
            
            # 判断是否为二进制文件
            try:
                content_str = content.decode('utf-8')
                is_binary = False
            except UnicodeDecodeError:
                content_str = None
                is_binary = True
            
            file_data_list.append({
                "filename": file.filename,
                "content": content_str if not is_binary else content,
                "relative_path": file.filename,
                "is_template": not is_binary,
                "encoding": "utf-8" if not is_binary else None
            })
        
        template_files = await service.upload_template_files(template_id, file_data_list)
        return success_response(data=template_files, message="文件上传成功")
    except CustomException as e:
        raise HTTPException(status_code=e.status_code, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{template_id}/render", response_model=TemplateRenderResponse, summary="渲染模板")
async def render_template(
    template_id: UUID,
    render_request: TemplateRenderRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    使用指定配置渲染模板
    """
    try:
        service = TemplateService(db)
        result = await service.render_template(template_id, render_request.config)
        
        # 记录使用情况
        await service.record_template_usage(
            template_id=template_id,
            user_id=current_user.id,
            usage_data={
                "project_name": render_request.project_name,
                "usage_type": "render",
                "config_data": render_request.config,
                "generated_files": list(result["files"].keys()),
                "success": True
            }
        )
        
        return success_response(data=result, message="模板渲染成功")
    except CustomException as e:
        raise HTTPException(status_code=e.status_code, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{template_id}/preview", response_model=TemplateRenderResponse, summary="预览模板")
async def preview_template(
    template_id: UUID,
    render_request: TemplateRenderRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    预览模板渲染结果（不记录使用情况）
    """
    try:
        service = TemplateService(db)
        result = await service.preview_template(template_id, render_request.config)
        return success_response(data=result, message="模板预览成功")
    except CustomException as e:
        raise HTTPException(status_code=e.status_code, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{template_id}/validate", response_model=TemplateValidationResponse, summary="验证模板")
async def validate_template(
    template_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    验证模板语法和配置
    """
    try:
        service = TemplateService(db)
        result = await service.validate_template(template_id)
        return success_response(data=result, message="模板验证完成")
    except CustomException as e:
        raise HTTPException(status_code=e.status_code, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{template_id}/publish", response_model=TemplateResponse, summary="发布模板")
async def publish_template(
    template_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    发布模板（将状态改为已发布）
    """
    try:
        service = TemplateService(db)
        template = await service.publish_template(template_id)
        return success_response(data=template, message="模板发布成功")
    except CustomException as e:
        raise HTTPException(status_code=e.status_code, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{template_id}/download", summary="下载模板")
async def download_template(
    template_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    下载模板文件包
    """
    try:
        service = TemplateService(db)
        template = await service.get_template(template_id, include_files=True)
        
        if not template:
            raise HTTPException(status_code=404, detail="模板不存在")
        
        # 创建ZIP文件
        import io
        import zipfile
        
        zip_buffer = io.BytesIO()
        with zipfile.ZipFile(zip_buffer, 'w', zipfile.ZIP_DEFLATED) as zip_file:
            for template_file in template.files:
                if template_file.is_binary:
                    # 读取二进制文件
                    with open(template_file.filepath, 'rb') as f:
                        zip_file.writestr(template_file.relative_path, f.read())
                else:
                    # 添加文本文件
                    zip_file.writestr(template_file.relative_path, template_file.content)
        
        zip_buffer.seek(0)
        
        # 更新下载统计
        await service.db.execute(
            update(Template)
            .where(Template.id == template_id)
            .values(download_count=Template.download_count + 1)
        )
        await service.db.commit()
        
        return StreamingResponse(
            io.BytesIO(zip_buffer.read()),
            media_type="application/zip",
            headers={"Content-Disposition": f"attachment; filename={template.name}.zip"}
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
