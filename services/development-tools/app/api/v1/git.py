# 自动驾驶开发加速系统 - Git版本控制API
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.auth import get_current_user
from app.core.exceptions import CustomException
from app.models.user import User
from app.schemas.git import (
    GitRepositoryCreate, GitRepositoryResponse, GitBranchCreate, GitBranchResponse,
    GitCommitCreate, GitCommitResponse, GitRemoteCreate, GitRemoteResponse,
    GitCloneRequest, GitPushRequest, GitPullRequest
)
from app.schemas.common import PaginatedResponse
from app.services.git_service import GitService
from app.utils.response import success_response

router = APIRouter(prefix="/git", tags=["Git版本控制"])

@router.post("/repositories", response_model=GitRepositoryResponse, summary="初始化Git仓库")
async def init_repository(
    request: GitRepositoryCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    在指定项目路径初始化Git仓库
    
    - **project_path**: 项目路径
    - **repository_name**: 仓库名称
    - **description**: 仓库描述
    - **default_branch**: 默认分支名称
    - **user_name**: Git用户名
    - **user_email**: Git用户邮箱
    """
    try:
        service = GitService(db)
        repository = await service.init_repository(
            request.project_path,
            current_user.id,
            request.dict()
        )
        return success_response(data=repository, message="Git仓库初始化成功")
    except CustomException as e:
        raise HTTPException(status_code=e.status_code, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/repositories/clone", response_model=GitRepositoryResponse, summary="克隆远程仓库")
async def clone_repository(
    request: GitCloneRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    克隆远程Git仓库到本地
    
    - **remote_url**: 远程仓库URL
    - **local_path**: 本地路径
    - **branch**: 指定分支
    - **depth**: 克隆深度
    - **single_branch**: 是否只克隆单个分支
    """
    try:
        service = GitService(db)
        repository = await service.clone_repository(
            request.remote_url,
            request.local_path,
            current_user.id,
            request.dict()
        )
        return success_response(data=repository, message="仓库克隆成功")
    except CustomException as e:
        raise HTTPException(status_code=e.status_code, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/repositories", response_model=PaginatedResponse[GitRepositoryResponse], summary="获取仓库列表")
async def list_repositories(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页大小"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取当前用户的Git仓库列表
    """
    try:
        from sqlalchemy import select, func
        from app.models.git_repository import GitRepository
        
        # 获取总数
        count_query = select(func.count()).select_from(GitRepository).where(GitRepository.owner_id == current_user.id)
        total_result = await db.execute(count_query)
        total = total_result.scalar()
        
        # 获取仓库列表
        offset = (page - 1) * page_size
        query = (
            select(GitRepository)
            .where(GitRepository.owner_id == current_user.id)
            .order_by(GitRepository.created_at.desc())
            .offset(offset)
            .limit(page_size)
        )
        
        result = await db.execute(query)
        repositories = result.scalars().all()
        
        return success_response(
            data={
                "items": list(repositories),
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": (total + page_size - 1) // page_size
            },
            message="获取仓库列表成功"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/repositories/{repository_id}", response_model=GitRepositoryResponse, summary="获取仓库详情")
async def get_repository(
    repository_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取指定仓库的详细信息
    """
    try:
        from app.models.git_repository import GitRepository
        
        repository = await db.get(GitRepository, repository_id)
        if not repository:
            raise HTTPException(status_code=404, detail="仓库不存在")
        
        # 检查权限
        if repository.owner_id != current_user.id:
            raise HTTPException(status_code=403, detail="无权访问此仓库")
        
        return success_response(data=repository, message="获取仓库详情成功")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/repositories/{repository_id}/branches", response_model=GitBranchResponse, summary="创建分支")
async def create_branch(
    repository_id: UUID,
    request: GitBranchCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    在指定仓库中创建新分支
    
    - **branch_name**: 分支名称
    - **source_branch**: 源分支名称
    """
    try:
        service = GitService(db)
        branch = await service.create_branch(
            repository_id,
            request.branch_name,
            request.source_branch
        )
        return success_response(data=branch, message="分支创建成功")
    except CustomException as e:
        raise HTTPException(status_code=e.status_code, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/repositories/{repository_id}/branches", response_model=List[GitBranchResponse], summary="获取分支列表")
async def list_branches(
    repository_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取仓库的分支列表
    """
    try:
        from sqlalchemy import select
        from app.models.git_repository import GitBranch
        
        query = select(GitBranch).where(GitBranch.repository_id == repository_id)
        result = await db.execute(query)
        branches = result.scalars().all()
        
        return success_response(data=list(branches), message="获取分支列表成功")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/repositories/{repository_id}/commits", response_model=GitCommitResponse, summary="提交更改")
async def commit_changes(
    repository_id: UUID,
    request: GitCommitCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    提交仓库中的更改
    
    - **message**: 提交消息
    - **files**: 要提交的文件列表（可选，默认提交所有更改）
    - **author_name**: 作者姓名（可选）
    - **author_email**: 作者邮箱（可选）
    """
    try:
        service = GitService(db)
        commit = await service.commit_changes(
            repository_id,
            request.message,
            request.files,
            request.author_name,
            request.author_email
        )
        return success_response(data=commit, message="提交成功")
    except CustomException as e:
        raise HTTPException(status_code=e.status_code, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/repositories/{repository_id}/commits", response_model=List[GitCommitResponse], summary="获取提交历史")
async def list_commits(
    repository_id: UUID,
    limit: int = Query(50, ge=1, le=200, description="返回数量限制"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取仓库的提交历史
    """
    try:
        from sqlalchemy import select
        from app.models.git_repository import GitCommit
        
        query = (
            select(GitCommit)
            .where(GitCommit.repository_id == repository_id)
            .order_by(GitCommit.committed_at.desc())
            .limit(limit)
        )
        
        result = await db.execute(query)
        commits = result.scalars().all()
        
        return success_response(data=list(commits), message="获取提交历史成功")
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/repositories/{repository_id}/push", summary="推送更改")
async def push_changes(
    repository_id: UUID,
    request: GitPushRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    推送本地更改到远程仓库
    
    - **remote_name**: 远程仓库名称（默认为origin）
    - **branch_name**: 分支名称（可选，默认为当前分支）
    """
    try:
        service = GitService(db)
        success = await service.push_changes(
            repository_id,
            request.remote_name,
            request.branch_name
        )
        
        if success:
            return success_response(message="推送成功")
        else:
            raise HTTPException(status_code=500, detail="推送失败")
    except CustomException as e:
        raise HTTPException(status_code=e.status_code, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/repositories/{repository_id}/pull", summary="拉取更改")
async def pull_changes(
    repository_id: UUID,
    request: GitPullRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    从远程仓库拉取更改
    
    - **remote_name**: 远程仓库名称（默认为origin）
    - **branch_name**: 分支名称（可选，默认为当前分支）
    """
    try:
        service = GitService(db)
        success = await service.pull_changes(
            repository_id,
            request.remote_name,
            request.branch_name
        )
        
        if success:
            return success_response(message="拉取成功")
        else:
            raise HTTPException(status_code=500, detail="拉取失败")
    except CustomException as e:
        raise HTTPException(status_code=e.status_code, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/repositories/{repository_id}", summary="删除仓库")
async def delete_repository(
    repository_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    删除指定的Git仓库
    """
    try:
        from app.models.git_repository import GitRepository
        import shutil
        from pathlib import Path
        
        repository = await db.get(GitRepository, repository_id)
        if not repository:
            raise HTTPException(status_code=404, detail="仓库不存在")
        
        # 检查权限
        if repository.owner_id != current_user.id:
            raise HTTPException(status_code=403, detail="无权删除此仓库")
        
        # 删除本地仓库文件
        repo_path = Path(repository.path)
        if repo_path.exists():
            shutil.rmtree(repo_path)
        
        # 删除数据库记录
        await db.delete(repository)
        await db.commit()
        
        return success_response(message="仓库删除成功")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
