# 自动驾驶开发加速系统 - 项目生成API
from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from fastapi.responses import FileResponse
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.auth import get_current_user
from app.core.exceptions import CustomException
from app.models.user import User
from app.schemas.project import (
    ProjectGenerateRequest, ProjectResponse, ProjectListResponse,
    ProjectQuery, ProjectFileResponse
)
from app.schemas.common import PaginatedResponse
from app.services.project_generator import ProjectGenerator
from app.utils.response import success_response

router = APIRouter(prefix="/projects", tags=["项目生成"])

@router.post("/generate", response_model=ProjectResponse, summary="生成项目")
async def generate_project(
    request: ProjectGenerateRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    基于模板生成新项目
    
    - **template_id**: 模板ID
    - **project_name**: 项目名称
    - **description**: 项目描述
    - **config**: 项目配置参数
    - **options**: 生成选项
    """
    try:
        generator = ProjectGenerator(db)
        
        # 异步生成项目
        if request.async_generation:
            # 后台任务生成
            background_tasks.add_task(
                generator.generate_project,
                request.template_id,
                request.dict(),
                current_user.id
            )
            
            return success_response(
                data={"status": "generating", "message": "项目正在后台生成中"},
                message="项目生成任务已启动"
            )
        else:
            # 同步生成
            result = await generator.generate_project(
                request.template_id,
                request.dict(),
                current_user.id
            )
            
            return success_response(data=result, message="项目生成成功")
            
    except CustomException as e:
        raise HTTPException(status_code=e.status_code, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/", response_model=PaginatedResponse[ProjectListResponse], summary="获取项目列表")
async def list_projects(
    status: Optional[str] = Query(None, description="项目状态"),
    template_id: Optional[UUID] = Query(None, description="模板ID"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页大小"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取当前用户的项目列表
    """
    try:
        generator = ProjectGenerator(db)
        projects, total = await generator.list_user_projects(
            current_user.id, page, page_size
        )
        
        return success_response(
            data={
                "items": projects,
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": (total + page_size - 1) // page_size
            },
            message="获取项目列表成功"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{project_id}", response_model=ProjectResponse, summary="获取项目详情")
async def get_project(
    project_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取指定项目的详细信息
    """
    try:
        generator = ProjectGenerator(db)
        project = await generator.get_project_info(project_id)
        
        if not project:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        # 检查权限
        if project.owner_id != current_user.id:
            raise HTTPException(status_code=403, detail="无权访问此项目")
        
        return success_response(data=project, message="获取项目详情成功")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{project_id}/files", response_model=List[ProjectFileResponse], summary="获取项目文件列表")
async def list_project_files(
    project_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取项目的文件列表
    """
    try:
        generator = ProjectGenerator(db)
        project = await generator.get_project_info(project_id)
        
        if not project:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        # 检查权限
        if project.owner_id != current_user.id:
            raise HTTPException(status_code=403, detail="无权访问此项目")
        
        return success_response(data=project.files, message="获取文件列表成功")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/{project_id}/download", summary="下载项目")
async def download_project(
    project_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    下载项目文件包
    """
    try:
        generator = ProjectGenerator(db)
        project = await generator.get_project_info(project_id)
        
        if not project:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        # 检查权限
        if project.owner_id != current_user.id:
            raise HTTPException(status_code=403, detail="无权访问此项目")
        
        # 打包项目
        zip_path = await generator.package_project(project_id)
        
        return FileResponse(
            path=str(zip_path),
            filename=f"{project.name}.zip",
            media_type="application/zip"
        )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/{project_id}", summary="删除项目")
async def delete_project(
    project_id: UUID,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    删除指定项目
    """
    try:
        generator = ProjectGenerator(db)
        project = await generator.get_project_info(project_id)
        
        if not project:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        # 检查权限
        if project.owner_id != current_user.id:
            raise HTTPException(status_code=403, detail="无权删除此项目")
        
        # 删除项目文件
        import shutil
        from pathlib import Path
        
        project_dir = Path(project.project_path)
        if project_dir.exists():
            shutil.rmtree(project_dir)
        
        # 删除数据库记录
        await db.delete(project)
        await db.commit()
        
        return success_response(message="项目删除成功")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/{project_id}/regenerate", response_model=ProjectResponse, summary="重新生成项目")
async def regenerate_project(
    project_id: UUID,
    config_updates: Optional[dict] = None,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    基于新配置重新生成项目
    """
    try:
        generator = ProjectGenerator(db)
        project = await generator.get_project_info(project_id)
        
        if not project:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        # 检查权限
        if project.owner_id != current_user.id:
            raise HTTPException(status_code=403, detail="无权操作此项目")
        
        # 合并配置
        new_config = {**project.config_data}
        if config_updates:
            new_config.update(config_updates)
        
        # 重新生成项目
        result = await generator.generate_project(
            project.template_id,
            new_config,
            current_user.id
        )
        
        return success_response(data=result, message="项目重新生成成功")
    except HTTPException:
        raise
    except CustomException as e:
        raise HTTPException(status_code=e.status_code, detail=e.message)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
