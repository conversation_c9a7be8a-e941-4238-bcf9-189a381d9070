# 自动驾驶开发加速系统 - 模板服务单元测试
import pytest
import tempfile
import shutil
import os
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path

from app.services.template_service import TemplateService
from app.models.template import Template, TemplateParameter
from app.core.exceptions import TemplateNotFoundError, TemplateValidationError


class TestTemplateService:
    """模板服务测试类"""

    @pytest.fixture
    def temp_dir(self):
        """创建临时目录"""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)

    @pytest.fixture
    def mock_db_session(self):
        """模拟数据库会话"""
        session = Mock()
        return session

    @pytest.fixture
    def template_service(self, temp_dir, mock_db_session):
        """创建模板服务实例"""
        with patch('app.services.template_service.get_db_session', return_value=mock_db_session):
            service = TemplateService(template_base_path=temp_dir)
            return service

    @pytest.fixture
    def sample_template(self):
        """示例模板数据"""
        return Template(
            id="test-template-1",
            name="Python FastAPI模板",
            description="用于创建FastAPI后端服务的模板",
            category="backend",
            language="python",
            framework="fastapi",
            version="1.0.0",
            template_path="templates/python-fastapi",
            parameters=[
                TemplateParameter(
                    name="project_name",
                    type="string",
                    description="项目名称",
                    required=True,
                    default_value="my-api"
                ),
                TemplateParameter(
                    name="enable_auth",
                    type="boolean",
                    description="启用身份认证",
                    required=False,
                    default_value=True
                ),
                TemplateParameter(
                    name="database_type",
                    type="select",
                    description="数据库类型",
                    required=True,
                    options=["postgresql", "mysql", "sqlite"],
                    default_value="postgresql"
                )
            ],
            created_by="test-user",
            created_at="2024-01-15T10:00:00Z",
            updated_at="2024-01-15T10:00:00Z"
        )

    def test_create_template_success(self, template_service, mock_db_session, sample_template, temp_dir):
        """测试成功创建模板"""
        # 准备模板文件
        template_dir = Path(temp_dir) / "templates" / "python-fastapi"
        template_dir.mkdir(parents=True)
        
        # 创建模板文件
        (template_dir / "main.py.j2").write_text("""
from fastapi import FastAPI

app = FastAPI(title="{{ project_name }}")

@app.get("/")
def read_root():
    return {"message": "Hello from {{ project_name }}"}

{% if enable_auth %}
@app.get("/protected")
def protected_route():
    return {"message": "This is a protected route"}
{% endif %}
""")
        
        (template_dir / "requirements.txt.j2").write_text("""
fastapi==0.104.1
uvicorn==0.24.0
{% if database_type == "postgresql" %}
psycopg2-binary==2.9.7
{% elif database_type == "mysql" %}
PyMySQL==1.1.0
{% endif %}
""")

        # 模拟数据库操作
        mock_db_session.add = Mock()
        mock_db_session.commit = Mock()
        mock_db_session.refresh = Mock()

        # 执行创建操作
        result = template_service.create_template(sample_template)

        # 验证结果
        assert result.name == sample_template.name
        assert result.description == sample_template.description
        mock_db_session.add.assert_called_once()
        mock_db_session.commit.assert_called_once()

    def test_create_template_validation_error(self, template_service, sample_template):
        """测试模板验证错误"""
        # 创建无效模板（缺少必需字段）
        invalid_template = Template(
            name="",  # 空名称
            description=sample_template.description,
            category=sample_template.category,
            language=sample_template.language,
            framework=sample_template.framework,
            version=sample_template.version,
            template_path=sample_template.template_path,
            parameters=sample_template.parameters,
            created_by=sample_template.created_by
        )

        # 验证抛出验证错误
        with pytest.raises(TemplateValidationError) as exc_info:
            template_service.create_template(invalid_template)
        
        assert "模板名称不能为空" in str(exc_info.value)

    def test_get_template_success(self, template_service, mock_db_session, sample_template):
        """测试成功获取模板"""
        # 模拟数据库查询
        mock_db_session.query.return_value.filter.return_value.first.return_value = sample_template

        # 执行查询
        result = template_service.get_template("test-template-1")

        # 验证结果
        assert result.id == sample_template.id
        assert result.name == sample_template.name

    def test_get_template_not_found(self, template_service, mock_db_session):
        """测试模板不存在"""
        # 模拟数据库查询返回None
        mock_db_session.query.return_value.filter.return_value.first.return_value = None

        # 验证抛出未找到错误
        with pytest.raises(TemplateNotFoundError):
            template_service.get_template("non-existent-template")

    def test_list_templates(self, template_service, mock_db_session, sample_template):
        """测试获取模板列表"""
        # 创建多个模板
        templates = [
            sample_template,
            Template(
                id="test-template-2",
                name="React TypeScript模板",
                description="用于创建React前端应用的模板",
                category="frontend",
                language="typescript",
                framework="react",
                version="1.0.0",
                template_path="templates/react-typescript",
                parameters=[],
                created_by="test-user"
            )
        ]

        # 模拟数据库查询
        mock_query = Mock()
        mock_query.offset.return_value.limit.return_value.all.return_value = templates
        mock_query.count.return_value = len(templates)
        mock_db_session.query.return_value = mock_query

        # 执行查询
        result, total = template_service.list_templates(page=1, page_size=10)

        # 验证结果
        assert len(result) == 2
        assert total == 2
        assert result[0].name == "Python FastAPI模板"
        assert result[1].name == "React TypeScript模板"

    def test_list_templates_with_filters(self, template_service, mock_db_session, sample_template):
        """测试带过滤条件的模板列表"""
        # 模拟数据库查询
        mock_query = Mock()
        mock_query.filter.return_value = mock_query
        mock_query.offset.return_value.limit.return_value.all.return_value = [sample_template]
        mock_query.count.return_value = 1
        mock_db_session.query.return_value = mock_query

        # 执行查询
        result, total = template_service.list_templates(
            category="backend",
            language="python",
            page=1,
            page_size=10
        )

        # 验证结果
        assert len(result) == 1
        assert total == 1
        assert result[0].category == "backend"

    def test_update_template_success(self, template_service, mock_db_session, sample_template):
        """测试成功更新模板"""
        # 模拟数据库查询
        mock_db_session.query.return_value.filter.return_value.first.return_value = sample_template
        mock_db_session.commit = Mock()

        # 更新数据
        updates = {
            "description": "更新后的描述",
            "version": "1.1.0"
        }

        # 执行更新
        result = template_service.update_template("test-template-1", updates)

        # 验证结果
        assert result.description == "更新后的描述"
        assert result.version == "1.1.0"
        mock_db_session.commit.assert_called_once()

    def test_delete_template_success(self, template_service, mock_db_session, sample_template, temp_dir):
        """测试成功删除模板"""
        # 创建模板文件
        template_dir = Path(temp_dir) / "templates" / "python-fastapi"
        template_dir.mkdir(parents=True)
        (template_dir / "main.py.j2").write_text("test content")

        # 模拟数据库查询
        mock_db_session.query.return_value.filter.return_value.first.return_value = sample_template
        mock_db_session.delete = Mock()
        mock_db_session.commit = Mock()

        # 执行删除
        template_service.delete_template("test-template-1")

        # 验证数据库操作
        mock_db_session.delete.assert_called_once_with(sample_template)
        mock_db_session.commit.assert_called_once()

        # 验证文件被删除
        assert not template_dir.exists()

    def test_validate_template_parameters_success(self, template_service, sample_template):
        """测试参数验证成功"""
        parameters = {
            "project_name": "my-awesome-api",
            "enable_auth": True,
            "database_type": "postgresql"
        }

        # 执行验证
        result = template_service.validate_template_parameters(sample_template, parameters)

        # 验证结果
        assert result is True

    def test_validate_template_parameters_missing_required(self, template_service, sample_template):
        """测试缺少必需参数"""
        parameters = {
            "enable_auth": True
            # 缺少必需的 project_name 和 database_type
        }

        # 验证抛出验证错误
        with pytest.raises(TemplateValidationError) as exc_info:
            template_service.validate_template_parameters(sample_template, parameters)
        
        assert "project_name" in str(exc_info.value)
        assert "database_type" in str(exc_info.value)

    def test_validate_template_parameters_invalid_select_option(self, template_service, sample_template):
        """测试无效的选择选项"""
        parameters = {
            "project_name": "my-api",
            "enable_auth": True,
            "database_type": "invalid_db"  # 无效选项
        }

        # 验证抛出验证错误
        with pytest.raises(TemplateValidationError) as exc_info:
            template_service.validate_template_parameters(sample_template, parameters)
        
        assert "database_type" in str(exc_info.value)
        assert "invalid_db" in str(exc_info.value)

    def test_render_template_success(self, template_service, sample_template, temp_dir):
        """测试模板渲染成功"""
        # 创建模板文件
        template_dir = Path(temp_dir) / "templates" / "python-fastapi"
        template_dir.mkdir(parents=True)
        
        (template_dir / "main.py.j2").write_text("""
from fastapi import FastAPI

app = FastAPI(title="{{ project_name }}")

@app.get("/")
def read_root():
    return {"message": "Hello from {{ project_name }}"}

{% if enable_auth %}
@app.get("/protected")
def protected_route():
    return {"message": "This is a protected route"}
{% endif %}
""")

        # 参数
        parameters = {
            "project_name": "test-api",
            "enable_auth": True,
            "database_type": "postgresql"
        }

        # 执行渲染
        result = template_service.render_template(sample_template, parameters)

        # 验证结果
        assert "main.py" in result
        assert "test-api" in result["main.py"]
        assert "protected_route" in result["main.py"]

    def test_render_template_without_auth(self, template_service, sample_template, temp_dir):
        """测试条件渲染"""
        # 创建模板文件
        template_dir = Path(temp_dir) / "templates" / "python-fastapi"
        template_dir.mkdir(parents=True)
        
        (template_dir / "main.py.j2").write_text("""
from fastapi import FastAPI

app = FastAPI(title="{{ project_name }}")

@app.get("/")
def read_root():
    return {"message": "Hello from {{ project_name }}"}

{% if enable_auth %}
@app.get("/protected")
def protected_route():
    return {"message": "This is a protected route"}
{% endif %}
""")

        # 参数（禁用认证）
        parameters = {
            "project_name": "test-api",
            "enable_auth": False,
            "database_type": "postgresql"
        }

        # 执行渲染
        result = template_service.render_template(sample_template, parameters)

        # 验证结果
        assert "main.py" in result
        assert "test-api" in result["main.py"]
        assert "protected_route" not in result["main.py"]

    def test_get_template_preview(self, template_service, sample_template, temp_dir):
        """测试模板预览"""
        # 创建模板文件
        template_dir = Path(temp_dir) / "templates" / "python-fastapi"
        template_dir.mkdir(parents=True)
        
        (template_dir / "main.py.j2").write_text("from fastapi import FastAPI\n\napp = FastAPI(title=\"{{ project_name }}\")")
        (template_dir / "README.md.j2").write_text("# {{ project_name }}\n\n{{ description }}")

        # 模拟数据库查询
        mock_db_session = Mock()
        mock_db_session.query.return_value.filter.return_value.first.return_value = sample_template
        
        with patch('app.services.template_service.get_db_session', return_value=mock_db_session):
            service = TemplateService(template_base_path=temp_dir)
            
            # 执行预览
            result = service.get_template_preview("test-template-1", {
                "project_name": "preview-api",
                "description": "This is a preview"
            })

        # 验证结果
        assert "main.py" in result
        assert "README.md" in result
        assert "preview-api" in result["main.py"]
        assert "This is a preview" in result["README.md"]

    @pytest.mark.asyncio
    async def test_template_service_performance(self, template_service, temp_dir):
        """测试模板服务性能"""
        import time
        
        # 创建大量模板文件
        for i in range(10):
            template_dir = Path(temp_dir) / "templates" / f"template-{i}"
            template_dir.mkdir(parents=True)
            (template_dir / "main.py.j2").write_text(f"# Template {i}\nprint('Hello from template {i}')")

        # 测试批量操作性能
        start_time = time.time()
        
        # 模拟批量渲染
        for i in range(10):
            template = Template(
                id=f"template-{i}",
                name=f"Template {i}",
                description=f"Test template {i}",
                category="test",
                language="python",
                framework="test",
                version="1.0.0",
                template_path=f"templates/template-{i}",
                parameters=[],
                created_by="test-user"
            )
            
            result = template_service.render_template(template, {})
            assert f"main.py" in result

        end_time = time.time()
        execution_time = end_time - start_time

        # 验证性能（应该在合理时间内完成）
        assert execution_time < 1.0  # 应该在1秒内完成

    def test_template_service_error_handling(self, template_service, mock_db_session):
        """测试错误处理"""
        # 模拟数据库错误
        mock_db_session.query.side_effect = Exception("Database connection error")

        # 验证错误处理
        with pytest.raises(Exception) as exc_info:
            template_service.get_template("test-template-1")
        
        assert "Database connection error" in str(exc_info.value)

    def test_template_caching(self, template_service, mock_db_session, sample_template):
        """测试模板缓存"""
        # 模拟数据库查询
        mock_db_session.query.return_value.filter.return_value.first.return_value = sample_template

        # 第一次查询
        result1 = template_service.get_template("test-template-1")
        
        # 第二次查询（应该使用缓存）
        result2 = template_service.get_template("test-template-1")

        # 验证结果相同
        assert result1.id == result2.id
        assert result1.name == result2.name

        # 验证数据库只被查询一次（如果实现了缓存）
        # 这里需要根据实际的缓存实现来验证
