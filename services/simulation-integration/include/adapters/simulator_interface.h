// 自动驾驶开发加速系统 - 仿真器抽象接口
#pragma once

#include <string>
#include <vector>
#include <memory>
#include <functional>
#include <unordered_map>

#include "common/types.h"
#include "common/status.h"

namespace simulation_integration {

// 仿真器类型枚举
enum class SimulatorType {
    CARLA,
    AIRSIM,
    UNKNOWN
};

// 仿真器状态枚举
enum class SimulatorStatus {
    STOPPED,
    STARTING,
    RUNNING,
    PAUSED,
    STOPPING,
    ERROR
};

// 传感器类型枚举
enum class SensorType {
    CAMERA_RGB,
    CAMERA_DEPTH,
    CAMERA_SEMANTIC,
    LIDAR,
    RADAR,
    IMU,
    GPS,
    COLLISION
};

// 车辆控制命令
struct VehicleControl {
    float throttle = 0.0f;      // 油门 [0.0, 1.0]
    float steer = 0.0f;         // 转向 [-1.0, 1.0]
    float brake = 0.0f;         // 刹车 [0.0, 1.0]
    bool hand_brake = false;    // 手刹
    bool reverse = false;       // 倒车
    int gear = 0;              // 档位
    bool manual_gear_shift = false; // 手动换挡
};

// 传感器数据基类
struct SensorData {
    uint64_t timestamp;         // 时间戳
    uint32_t frame_id;          // 帧ID
    SensorType type;            // 传感器类型
    std::string sensor_id;      // 传感器ID
    
    virtual ~SensorData() = default;
};

// 图像数据
struct ImageData : public SensorData {
    uint32_t width;             // 图像宽度
    uint32_t height;            // 图像高度
    uint32_t channels;          // 通道数
    std::vector<uint8_t> data;  // 图像数据
    std::string format;         // 图像格式 (RGB, BGR, etc.)
};

// 点云数据
struct PointCloudData : public SensorData {
    struct Point {
        float x, y, z;          // 坐标
        float intensity;        // 强度
    };
    std::vector<Point> points;  // 点云数据
};

// IMU数据
struct IMUData : public SensorData {
    Vector3D accelerometer;     // 加速度计
    Vector3D gyroscope;         // 陀螺仪
    float compass;              // 指南针
};

// GPS数据
struct GPSData : public SensorData {
    double latitude;            // 纬度
    double longitude;           // 经度
    double altitude;            // 海拔
};

// 碰撞数据
struct CollisionData : public SensorData {
    uint32_t other_actor_id;    // 碰撞对象ID
    Vector3D normal_impulse;    // 法向冲量
    std::string other_actor_type; // 碰撞对象类型
};

// 车辆状态
struct VehicleState {
    uint32_t vehicle_id;        // 车辆ID
    Transform transform;        // 位置和姿态
    Vector3D velocity;          // 速度
    Vector3D angular_velocity;  // 角速度
    VehicleControl control;     // 控制命令
    float speed_limit;          // 速度限制
    bool is_at_traffic_light;   // 是否在红绿灯处
    std::string traffic_light_state; // 红绿灯状态
};

// 场景配置
struct ScenarioConfig {
    std::string name;           // 场景名称
    std::string description;    // 场景描述
    std::string map_name;       // 地图名称
    std::vector<Transform> spawn_points; // 生成点
    WeatherParameters weather;  // 天气参数
    std::unordered_map<std::string, std::string> parameters; // 其他参数
};

// 传感器配置
struct SensorConfig {
    std::string id;             // 传感器ID
    SensorType type;            // 传感器类型
    Transform relative_transform; // 相对位置
    std::unordered_map<std::string, std::string> parameters; // 传感器参数
};

// 仿真器抽象接口
class ISimulator {
public:
    virtual ~ISimulator() = default;
    
    // 基础生命周期管理
    virtual Status initialize(const std::string& config_file) = 0;
    virtual Status start() = 0;
    virtual Status stop() = 0;
    virtual Status pause() = 0;
    virtual Status resume() = 0;
    virtual Status reset() = 0;
    virtual void shutdown() = 0;
    
    // 状态查询
    virtual SimulatorType get_type() const = 0;
    virtual SimulatorStatus get_status() const = 0;
    virtual std::string get_version() const = 0;
    virtual bool is_connected() const = 0;
    virtual float get_simulation_time() const = 0;
    virtual uint64_t get_frame_count() const = 0;
    
    // 场景管理
    virtual Status load_scenario(const ScenarioConfig& config) = 0;
    virtual Status unload_scenario() = 0;
    virtual std::vector<std::string> get_available_maps() const = 0;
    virtual Status set_weather(const WeatherParameters& weather) = 0;
    virtual WeatherParameters get_weather() const = 0;
    
    // 车辆管理
    virtual Status spawn_vehicle(const std::string& blueprint_id, 
                                const Transform& spawn_point,
                                uint32_t& vehicle_id) = 0;
    virtual Status destroy_vehicle(uint32_t vehicle_id) = 0;
    virtual Status apply_control(uint32_t vehicle_id, const VehicleControl& control) = 0;
    virtual VehicleState get_vehicle_state(uint32_t vehicle_id) const = 0;
    virtual std::vector<uint32_t> get_vehicle_ids() const = 0;
    
    // 传感器管理
    virtual Status attach_sensor(uint32_t vehicle_id, 
                                const SensorConfig& config,
                                std::string& sensor_id) = 0;
    virtual Status detach_sensor(const std::string& sensor_id) = 0;
    virtual std::vector<std::string> get_sensor_ids(uint32_t vehicle_id) const = 0;
    
    // 数据回调注册
    virtual void register_sensor_callback(const std::string& sensor_id,
                                        std::function<void(std::shared_ptr<SensorData>)> callback) = 0;
    virtual void unregister_sensor_callback(const std::string& sensor_id) = 0;
    
    // 仿真控制
    virtual Status set_simulation_time_step(float time_step) = 0;
    virtual float get_simulation_time_step() const = 0;
    virtual Status tick() = 0; // 单步执行
    virtual Status set_synchronous_mode(bool enabled) = 0;
    virtual bool is_synchronous_mode() const = 0;
    
    // 调试和诊断
    virtual std::string get_debug_info() const = 0;
    virtual std::vector<std::string> get_error_messages() const = 0;
    virtual void clear_error_messages() = 0;
    
    // 扩展接口
    virtual Status execute_command(const std::string& command, 
                                 const std::unordered_map<std::string, std::string>& params,
                                 std::string& result) = 0;
};

// 仿真器工厂接口
class ISimulatorFactory {
public:
    virtual ~ISimulatorFactory() = default;
    virtual std::unique_ptr<ISimulator> create_simulator() = 0;
    virtual SimulatorType get_simulator_type() const = 0;
    virtual std::string get_simulator_name() const = 0;
    virtual std::vector<std::string> get_required_dependencies() const = 0;
    virtual bool check_dependencies() const = 0;
};

// 仿真器注册宏
#define REGISTER_SIMULATOR_FACTORY(factory_class, simulator_type) \
    namespace { \
        static bool registered_##factory_class = \
            SimulatorRegistry::instance().register_factory( \
                simulator_type, std::make_unique<factory_class>()); \
    }

} // namespace simulation_integration
