// 自动驾驶开发加速系统 - 场景类型定义
#pragma once

#include <string>
#include <vector>
#include <unordered_map>
#include <chrono>
#include <memory>

#include "common/types.h"

namespace simulation_integration {

// 场景状态枚举
enum class ScenarioStatus {
    CREATED,        // 已创建
    LOADED,         // 已加载
    RUNNING,        // 运行中
    PAUSED,         // 已暂停
    COMPLETED,      // 已完成
    FAILED,         // 失败
    MODIFIED,       // 已修改
    IMPORTED        // 已导入
};

// 天气参数
struct WeatherParameters {
    float cloudiness = 0.0f;        // 云量 [0.0, 100.0]
    float precipitation = 0.0f;     // 降水量 [0.0, 100.0]
    float precipitation_deposits = 0.0f; // 降水沉积 [0.0, 100.0]
    float wind_intensity = 0.0f;    // 风力强度 [0.0, 100.0]
    float sun_azimuth_angle = 0.0f; // 太阳方位角 [0.0, 360.0]
    float sun_altitude_angle = 90.0f; // 太阳高度角 [-90.0, 90.0]
    float fog_density = 0.0f;       // 雾密度 [0.0, 100.0]
    float fog_distance = 0.0f;      // 雾距离 [0.0, inf]
    float wetness = 0.0f;           // 湿度 [0.0, 100.0]
    float fog_falloff = 0.0f;       // 雾衰减 [0.0, inf]
};

// 交通灯状态
enum class TrafficLightState {
    RED,
    YELLOW,
    GREEN,
    OFF
};

// 交通灯配置
struct TrafficLight {
    std::string id;
    Transform transform;
    TrafficLightState initial_state = TrafficLightState::GREEN;
    float red_duration = 30.0f;     // 红灯持续时间(秒)
    float yellow_duration = 5.0f;   // 黄灯持续时间(秒)
    float green_duration = 30.0f;   // 绿灯持续时间(秒)
    bool is_automatic = true;       // 是否自动切换
};

// 静态障碍物
struct StaticObstacle {
    std::string id;
    std::string type;               // 障碍物类型 (cone, barrier, etc.)
    Transform transform;
    Vector3D size;                  // 尺寸
    std::unordered_map<std::string, std::string> properties;
};

// 动态对象行为类型
enum class BehaviorType {
    WAYPOINT_FOLLOWING,     // 路径点跟随
    LANE_FOLLOWING,         // 车道跟随
    TRAFFIC_AWARE,          // 交通感知
    AGGRESSIVE,             // 激进驾驶
    CAUTIOUS,              // 谨慎驾驶
    CUSTOM                 // 自定义行为
};

// 路径点
struct Waypoint {
    Transform transform;
    float speed = 0.0f;             // 期望速度 (m/s)
    float wait_time = 0.0f;         // 等待时间 (秒)
    std::unordered_map<std::string, std::string> actions; // 动作参数
};

// 动态对象
struct DynamicActor {
    std::string id;
    std::string type;               // 对象类型 (vehicle, pedestrian, cyclist)
    std::string blueprint_id;       // 蓝图ID
    Transform spawn_transform;      // 生成位置
    BehaviorType behavior = BehaviorType::LANE_FOLLOWING;
    std::vector<Waypoint> waypoints; // 路径点
    float target_speed = 10.0f;     // 目标速度 (m/s)
    std::unordered_map<std::string, std::string> behavior_parameters;
};

// 主车配置
struct EgoVehicle {
    std::string blueprint_id = "vehicle.tesla.model3";
    Transform spawn_transform;
    std::vector<SensorConfig> sensors; // 传感器配置
    std::unordered_map<std::string, std::string> properties;
};

// 触发器类型
enum class TriggerType {
    TIME_TRIGGER,           // 时间触发
    DISTANCE_TRIGGER,       // 距离触发
    SPEED_TRIGGER,          // 速度触发
    COLLISION_TRIGGER,      // 碰撞触发
    LANE_CHANGE_TRIGGER,    // 变道触发
    CUSTOM_TRIGGER          // 自定义触发
};

// 触发条件
struct TriggerCondition {
    TriggerType type;
    std::string target_actor_id;    // 目标对象ID
    std::unordered_map<std::string, float> parameters; // 触发参数
};

// 动作类型
enum class ActionType {
    SPEED_ACTION,           // 速度动作
    LANE_CHANGE_ACTION,     // 变道动作
    BRAKE_ACTION,           // 刹车动作
    STOP_ACTION,            // 停车动作
    TELEPORT_ACTION,        // 传送动作
    WEATHER_ACTION,         // 天气动作
    TRAFFIC_LIGHT_ACTION,   // 交通灯动作
    CUSTOM_ACTION           // 自定义动作
};

// 场景动作
struct ScenarioAction {
    std::string id;
    ActionType type;
    std::string target_actor_id;    // 目标对象ID
    std::unordered_map<std::string, std::string> parameters; // 动作参数
    float delay = 0.0f;             // 延迟时间(秒)
    float duration = 0.0f;          // 持续时间(秒)
};

// 场景事件
struct ScenarioEvent {
    std::string id;
    std::string name;
    std::string description;
    std::vector<TriggerCondition> triggers; // 触发条件
    std::vector<ScenarioAction> actions;    // 执行动作
    bool is_repeatable = false;     // 是否可重复
    int max_executions = 1;         // 最大执行次数
};

// 评估指标类型
enum class MetricType {
    COLLISION_COUNT,        // 碰撞次数
    TIME_TO_COLLISION,      // 碰撞时间
    LANE_DEVIATION,         // 车道偏离
    SPEED_VIOLATION,        // 速度违规
    COMFORT_METRIC,         // 舒适度指标
    EFFICIENCY_METRIC,      // 效率指标
    CUSTOM_METRIC          // 自定义指标
};

// 评估指标
struct EvaluationMetric {
    std::string id;
    MetricType type;
    std::string description;
    std::unordered_map<std::string, float> thresholds; // 阈值
    std::unordered_map<std::string, std::string> parameters; // 参数
};

// 场景定义
struct ScenarioDefinition {
    // 基本信息
    std::string name;
    std::string description;
    std::string author;
    std::string version = "1.0.0";
    std::vector<std::string> tags;
    
    // 地图和环境
    std::string map_name;
    WeatherParameters weather;
    float simulation_time_step = 0.05f; // 仿真时间步长(秒)
    float max_simulation_time = 300.0f; // 最大仿真时间(秒)
    
    // 对象配置
    EgoVehicle ego_vehicle;
    std::vector<DynamicActor> dynamic_actors;
    std::vector<StaticObstacle> static_obstacles;
    std::vector<TrafficLight> traffic_lights;
    
    // 事件和动作
    std::vector<ScenarioEvent> events;
    
    // 评估指标
    std::vector<EvaluationMetric> evaluation_metrics;
    
    // 扩展参数
    std::unordered_map<std::string, std::string> custom_parameters;
};

// 场景实例
struct Scenario {
    std::string id;
    ScenarioDefinition definition;
    ScenarioStatus status = ScenarioStatus::CREATED;
    
    // 版本信息
    int version = 1;
    std::chrono::system_clock::time_point created_time;
    std::chrono::system_clock::time_point modified_time;
    
    // 运行时信息
    float current_simulation_time = 0.0f;
    std::unordered_map<std::string, uint32_t> actor_ids; // 对象ID映射
    std::unordered_map<std::string, bool> event_states;  // 事件状态
    
    // 评估结果
    std::unordered_map<std::string, float> metric_results;
    std::vector<std::string> error_messages;
};

// 场景信息（用于列表显示）
struct ScenarioInfo {
    std::string id;
    std::string name;
    std::string description;
    ScenarioStatus status;
    int version;
    std::chrono::system_clock::time_point created_time;
    std::chrono::system_clock::time_point modified_time;
    std::vector<std::string> tags;
};

// 场景模板
struct ScenarioTemplate {
    std::string id;
    std::string name;
    std::string description;
    std::string category;
    ScenarioDefinition definition;
    std::vector<std::string> parameter_names; // 可配置参数名称
    std::unordered_map<std::string, std::string> default_parameters;
};

// 场景验证结果
struct ScenarioValidationResult {
    bool is_valid = true;
    std::string error_message;
    std::vector<std::string> warnings;
    std::vector<std::string> suggestions;
};

// 场景执行结果
struct ScenarioExecutionResult {
    std::string scenario_id;
    bool success = false;
    float execution_time = 0.0f;
    std::unordered_map<std::string, float> metric_results;
    std::vector<std::string> error_messages;
    std::vector<std::string> warnings;
    
    // 详细数据
    std::string log_file_path;
    std::string data_file_path;
    std::string video_file_path;
};

} // namespace simulation_integration
