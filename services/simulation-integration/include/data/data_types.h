// 自动驾驶开发加速系统 - 数据类型定义
#pragma once

#include <string>
#include <vector>
#include <memory>
#include <chrono>
#include <unordered_map>

#include "adapters/simulator_interface.h"

namespace simulation_integration {

// 数据包结构
struct DataPacket {
    std::string session_id;                     // 会话ID
    std::string sensor_id;                      // 传感器ID
    uint64_t timestamp;                         // 时间戳(微秒)
    std::string data_type;                      // 数据类型
    std::shared_ptr<SensorData> sensor_data;    // 传感器数据
    std::shared_ptr<VehicleState> vehicle_state; // 车辆状态数据
    std::unordered_map<std::string, std::string> metadata; // 元数据
};

// 循环缓冲区模板
template<typename T>
class CircularBuffer {
public:
    explicit CircularBuffer(size_t capacity) 
        : capacity_(capacity), buffer_(capacity), head_(0), tail_(0), size_(0) {}
    
    void push(const T& item) {
        std::lock_guard<std::mutex> lock(mutex_);
        buffer_[tail_] = item;
        tail_ = (tail_ + 1) % capacity_;
        
        if (size_ < capacity_) {
            size_++;
        } else {
            head_ = (head_ + 1) % capacity_;
        }
    }
    
    bool pop(T& item) {
        std::lock_guard<std::mutex> lock(mutex_);
        if (size_ == 0) {
            return false;
        }
        
        item = buffer_[head_];
        head_ = (head_ + 1) % capacity_;
        size_--;
        return true;
    }
    
    size_t size() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return size_;
    }
    
    bool empty() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return size_ == 0;
    }
    
    bool full() const {
        std::lock_guard<std::mutex> lock(mutex_);
        return size_ == capacity_;
    }

private:
    size_t capacity_;
    std::vector<T> buffer_;
    size_t head_;
    size_t tail_;
    size_t size_;
    mutable std::mutex mutex_;
};

// 传感器统计信息
struct SensorStatistics {
    std::string sensor_id;
    uint64_t packet_count = 0;
    uint64_t total_bytes = 0;
    double average_frequency = 0.0;
    std::chrono::steady_clock::time_point last_update_time;
    std::chrono::steady_clock::time_point first_update_time;
};

// 采集统计信息
struct CollectionStatistics {
    std::string session_id;
    std::chrono::steady_clock::time_point start_time;
    std::chrono::steady_clock::time_point end_time;
    uint64_t total_packets = 0;
    uint64_t total_bytes = 0;
    double duration_seconds = 0.0;
    double average_throughput_mbps = 0.0;
    std::unordered_map<std::string, SensorStatistics> sensor_stats;
};

// 数据流配置
struct DataStreamConfig {
    std::string stream_id;
    std::string data_type;
    bool enabled = true;
    uint32_t buffer_size = 1000;
    uint32_t batch_size = 100;
    uint32_t flush_interval_ms = 1000;
    std::string compression_type = "lz4";
    std::unordered_map<std::string, std::string> custom_config;
};

// 数据记录配置
struct DataRecordingConfig {
    std::string output_directory;
    std::string file_format = "parquet";  // parquet, csv, json
    bool enable_compression = true;
    std::string compression_codec = "snappy";
    uint64_t max_file_size_mb = 1024;
    uint32_t max_files_per_session = 100;
    bool enable_metadata = true;
    std::vector<DataStreamConfig> streams;
};

// 数据回放配置
struct DataPlaybackConfig {
    std::string data_directory;
    std::string session_id;
    double playback_speed = 1.0;
    bool loop_playback = false;
    uint64_t start_timestamp = 0;
    uint64_t end_timestamp = 0;
    std::vector<std::string> enabled_sensors;
    std::unordered_map<std::string, std::string> filter_conditions;
};

// 数据查询条件
struct DataQueryCondition {
    std::string session_id;
    std::vector<std::string> sensor_ids;
    uint64_t start_timestamp = 0;
    uint64_t end_timestamp = 0;
    std::string data_type;
    uint32_t limit = 1000;
    uint32_t offset = 0;
    std::unordered_map<std::string, std::string> filters;
};

// 数据查询结果
struct DataQueryResult {
    std::vector<std::shared_ptr<DataPacket>> packets;
    uint64_t total_count = 0;
    bool has_more = false;
    std::string next_cursor;
    std::chrono::milliseconds query_duration;
};

// 数据处理管道配置
struct DataProcessingPipelineConfig {
    std::string pipeline_id;
    std::string input_stream;
    std::string output_stream;
    std::vector<std::string> processing_steps;
    std::unordered_map<std::string, std::string> parameters;
    bool enable_parallel_processing = true;
    uint32_t worker_threads = 4;
};

// 数据处理结果
struct DataProcessingResult {
    bool success = false;
    std::string error_message;
    uint64_t processed_packets = 0;
    uint64_t failed_packets = 0;
    std::chrono::milliseconds processing_duration;
    std::unordered_map<std::string, std::string> metrics;
};

// 数据同步状态
enum class DataSyncStatus {
    IDLE,
    SYNCING,
    COMPLETED,
    FAILED,
    CANCELLED
};

// 数据同步配置
struct DataSyncConfig {
    std::string source_path;
    std::string destination_path;
    std::string sync_mode = "incremental";  // full, incremental
    bool verify_integrity = true;
    bool compress_during_sync = false;
    uint32_t parallel_transfers = 4;
    std::vector<std::string> exclude_patterns;
};

// 数据同步状态信息
struct DataSyncStatus {
    std::string sync_id;
    DataSyncStatus status = DataSyncStatus::IDLE;
    uint64_t total_files = 0;
    uint64_t synced_files = 0;
    uint64_t total_bytes = 0;
    uint64_t synced_bytes = 0;
    double progress_percentage = 0.0;
    std::chrono::steady_clock::time_point start_time;
    std::chrono::steady_clock::time_point end_time;
    std::string error_message;
};

// 数据存储信息
struct DataStorageInfo {
    std::string storage_id;
    std::string storage_type;  // local, s3, hdfs, etc.
    std::string base_path;
    uint64_t total_capacity_bytes = 0;
    uint64_t used_bytes = 0;
    uint64_t available_bytes = 0;
    double usage_percentage = 0.0;
    bool is_healthy = true;
    std::chrono::steady_clock::time_point last_check_time;
};

// 数据会话信息
struct DataSessionInfo {
    std::string session_id;
    std::string session_name;
    std::string description;
    std::chrono::system_clock::time_point created_time;
    std::chrono::system_clock::time_point start_time;
    std::chrono::system_clock::time_point end_time;
    std::string status;  // recording, completed, failed
    uint64_t total_packets = 0;
    uint64_t total_bytes = 0;
    double duration_seconds = 0.0;
    std::vector<std::string> sensor_list;
    std::string data_directory;
    std::unordered_map<std::string, std::string> metadata;
};

} // namespace simulation_integration
