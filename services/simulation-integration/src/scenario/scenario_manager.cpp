// 自动驾驶开发加速系统 - 场景管理器
#include "scenario/scenario_manager.h"
#include "scenario/scenario_parser.h"
#include "scenario/scenario_validator.h"
#include "core/logger.h"
#include "utils/file_utils.h"

#include <fstream>
#include <filesystem>
#include <algorithm>

namespace simulation_integration {

class ScenarioManager::Impl {
public:
    Impl(std::shared_ptr<Config> config) 
        : config_(config)
        , parser_(std::make_unique<ScenarioParser>())
        , validator_(std::make_unique<ScenarioValidator>()) {
        
        scenarios_path_ = config_->get_scenarios_path();
        std::filesystem::create_directories(scenarios_path_);
    }
    
    bool initialize() {
        try {
            // 加载场景模板
            load_scenario_templates();
            
            // 扫描现有场景
            scan_existing_scenarios();
            
            LOG_INFO("场景管理器初始化成功");
            LOG_INFO("场景模板数量: {}", scenario_templates_.size());
            LOG_INFO("现有场景数量: {}", scenarios_.size());
            
            return true;
            
        } catch (const std::exception& e) {
            LOG_ERROR("场景管理器初始化失败: {}", e.what());
            return false;
        }
    }
    
    Status create_scenario(const ScenarioDefinition& definition, std::string& scenario_id) {
        try {
            // 验证场景定义
            auto validation_result = validator_->validate(definition);
            if (!validation_result.is_valid) {
                return Status::error("场景验证失败: " + validation_result.error_message);
            }
            
            // 生成场景ID
            scenario_id = generate_scenario_id(definition.name);
            
            // 创建场景对象
            auto scenario = std::make_shared<Scenario>();
            scenario->id = scenario_id;
            scenario->definition = definition;
            scenario->status = ScenarioStatus::CREATED;
            scenario->created_time = std::chrono::system_clock::now();
            scenario->modified_time = scenario->created_time;
            scenario->version = 1;
            
            // 保存场景文件
            std::string scenario_file = get_scenario_file_path(scenario_id);
            if (!save_scenario_to_file(scenario, scenario_file)) {
                return Status::error("场景文件保存失败");
            }
            
            // 添加到内存缓存
            scenarios_[scenario_id] = scenario;
            
            LOG_INFO("场景创建成功: {} ({})", definition.name, scenario_id);
            
            return Status::ok();
            
        } catch (const std::exception& e) {
            LOG_ERROR("场景创建失败: {}", e.what());
            return Status::error(e.what());
        }
    }
    
    Status load_scenario(const std::string& scenario_id) {
        auto it = scenarios_.find(scenario_id);
        if (it != scenarios_.end()) {
            // 场景已加载
            return Status::ok();
        }
        
        try {
            // 从文件加载场景
            std::string scenario_file = get_scenario_file_path(scenario_id);
            if (!std::filesystem::exists(scenario_file)) {
                return Status::error("场景文件不存在: " + scenario_id);
            }
            
            auto scenario = load_scenario_from_file(scenario_file);
            if (!scenario) {
                return Status::error("场景文件加载失败");
            }
            
            // 验证场景
            auto validation_result = validator_->validate(scenario->definition);
            if (!validation_result.is_valid) {
                return Status::error("场景验证失败: " + validation_result.error_message);
            }
            
            // 添加到内存缓存
            scenarios_[scenario_id] = scenario;
            
            LOG_INFO("场景加载成功: {}", scenario_id);
            
            return Status::ok();
            
        } catch (const std::exception& e) {
            LOG_ERROR("场景加载失败: {}", e.what());
            return Status::error(e.what());
        }
    }
    
    Status update_scenario(const std::string& scenario_id, const ScenarioDefinition& definition) {
        auto it = scenarios_.find(scenario_id);
        if (it == scenarios_.end()) {
            return Status::error("场景不存在: " + scenario_id);
        }
        
        try {
            // 验证新的场景定义
            auto validation_result = validator_->validate(definition);
            if (!validation_result.is_valid) {
                return Status::error("场景验证失败: " + validation_result.error_message);
            }
            
            auto scenario = it->second;
            
            // 备份当前版本
            backup_scenario_version(scenario);
            
            // 更新场景定义
            scenario->definition = definition;
            scenario->modified_time = std::chrono::system_clock::now();
            scenario->version++;
            scenario->status = ScenarioStatus::MODIFIED;
            
            // 保存到文件
            std::string scenario_file = get_scenario_file_path(scenario_id);
            if (!save_scenario_to_file(scenario, scenario_file)) {
                return Status::error("场景文件保存失败");
            }
            
            LOG_INFO("场景更新成功: {} (版本: {})", scenario_id, scenario->version);
            
            return Status::ok();
            
        } catch (const std::exception& e) {
            LOG_ERROR("场景更新失败: {}", e.what());
            return Status::error(e.what());
        }
    }
    
    Status delete_scenario(const std::string& scenario_id) {
        auto it = scenarios_.find(scenario_id);
        if (it == scenarios_.end()) {
            return Status::error("场景不存在: " + scenario_id);
        }
        
        try {
            // 检查场景是否正在使用
            if (it->second->status == ScenarioStatus::RUNNING) {
                return Status::error("场景正在运行，无法删除");
            }
            
            // 删除场景文件
            std::string scenario_file = get_scenario_file_path(scenario_id);
            if (std::filesystem::exists(scenario_file)) {
                std::filesystem::remove(scenario_file);
            }
            
            // 删除版本备份
            delete_scenario_versions(scenario_id);
            
            // 从内存中移除
            scenarios_.erase(it);
            
            LOG_INFO("场景删除成功: {}", scenario_id);
            
            return Status::ok();
            
        } catch (const std::exception& e) {
            LOG_ERROR("场景删除失败: {}", e.what());
            return Status::error(e.what());
        }
    }
    
    std::shared_ptr<Scenario> get_scenario(const std::string& scenario_id) {
        auto it = scenarios_.find(scenario_id);
        return (it != scenarios_.end()) ? it->second : nullptr;
    }
    
    std::vector<ScenarioInfo> list_scenarios() const {
        std::vector<ScenarioInfo> scenario_list;
        
        for (const auto& [id, scenario] : scenarios_) {
            ScenarioInfo info;
            info.id = id;
            info.name = scenario->definition.name;
            info.description = scenario->definition.description;
            info.status = scenario->status;
            info.version = scenario->version;
            info.created_time = scenario->created_time;
            info.modified_time = scenario->modified_time;
            info.tags = scenario->definition.tags;
            
            scenario_list.push_back(info);
        }
        
        // 按修改时间排序
        std::sort(scenario_list.begin(), scenario_list.end(),
                 [](const ScenarioInfo& a, const ScenarioInfo& b) {
                     return a.modified_time > b.modified_time;
                 });
        
        return scenario_list;
    }
    
    std::vector<ScenarioTemplate> get_scenario_templates() const {
        return scenario_templates_;
    }
    
    Status create_scenario_from_template(const std::string& template_id,
                                       const std::string& scenario_name,
                                       const std::unordered_map<std::string, std::string>& parameters,
                                       std::string& scenario_id) {
        // 查找模板
        auto template_it = std::find_if(scenario_templates_.begin(), scenario_templates_.end(),
                                       [&template_id](const ScenarioTemplate& t) {
                                           return t.id == template_id;
                                       });
        
        if (template_it == scenario_templates_.end()) {
            return Status::error("场景模板不存在: " + template_id);
        }
        
        try {
            // 从模板创建场景定义
            ScenarioDefinition definition = template_it->definition;
            definition.name = scenario_name;
            
            // 应用参数
            apply_template_parameters(definition, parameters);
            
            // 创建场景
            return create_scenario(definition, scenario_id);
            
        } catch (const std::exception& e) {
            LOG_ERROR("从模板创建场景失败: {}", e.what());
            return Status::error(e.what());
        }
    }
    
    Status export_scenario(const std::string& scenario_id, const std::string& export_path) {
        auto scenario = get_scenario(scenario_id);
        if (!scenario) {
            return Status::error("场景不存在: " + scenario_id);
        }
        
        try {
            // 创建导出目录
            std::filesystem::create_directories(export_path);
            
            // 导出场景定义文件
            std::string scenario_file = export_path + "/" + scenario_id + ".json";
            if (!save_scenario_to_file(scenario, scenario_file)) {
                return Status::error("场景导出失败");
            }
            
            // 导出相关资源文件
            export_scenario_resources(scenario, export_path);
            
            LOG_INFO("场景导出成功: {} -> {}", scenario_id, export_path);
            
            return Status::ok();
            
        } catch (const std::exception& e) {
            LOG_ERROR("场景导出失败: {}", e.what());
            return Status::error(e.what());
        }
    }
    
    Status import_scenario(const std::string& import_path, std::string& scenario_id) {
        try {
            // 查找场景文件
            std::string scenario_file = find_scenario_file_in_path(import_path);
            if (scenario_file.empty()) {
                return Status::error("导入路径中未找到场景文件");
            }
            
            // 加载场景
            auto scenario = load_scenario_from_file(scenario_file);
            if (!scenario) {
                return Status::error("场景文件加载失败");
            }
            
            // 生成新的场景ID
            scenario_id = generate_scenario_id(scenario->definition.name);
            scenario->id = scenario_id;
            scenario->created_time = std::chrono::system_clock::now();
            scenario->modified_time = scenario->created_time;
            scenario->version = 1;
            scenario->status = ScenarioStatus::IMPORTED;
            
            // 导入相关资源文件
            import_scenario_resources(import_path, scenario_id);
            
            // 保存场景
            std::string new_scenario_file = get_scenario_file_path(scenario_id);
            if (!save_scenario_to_file(scenario, new_scenario_file)) {
                return Status::error("场景保存失败");
            }
            
            // 添加到内存缓存
            scenarios_[scenario_id] = scenario;
            
            LOG_INFO("场景导入成功: {} ({})", scenario->definition.name, scenario_id);
            
            return Status::ok();
            
        } catch (const std::exception& e) {
            LOG_ERROR("场景导入失败: {}", e.what());
            return Status::error(e.what());
        }
    }

private:
    void load_scenario_templates() {
        std::string templates_path = config_->get_scenario_templates_path();
        if (!std::filesystem::exists(templates_path)) {
            LOG_WARN("场景模板目录不存在: {}", templates_path);
            return;
        }
        
        for (const auto& entry : std::filesystem::directory_iterator(templates_path)) {
            if (entry.is_regular_file() && entry.path().extension() == ".json") {
                try {
                    auto scenario_template = load_scenario_template_from_file(entry.path().string());
                    if (scenario_template) {
                        scenario_templates_.push_back(*scenario_template);
                        LOG_DEBUG("加载场景模板: {}", scenario_template->name);
                    }
                } catch (const std::exception& e) {
                    LOG_WARN("加载场景模板失败 {}: {}", entry.path().string(), e.what());
                }
            }
        }
    }
    
    void scan_existing_scenarios() {
        if (!std::filesystem::exists(scenarios_path_)) {
            return;
        }
        
        for (const auto& entry : std::filesystem::directory_iterator(scenarios_path_)) {
            if (entry.is_regular_file() && entry.path().extension() == ".json") {
                try {
                    auto scenario = load_scenario_from_file(entry.path().string());
                    if (scenario) {
                        scenarios_[scenario->id] = scenario;
                        LOG_DEBUG("扫描到场景: {}", scenario->definition.name);
                    }
                } catch (const std::exception& e) {
                    LOG_WARN("加载场景失败 {}: {}", entry.path().string(), e.what());
                }
            }
        }
    }
    
    std::string generate_scenario_id(const std::string& name) {
        // 生成基于时间戳和名称的唯一ID
        auto now = std::chrono::system_clock::now();
        auto timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
            now.time_since_epoch()).count();
        
        std::string clean_name = name;
        std::replace_if(clean_name.begin(), clean_name.end(),
                       [](char c) { return !std::isalnum(c); }, '_');
        
        return clean_name + "_" + std::to_string(timestamp);
    }
    
    std::string get_scenario_file_path(const std::string& scenario_id) {
        return scenarios_path_ + "/" + scenario_id + ".json";
    }
    
    bool save_scenario_to_file(std::shared_ptr<Scenario> scenario, const std::string& file_path) {
        try {
            auto json_data = parser_->serialize_scenario(*scenario);
            
            std::ofstream file(file_path);
            if (!file.is_open()) {
                LOG_ERROR("无法打开文件进行写入: {}", file_path);
                return false;
            }
            
            file << json_data.dump(2);
            file.close();
            
            return true;
            
        } catch (const std::exception& e) {
            LOG_ERROR("保存场景文件失败: {}", e.what());
            return false;
        }
    }
    
    std::shared_ptr<Scenario> load_scenario_from_file(const std::string& file_path) {
        try {
            std::ifstream file(file_path);
            if (!file.is_open()) {
                LOG_ERROR("无法打开场景文件: {}", file_path);
                return nullptr;
            }
            
            nlohmann::json json_data;
            file >> json_data;
            file.close();
            
            return parser_->parse_scenario(json_data);
            
        } catch (const std::exception& e) {
            LOG_ERROR("加载场景文件失败: {}", e.what());
            return nullptr;
        }
    }
    
    // 其他辅助方法...
    
private:
    std::shared_ptr<Config> config_;
    std::unique_ptr<ScenarioParser> parser_;
    std::unique_ptr<ScenarioValidator> validator_;
    
    std::string scenarios_path_;
    std::unordered_map<std::string, std::shared_ptr<Scenario>> scenarios_;
    std::vector<ScenarioTemplate> scenario_templates_;
};

// ScenarioManager实现
ScenarioManager::ScenarioManager(std::shared_ptr<Config> config)
    : impl_(std::make_unique<Impl>(config)) {}

ScenarioManager::~ScenarioManager() = default;

bool ScenarioManager::initialize() {
    return impl_->initialize();
}

Status ScenarioManager::create_scenario(const ScenarioDefinition& definition, std::string& scenario_id) {
    return impl_->create_scenario(definition, scenario_id);
}

Status ScenarioManager::load_scenario(const std::string& scenario_id) {
    return impl_->load_scenario(scenario_id);
}

// 其他方法的实现...

} // namespace simulation_integration
