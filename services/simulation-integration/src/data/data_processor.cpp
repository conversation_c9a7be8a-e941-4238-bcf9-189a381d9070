// 自动驾驶开发加速系统 - 数据处理器
#include "data/data_processor.h"
#include "core/logger.h"
#include "utils/image_utils.h"
#include "utils/pointcloud_utils.h"

#include <opencv2/opencv.hpp>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl/filters/voxel_grid.h>
#include <pcl/filters/statistical_outlier_removal.h>

namespace simulation_integration {

class DataProcessor::Impl {
public:
    Impl(std::shared_ptr<Config> config) : config_(config) {}
    
    bool initialize() {
        try {
            // 初始化图像处理参数
            image_resize_enabled_ = config_->get_bool("data.image.resize_enabled", false);
            image_target_width_ = config_->get_int("data.image.target_width", 1920);
            image_target_height_ = config_->get_int("data.image.target_height", 1080);
            image_compression_quality_ = config_->get_int("data.image.compression_quality", 95);
            
            // 初始化点云处理参数
            pointcloud_downsample_enabled_ = config_->get_bool("data.pointcloud.downsample_enabled", true);
            pointcloud_voxel_size_ = config_->get_float("data.pointcloud.voxel_size", 0.1f);
            pointcloud_outlier_removal_enabled_ = config_->get_bool("data.pointcloud.outlier_removal_enabled", true);
            pointcloud_outlier_k_ = config_->get_int("data.pointcloud.outlier_k", 50);
            pointcloud_outlier_std_ratio_ = config_->get_float("data.pointcloud.outlier_std_ratio", 1.0f);
            
            // 初始化数据过滤参数
            enable_data_validation_ = config_->get_bool("data.validation.enabled", true);
            max_image_size_mb_ = config_->get_float("data.validation.max_image_size_mb", 50.0f);
            max_pointcloud_points_ = config_->get_int("data.validation.max_pointcloud_points", 1000000);
            
            LOG_INFO("数据处理器初始化成功");
            return true;
            
        } catch (const std::exception& e) {
            LOG_ERROR("数据处理器初始化失败: {}", e.what());
            return false;
        }
    }
    
    Status process_image_data(std::shared_ptr<ImageData> image_data) {
        if (!image_data) {
            return Status::error("图像数据为空");
        }
        
        try {
            // 数据验证
            if (enable_data_validation_) {
                auto validation_result = validate_image_data(image_data);
                if (!validation_result.is_ok()) {
                    return validation_result;
                }
            }
            
            // 转换为OpenCV格式
            cv::Mat image = convert_to_opencv_mat(image_data);
            if (image.empty()) {
                return Status::error("图像数据转换失败");
            }
            
            // 图像预处理
            if (image_resize_enabled_) {
                cv::resize(image, image, cv::Size(image_target_width_, image_target_height_));
                
                // 更新图像数据
                image_data->width = image_target_width_;
                image_data->height = image_target_height_;
            }
            
            // 图像增强（可选）
            if (config_->get_bool("data.image.enhance_enabled", false)) {
                enhance_image(image);
            }
            
            // 转换回原始格式
            convert_from_opencv_mat(image, image_data);
            
            // 更新处理统计
            update_processing_stats("image", true);
            
            return Status::ok();
            
        } catch (const std::exception& e) {
            LOG_ERROR("图像数据处理失败: {}", e.what());
            update_processing_stats("image", false);
            return Status::error(e.what());
        }
    }
    
    Status process_pointcloud_data(std::shared_ptr<PointCloudData> pointcloud_data) {
        if (!pointcloud_data) {
            return Status::error("点云数据为空");
        }
        
        try {
            // 数据验证
            if (enable_data_validation_) {
                auto validation_result = validate_pointcloud_data(pointcloud_data);
                if (!validation_result.is_ok()) {
                    return validation_result;
                }
            }
            
            // 转换为PCL格式
            pcl::PointCloud<pcl::PointXYZI>::Ptr cloud = convert_to_pcl_cloud(pointcloud_data);
            if (!cloud || cloud->empty()) {
                return Status::error("点云数据转换失败");
            }
            
            // 体素下采样
            if (pointcloud_downsample_enabled_ && cloud->size() > 10000) {
                pcl::VoxelGrid<pcl::PointXYZI> voxel_filter;
                voxel_filter.setInputCloud(cloud);
                voxel_filter.setLeafSize(pointcloud_voxel_size_, pointcloud_voxel_size_, pointcloud_voxel_size_);
                
                pcl::PointCloud<pcl::PointXYZI>::Ptr filtered_cloud(new pcl::PointCloud<pcl::PointXYZI>);
                voxel_filter.filter(*filtered_cloud);
                cloud = filtered_cloud;
            }
            
            // 离群点移除
            if (pointcloud_outlier_removal_enabled_ && cloud->size() > 100) {
                pcl::StatisticalOutlierRemoval<pcl::PointXYZI> outlier_filter;
                outlier_filter.setInputCloud(cloud);
                outlier_filter.setMeanK(pointcloud_outlier_k_);
                outlier_filter.setStddevMulThresh(pointcloud_outlier_std_ratio_);
                
                pcl::PointCloud<pcl::PointXYZI>::Ptr filtered_cloud(new pcl::PointCloud<pcl::PointXYZI>);
                outlier_filter.filter(*filtered_cloud);
                cloud = filtered_cloud;
            }
            
            // 转换回原始格式
            convert_from_pcl_cloud(cloud, pointcloud_data);
            
            // 更新处理统计
            update_processing_stats("pointcloud", true);
            
            return Status::ok();
            
        } catch (const std::exception& e) {
            LOG_ERROR("点云数据处理失败: {}", e.what());
            update_processing_stats("pointcloud", false);
            return Status::error(e.what());
        }
    }
    
    Status process_imu_data(std::shared_ptr<IMUData> imu_data) {
        if (!imu_data) {
            return Status::error("IMU数据为空");
        }
        
        try {
            // 数据验证
            if (enable_data_validation_) {
                auto validation_result = validate_imu_data(imu_data);
                if (!validation_result.is_ok()) {
                    return validation_result;
                }
            }
            
            // IMU数据滤波（简单的低通滤波）
            if (config_->get_bool("data.imu.filter_enabled", true)) {
                apply_imu_filter(imu_data);
            }
            
            // 坐标系转换（如果需要）
            if (config_->get_bool("data.imu.coordinate_transform_enabled", false)) {
                transform_imu_coordinates(imu_data);
            }
            
            // 更新处理统计
            update_processing_stats("imu", true);
            
            return Status::ok();
            
        } catch (const std::exception& e) {
            LOG_ERROR("IMU数据处理失败: {}", e.what());
            update_processing_stats("imu", false);
            return Status::error(e.what());
        }
    }
    
    Status process_gps_data(std::shared_ptr<GPSData> gps_data) {
        if (!gps_data) {
            return Status::error("GPS数据为空");
        }
        
        try {
            // 数据验证
            if (enable_data_validation_) {
                auto validation_result = validate_gps_data(gps_data);
                if (!validation_result.is_ok()) {
                    return validation_result;
                }
            }
            
            // GPS数据平滑处理
            if (config_->get_bool("data.gps.smoothing_enabled", true)) {
                apply_gps_smoothing(gps_data);
            }
            
            // 坐标系转换（WGS84 to UTM等）
            if (config_->get_bool("data.gps.coordinate_transform_enabled", false)) {
                transform_gps_coordinates(gps_data);
            }
            
            // 更新处理统计
            update_processing_stats("gps", true);
            
            return Status::ok();
            
        } catch (const std::exception& e) {
            LOG_ERROR("GPS数据处理失败: {}", e.what());
            update_processing_stats("gps", false);
            return Status::error(e.what());
        }
    }
    
    DataProcessingResult get_processing_statistics() const {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        
        DataProcessingResult result;
        result.success = true;
        result.processed_packets = total_processed_packets_;
        result.failed_packets = total_failed_packets_;
        
        // 计算处理时间
        if (processing_start_time_.time_since_epoch().count() > 0) {
            auto now = std::chrono::steady_clock::now();
            result.processing_duration = std::chrono::duration_cast<std::chrono::milliseconds>(
                now - processing_start_time_);
        }
        
        // 添加详细统计信息
        for (const auto& [data_type, stats] : type_stats_) {
            result.metrics[data_type + "_processed"] = std::to_string(stats.processed_count);
            result.metrics[data_type + "_failed"] = std::to_string(stats.failed_count);
            result.metrics[data_type + "_success_rate"] = std::to_string(
                stats.processed_count > 0 ? 
                (double)stats.processed_count / (stats.processed_count + stats.failed_count) * 100.0 : 0.0);
        }
        
        return result;
    }
    
    void reset_statistics() {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        total_processed_packets_ = 0;
        total_failed_packets_ = 0;
        type_stats_.clear();
        processing_start_time_ = std::chrono::steady_clock::now();
    }
    
    void shutdown() {
        LOG_INFO("数据处理器正在关闭...");
        // 清理资源
        LOG_INFO("数据处理器已关闭");
    }

private:
    Status validate_image_data(std::shared_ptr<ImageData> image_data) {
        // 检查图像尺寸
        if (image_data->width == 0 || image_data->height == 0) {
            return Status::error("图像尺寸无效");
        }
        
        // 检查图像数据大小
        size_t expected_size = image_data->width * image_data->height * image_data->channels;
        if (image_data->data.size() != expected_size) {
            return Status::error("图像数据大小不匹配");
        }
        
        // 检查图像大小限制
        double size_mb = image_data->data.size() / (1024.0 * 1024.0);
        if (size_mb > max_image_size_mb_) {
            return Status::error("图像大小超过限制: " + std::to_string(size_mb) + "MB");
        }
        
        return Status::ok();
    }
    
    Status validate_pointcloud_data(std::shared_ptr<PointCloudData> pointcloud_data) {
        // 检查点云大小
        if (pointcloud_data->points.empty()) {
            return Status::error("点云数据为空");
        }
        
        // 检查点云点数限制
        if (pointcloud_data->points.size() > max_pointcloud_points_) {
            return Status::error("点云点数超过限制: " + std::to_string(pointcloud_data->points.size()));
        }
        
        return Status::ok();
    }
    
    Status validate_imu_data(std::shared_ptr<IMUData> imu_data) {
        // 检查IMU数据范围
        const float max_accel = 50.0f;  // m/s^2
        const float max_gyro = 10.0f;   // rad/s
        
        if (std::abs(imu_data->accelerometer.x) > max_accel ||
            std::abs(imu_data->accelerometer.y) > max_accel ||
            std::abs(imu_data->accelerometer.z) > max_accel) {
            return Status::error("加速度计数据超出范围");
        }
        
        if (std::abs(imu_data->gyroscope.x) > max_gyro ||
            std::abs(imu_data->gyroscope.y) > max_gyro ||
            std::abs(imu_data->gyroscope.z) > max_gyro) {
            return Status::error("陀螺仪数据超出范围");
        }
        
        return Status::ok();
    }
    
    Status validate_gps_data(std::shared_ptr<GPSData> gps_data) {
        // 检查GPS坐标范围
        if (gps_data->latitude < -90.0 || gps_data->latitude > 90.0) {
            return Status::error("纬度超出范围");
        }
        
        if (gps_data->longitude < -180.0 || gps_data->longitude > 180.0) {
            return Status::error("经度超出范围");
        }
        
        return Status::ok();
    }
    
    cv::Mat convert_to_opencv_mat(std::shared_ptr<ImageData> image_data) {
        int cv_type;
        if (image_data->channels == 1) {
            cv_type = CV_8UC1;
        } else if (image_data->channels == 3) {
            cv_type = CV_8UC3;
        } else if (image_data->channels == 4) {
            cv_type = CV_8UC4;
        } else {
            return cv::Mat();
        }
        
        return cv::Mat(image_data->height, image_data->width, cv_type, image_data->data.data());
    }
    
    void convert_from_opencv_mat(const cv::Mat& image, std::shared_ptr<ImageData> image_data) {
        image_data->width = image.cols;
        image_data->height = image.rows;
        image_data->channels = image.channels();
        
        size_t data_size = image.total() * image.elemSize();
        image_data->data.resize(data_size);
        std::memcpy(image_data->data.data(), image.data, data_size);
    }
    
    void enhance_image(cv::Mat& image) {
        // 简单的图像增强：直方图均衡化
        if (image.channels() == 1) {
            cv::equalizeHist(image, image);
        } else if (image.channels() == 3) {
            cv::Mat yuv;
            cv::cvtColor(image, yuv, cv::COLOR_BGR2YUV);
            
            std::vector<cv::Mat> channels;
            cv::split(yuv, channels);
            cv::equalizeHist(channels[0], channels[0]);
            cv::merge(channels, yuv);
            
            cv::cvtColor(yuv, image, cv::COLOR_YUV2BGR);
        }
    }
    
    pcl::PointCloud<pcl::PointXYZI>::Ptr convert_to_pcl_cloud(std::shared_ptr<PointCloudData> pointcloud_data) {
        pcl::PointCloud<pcl::PointXYZI>::Ptr cloud(new pcl::PointCloud<pcl::PointXYZI>);
        cloud->reserve(pointcloud_data->points.size());
        
        for (const auto& point : pointcloud_data->points) {
            pcl::PointXYZI pcl_point;
            pcl_point.x = point.x;
            pcl_point.y = point.y;
            pcl_point.z = point.z;
            pcl_point.intensity = point.intensity;
            cloud->push_back(pcl_point);
        }
        
        return cloud;
    }
    
    void convert_from_pcl_cloud(pcl::PointCloud<pcl::PointXYZI>::Ptr cloud, 
                               std::shared_ptr<PointCloudData> pointcloud_data) {
        pointcloud_data->points.clear();
        pointcloud_data->points.reserve(cloud->size());
        
        for (const auto& pcl_point : *cloud) {
            PointCloudData::Point point;
            point.x = pcl_point.x;
            point.y = pcl_point.y;
            point.z = pcl_point.z;
            point.intensity = pcl_point.intensity;
            pointcloud_data->points.push_back(point);
        }
    }
    
    void apply_imu_filter(std::shared_ptr<IMUData> imu_data) {
        // 简单的低通滤波器
        const float alpha = 0.8f;  // 滤波系数
        
        // 这里应该维护历史数据进行滤波，简化实现
        // 实际应用中需要维护滤波器状态
    }
    
    void transform_imu_coordinates(std::shared_ptr<IMUData> imu_data) {
        // 坐标系转换（示例：从NED到ENU）
        // 这里是简化实现，实际需要根据具体坐标系进行转换
    }
    
    void apply_gps_smoothing(std::shared_ptr<GPSData> gps_data) {
        // GPS数据平滑处理
        // 这里应该维护历史GPS数据进行平滑，简化实现
    }
    
    void transform_gps_coordinates(std::shared_ptr<GPSData> gps_data) {
        // GPS坐标转换（例如WGS84到UTM）
        // 这里是简化实现，实际需要使用专业的坐标转换库
    }
    
    void update_processing_stats(const std::string& data_type, bool success) {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        
        if (success) {
            total_processed_packets_++;
            type_stats_[data_type].processed_count++;
        } else {
            total_failed_packets_++;
            type_stats_[data_type].failed_count++;
        }
        
        // 初始化开始时间
        if (processing_start_time_.time_since_epoch().count() == 0) {
            processing_start_time_ = std::chrono::steady_clock::now();
        }
    }

private:
    std::shared_ptr<Config> config_;
    
    // 图像处理参数
    bool image_resize_enabled_;
    uint32_t image_target_width_;
    uint32_t image_target_height_;
    int image_compression_quality_;
    
    // 点云处理参数
    bool pointcloud_downsample_enabled_;
    float pointcloud_voxel_size_;
    bool pointcloud_outlier_removal_enabled_;
    int pointcloud_outlier_k_;
    float pointcloud_outlier_std_ratio_;
    
    // 数据验证参数
    bool enable_data_validation_;
    float max_image_size_mb_;
    uint32_t max_pointcloud_points_;
    
    // 统计信息
    mutable std::mutex stats_mutex_;
    uint64_t total_processed_packets_ = 0;
    uint64_t total_failed_packets_ = 0;
    std::chrono::steady_clock::time_point processing_start_time_;
    
    struct TypeStatistics {
        uint64_t processed_count = 0;
        uint64_t failed_count = 0;
    };
    std::unordered_map<std::string, TypeStatistics> type_stats_;
};

// DataProcessor实现
DataProcessor::DataProcessor(std::shared_ptr<Config> config)
    : impl_(std::make_unique<Impl>(config)) {}

DataProcessor::~DataProcessor() = default;

bool DataProcessor::initialize() {
    return impl_->initialize();
}

Status DataProcessor::process_image_data(std::shared_ptr<ImageData> image_data) {
    return impl_->process_image_data(image_data);
}

Status DataProcessor::process_pointcloud_data(std::shared_ptr<PointCloudData> pointcloud_data) {
    return impl_->process_pointcloud_data(pointcloud_data);
}

Status DataProcessor::process_imu_data(std::shared_ptr<IMUData> imu_data) {
    return impl_->process_imu_data(imu_data);
}

Status DataProcessor::process_gps_data(std::shared_ptr<GPSData> gps_data) {
    return impl_->process_gps_data(gps_data);
}

DataProcessingResult DataProcessor::get_processing_statistics() const {
    return impl_->get_processing_statistics();
}

void DataProcessor::reset_statistics() {
    impl_->reset_statistics();
}

void DataProcessor::shutdown() {
    impl_->shutdown();
}

} // namespace simulation_integration
