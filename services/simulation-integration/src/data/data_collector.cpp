// 自动驾驶开发加速系统 - 数据采集器
#include "data/data_collector.h"
#include "data/data_processor.h"
#include "data/data_recorder.h"
#include "core/logger.h"
#include "utils/time_utils.h"

#include <arrow/api.h>
#include <arrow/io/api.h>
#include <parquet/arrow/writer.h>
#include <thread>
#include <chrono>
#include <queue>
#include <mutex>
#include <condition_variable>

namespace simulation_integration {

class DataCollector::Impl {
public:
    Impl(std::shared_ptr<Config> config) 
        : config_(config)
        , is_collecting_(false)
        , data_processor_(std::make_unique<DataProcessor>(config))
        , data_recorder_(std::make_unique<DataRecorder>(config)) {}
    
    bool initialize() {
        try {
            // 初始化数据处理器
            if (!data_processor_->initialize()) {
                LOG_ERROR("数据处理器初始化失败");
                return false;
            }
            
            // 初始化数据记录器
            if (!data_recorder_->initialize()) {
                LOG_ERROR("数据记录器初始化失败");
                return false;
            }
            
            // 创建数据缓冲区
            create_data_buffers();
            
            // 初始化Arrow内存池
            arrow_pool_ = arrow::default_memory_pool();
            
            LOG_INFO("数据采集器初始化成功");
            return true;
            
        } catch (const std::exception& e) {
            LOG_ERROR("数据采集器初始化失败: {}", e.what());
            return false;
        }
    }
    
    Status start_collection(const std::string& session_id) {
        if (is_collecting_) {
            return Status::error("数据采集已在进行中");
        }
        
        try {
            current_session_id_ = session_id;
            is_collecting_ = true;
            
            // 启动数据处理线程
            processing_thread_ = std::thread([this]() {
                process_data_loop();
            });
            
            // 启动数据记录线程
            recording_thread_ = std::thread([this]() {
                record_data_loop();
            });
            
            LOG_INFO("数据采集开始: 会话ID={}", session_id);
            return Status::ok();
            
        } catch (const std::exception& e) {
            is_collecting_ = false;
            LOG_ERROR("启动数据采集失败: {}", e.what());
            return Status::error(e.what());
        }
    }
    
    Status stop_collection() {
        if (!is_collecting_) {
            return Status::ok();
        }
        
        try {
            is_collecting_ = false;
            
            // 通知处理线程停止
            data_condition_.notify_all();
            
            // 等待线程结束
            if (processing_thread_.joinable()) {
                processing_thread_.join();
            }
            
            if (recording_thread_.joinable()) {
                recording_thread_.join();
            }
            
            // 刷新剩余数据
            flush_remaining_data();
            
            LOG_INFO("数据采集停止: 会话ID={}", current_session_id_);
            return Status::ok();
            
        } catch (const std::exception& e) {
            LOG_ERROR("停止数据采集失败: {}", e.what());
            return Status::error(e.what());
        }
    }
    
    Status collect_sensor_data(const std::string& sensor_id, 
                              std::shared_ptr<SensorData> data) {
        if (!is_collecting_) {
            return Status::error("数据采集未启动");
        }
        
        try {
            // 添加时间戳和会话信息
            data->timestamp = get_current_timestamp_us();
            
            // 创建数据包
            auto data_packet = std::make_shared<DataPacket>();
            data_packet->session_id = current_session_id_;
            data_packet->sensor_id = sensor_id;
            data_packet->timestamp = data->timestamp;
            data_packet->data_type = get_data_type_string(data->type);
            data_packet->sensor_data = data;
            
            // 添加到处理队列
            {
                std::lock_guard<std::mutex> lock(data_mutex_);
                data_queue_.push(data_packet);
            }
            data_condition_.notify_one();
            
            // 更新统计信息
            update_collection_stats(sensor_id, data);
            
            return Status::ok();
            
        } catch (const std::exception& e) {
            LOG_ERROR("采集传感器数据失败: {}", e.what());
            return Status::error(e.what());
        }
    }
    
    Status collect_vehicle_state(const std::string& vehicle_id, 
                                const VehicleState& state) {
        if (!is_collecting_) {
            return Status::error("数据采集未启动");
        }
        
        try {
            // 创建车辆状态数据包
            auto data_packet = std::make_shared<DataPacket>();
            data_packet->session_id = current_session_id_;
            data_packet->sensor_id = vehicle_id;
            data_packet->timestamp = get_current_timestamp_us();
            data_packet->data_type = "vehicle_state";
            
            // 序列化车辆状态
            data_packet->vehicle_state = std::make_shared<VehicleState>(state);
            
            // 添加到处理队列
            {
                std::lock_guard<std::mutex> lock(data_mutex_);
                data_queue_.push(data_packet);
            }
            data_condition_.notify_one();
            
            return Status::ok();
            
        } catch (const std::exception& e) {
            LOG_ERROR("采集车辆状态失败: {}", e.what());
            return Status::error(e.what());
        }
    }
    
    CollectionStatistics get_statistics() const {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        return collection_stats_;
    }
    
    std::vector<std::string> get_active_sensors() const {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        std::vector<std::string> sensors;
        for (const auto& [sensor_id, stats] : sensor_stats_) {
            sensors.push_back(sensor_id);
        }
        return sensors;
    }
    
    void shutdown() {
        LOG_INFO("正在关闭数据采集器...");
        
        // 停止采集
        stop_collection();
        
        // 关闭数据处理器和记录器
        if (data_processor_) {
            data_processor_->shutdown();
        }
        
        if (data_recorder_) {
            data_recorder_->shutdown();
        }
        
        LOG_INFO("数据采集器已关闭");
    }

private:
    void create_data_buffers() {
        // 为不同类型的传感器数据创建缓冲区
        image_buffer_ = std::make_shared<CircularBuffer<ImageData>>(
            config_->get_image_buffer_size());
        
        pointcloud_buffer_ = std::make_shared<CircularBuffer<PointCloudData>>(
            config_->get_pointcloud_buffer_size());
        
        imu_buffer_ = std::make_shared<CircularBuffer<IMUData>>(
            config_->get_imu_buffer_size());
        
        gps_buffer_ = std::make_shared<CircularBuffer<GPSData>>(
            config_->get_gps_buffer_size());
        
        vehicle_state_buffer_ = std::make_shared<CircularBuffer<VehicleState>>(
            config_->get_vehicle_state_buffer_size());
    }
    
    void process_data_loop() {
        LOG_INFO("数据处理线程启动");
        
        while (is_collecting_) {
            std::unique_lock<std::mutex> lock(data_mutex_);
            
            // 等待数据或停止信号
            data_condition_.wait(lock, [this]() {
                return !data_queue_.empty() || !is_collecting_;
            });
            
            // 处理队列中的数据
            while (!data_queue_.empty()) {
                auto data_packet = data_queue_.front();
                data_queue_.pop();
                
                lock.unlock();
                
                // 处理数据包
                process_data_packet(data_packet);
                
                lock.lock();
            }
        }
        
        LOG_INFO("数据处理线程结束");
    }
    
    void record_data_loop() {
        LOG_INFO("数据记录线程启动");
        
        auto last_flush_time = std::chrono::steady_clock::now();
        const auto flush_interval = std::chrono::seconds(
            config_->get_data_flush_interval_seconds());
        
        while (is_collecting_) {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
            
            auto now = std::chrono::steady_clock::now();
            if (now - last_flush_time >= flush_interval) {
                // 定期刷新数据到磁盘
                flush_data_to_disk();
                last_flush_time = now;
            }
        }
        
        LOG_INFO("数据记录线程结束");
    }
    
    void process_data_packet(std::shared_ptr<DataPacket> packet) {
        try {
            // 根据数据类型进行处理
            if (packet->sensor_data) {
                switch (packet->sensor_data->type) {
                    case SensorType::CAMERA_RGB:
                    case SensorType::CAMERA_DEPTH:
                    case SensorType::CAMERA_SEMANTIC:
                        process_image_data(packet);
                        break;
                    case SensorType::LIDAR:
                        process_pointcloud_data(packet);
                        break;
                    case SensorType::IMU:
                        process_imu_data(packet);
                        break;
                    case SensorType::GPS:
                        process_gps_data(packet);
                        break;
                    default:
                        LOG_WARN("未知的传感器数据类型: {}", 
                                static_cast<int>(packet->sensor_data->type));
                        break;
                }
            } else if (packet->vehicle_state) {
                process_vehicle_state_data(packet);
            }
            
        } catch (const std::exception& e) {
            LOG_ERROR("处理数据包失败: {}", e.what());
        }
    }
    
    void process_image_data(std::shared_ptr<DataPacket> packet) {
        auto image_data = std::static_pointer_cast<ImageData>(packet->sensor_data);
        
        // 数据预处理
        data_processor_->process_image_data(image_data);
        
        // 添加到缓冲区
        image_buffer_->push(*image_data);
        
        // 转换为Arrow格式
        auto arrow_batch = convert_image_to_arrow(image_data, packet);
        if (arrow_batch) {
            // 添加到记录队列
            data_recorder_->add_batch("images", arrow_batch);
        }
    }
    
    void process_pointcloud_data(std::shared_ptr<DataPacket> packet) {
        auto pointcloud_data = std::static_pointer_cast<PointCloudData>(packet->sensor_data);
        
        // 数据预处理
        data_processor_->process_pointcloud_data(pointcloud_data);
        
        // 添加到缓冲区
        pointcloud_buffer_->push(*pointcloud_data);
        
        // 转换为Arrow格式
        auto arrow_batch = convert_pointcloud_to_arrow(pointcloud_data, packet);
        if (arrow_batch) {
            data_recorder_->add_batch("pointclouds", arrow_batch);
        }
    }
    
    void process_imu_data(std::shared_ptr<DataPacket> packet) {
        auto imu_data = std::static_pointer_cast<IMUData>(packet->sensor_data);
        
        // 数据预处理
        data_processor_->process_imu_data(imu_data);
        
        // 添加到缓冲区
        imu_buffer_->push(*imu_data);
        
        // 转换为Arrow格式
        auto arrow_batch = convert_imu_to_arrow(imu_data, packet);
        if (arrow_batch) {
            data_recorder_->add_batch("imu", arrow_batch);
        }
    }
    
    void process_gps_data(std::shared_ptr<DataPacket> packet) {
        auto gps_data = std::static_pointer_cast<GPSData>(packet->sensor_data);
        
        // 数据预处理
        data_processor_->process_gps_data(gps_data);
        
        // 添加到缓冲区
        gps_buffer_->push(*gps_data);
        
        // 转换为Arrow格式
        auto arrow_batch = convert_gps_to_arrow(gps_data, packet);
        if (arrow_batch) {
            data_recorder_->add_batch("gps", arrow_batch);
        }
    }
    
    void process_vehicle_state_data(std::shared_ptr<DataPacket> packet) {
        // 添加到缓冲区
        vehicle_state_buffer_->push(*packet->vehicle_state);
        
        // 转换为Arrow格式
        auto arrow_batch = convert_vehicle_state_to_arrow(packet->vehicle_state, packet);
        if (arrow_batch) {
            data_recorder_->add_batch("vehicle_states", arrow_batch);
        }
    }
    
    std::shared_ptr<arrow::RecordBatch> convert_image_to_arrow(
        std::shared_ptr<ImageData> image_data,
        std::shared_ptr<DataPacket> packet) {
        
        try {
            // 创建Arrow schema
            auto schema = arrow::schema({
                arrow::field("timestamp", arrow::timestamp(arrow::TimeUnit::MICRO)),
                arrow::field("session_id", arrow::utf8()),
                arrow::field("sensor_id", arrow::utf8()),
                arrow::field("frame_id", arrow::uint32()),
                arrow::field("width", arrow::uint32()),
                arrow::field("height", arrow::uint32()),
                arrow::field("channels", arrow::uint32()),
                arrow::field("format", arrow::utf8()),
                arrow::field("data", arrow::binary())
            });
            
            // 创建数据数组
            arrow::TimestampBuilder timestamp_builder(arrow::timestamp(arrow::TimeUnit::MICRO), arrow_pool_);
            arrow::StringBuilder session_id_builder(arrow_pool_);
            arrow::StringBuilder sensor_id_builder(arrow_pool_);
            arrow::UInt32Builder frame_id_builder(arrow_pool_);
            arrow::UInt32Builder width_builder(arrow_pool_);
            arrow::UInt32Builder height_builder(arrow_pool_);
            arrow::UInt32Builder channels_builder(arrow_pool_);
            arrow::StringBuilder format_builder(arrow_pool_);
            arrow::BinaryBuilder data_builder(arrow_pool_);
            
            // 添加数据
            timestamp_builder.Append(packet->timestamp);
            session_id_builder.Append(packet->session_id);
            sensor_id_builder.Append(packet->sensor_id);
            frame_id_builder.Append(image_data->frame_id);
            width_builder.Append(image_data->width);
            height_builder.Append(image_data->height);
            channels_builder.Append(image_data->channels);
            format_builder.Append(image_data->format);
            data_builder.Append(image_data->data.data(), image_data->data.size());
            
            // 完成构建
            std::shared_ptr<arrow::Array> timestamp_array;
            std::shared_ptr<arrow::Array> session_id_array;
            std::shared_ptr<arrow::Array> sensor_id_array;
            std::shared_ptr<arrow::Array> frame_id_array;
            std::shared_ptr<arrow::Array> width_array;
            std::shared_ptr<arrow::Array> height_array;
            std::shared_ptr<arrow::Array> channels_array;
            std::shared_ptr<arrow::Array> format_array;
            std::shared_ptr<arrow::Array> data_array;
            
            timestamp_builder.Finish(&timestamp_array);
            session_id_builder.Finish(&session_id_array);
            sensor_id_builder.Finish(&sensor_id_array);
            frame_id_builder.Finish(&frame_id_array);
            width_builder.Finish(&width_array);
            height_builder.Finish(&height_array);
            channels_builder.Finish(&channels_array);
            format_builder.Finish(&format_array);
            data_builder.Finish(&data_array);
            
            // 创建RecordBatch
            return arrow::RecordBatch::Make(schema, 1, {
                timestamp_array, session_id_array, sensor_id_array,
                frame_id_array, width_array, height_array,
                channels_array, format_array, data_array
            });
            
        } catch (const std::exception& e) {
            LOG_ERROR("转换图像数据到Arrow格式失败: {}", e.what());
            return nullptr;
        }
    }
    
    // 其他数据类型的Arrow转换方法...
    
    void flush_data_to_disk() {
        if (data_recorder_) {
            data_recorder_->flush();
        }
    }
    
    void flush_remaining_data() {
        // 处理队列中剩余的数据
        std::lock_guard<std::mutex> lock(data_mutex_);
        while (!data_queue_.empty()) {
            auto data_packet = data_queue_.front();
            data_queue_.pop();
            process_data_packet(data_packet);
        }
        
        // 刷新到磁盘
        flush_data_to_disk();
    }
    
    void update_collection_stats(const std::string& sensor_id, 
                                std::shared_ptr<SensorData> data) {
        std::lock_guard<std::mutex> lock(stats_mutex_);
        
        // 更新总体统计
        collection_stats_.total_packets++;
        collection_stats_.total_bytes += get_data_size(data);
        
        // 更新传感器统计
        auto& sensor_stat = sensor_stats_[sensor_id];
        sensor_stat.packet_count++;
        sensor_stat.total_bytes += get_data_size(data);
        sensor_stat.last_update_time = std::chrono::steady_clock::now();
    }
    
    size_t get_data_size(std::shared_ptr<SensorData> data) {
        // 根据数据类型计算大小
        switch (data->type) {
            case SensorType::CAMERA_RGB:
            case SensorType::CAMERA_DEPTH:
            case SensorType::CAMERA_SEMANTIC: {
                auto image_data = std::static_pointer_cast<ImageData>(data);
                return image_data->data.size();
            }
            case SensorType::LIDAR: {
                auto pointcloud_data = std::static_pointer_cast<PointCloudData>(data);
                return pointcloud_data->points.size() * sizeof(PointCloudData::Point);
            }
            case SensorType::IMU:
                return sizeof(IMUData);
            case SensorType::GPS:
                return sizeof(GPSData);
            default:
                return 0;
        }
    }
    
    std::string get_data_type_string(SensorType type) {
        switch (type) {
            case SensorType::CAMERA_RGB: return "camera_rgb";
            case SensorType::CAMERA_DEPTH: return "camera_depth";
            case SensorType::CAMERA_SEMANTIC: return "camera_semantic";
            case SensorType::LIDAR: return "lidar";
            case SensorType::IMU: return "imu";
            case SensorType::GPS: return "gps";
            case SensorType::COLLISION: return "collision";
            default: return "unknown";
        }
    }
    
    uint64_t get_current_timestamp_us() {
        auto now = std::chrono::high_resolution_clock::now();
        return std::chrono::duration_cast<std::chrono::microseconds>(
            now.time_since_epoch()).count();
    }

private:
    std::shared_ptr<Config> config_;
    std::unique_ptr<DataProcessor> data_processor_;
    std::unique_ptr<DataRecorder> data_recorder_;
    
    // 采集状态
    std::atomic<bool> is_collecting_;
    std::string current_session_id_;
    
    // 数据队列和线程
    std::queue<std::shared_ptr<DataPacket>> data_queue_;
    std::mutex data_mutex_;
    std::condition_variable data_condition_;
    std::thread processing_thread_;
    std::thread recording_thread_;
    
    // 数据缓冲区
    std::shared_ptr<CircularBuffer<ImageData>> image_buffer_;
    std::shared_ptr<CircularBuffer<PointCloudData>> pointcloud_buffer_;
    std::shared_ptr<CircularBuffer<IMUData>> imu_buffer_;
    std::shared_ptr<CircularBuffer<GPSData>> gps_buffer_;
    std::shared_ptr<CircularBuffer<VehicleState>> vehicle_state_buffer_;
    
    // Arrow内存池
    arrow::MemoryPool* arrow_pool_;
    
    // 统计信息
    mutable std::mutex stats_mutex_;
    CollectionStatistics collection_stats_;
    std::unordered_map<std::string, SensorStatistics> sensor_stats_;
};

// DataCollector实现
DataCollector::DataCollector(std::shared_ptr<Config> config)
    : impl_(std::make_unique<Impl>(config)) {}

DataCollector::~DataCollector() = default;

bool DataCollector::initialize() {
    return impl_->initialize();
}

Status DataCollector::start_collection(const std::string& session_id) {
    return impl_->start_collection(session_id);
}

Status DataCollector::stop_collection() {
    return impl_->stop_collection();
}

Status DataCollector::collect_sensor_data(const std::string& sensor_id, 
                                         std::shared_ptr<SensorData> data) {
    return impl_->collect_sensor_data(sensor_id, data);
}

Status DataCollector::collect_vehicle_state(const std::string& vehicle_id, 
                                           const VehicleState& state) {
    return impl_->collect_vehicle_state(vehicle_id, state);
}

CollectionStatistics DataCollector::get_statistics() const {
    return impl_->get_statistics();
}

std::vector<std::string> DataCollector::get_active_sensors() const {
    return impl_->get_active_sensors();
}

void DataCollector::shutdown() {
    impl_->shutdown();
}

} // namespace simulation_integration
