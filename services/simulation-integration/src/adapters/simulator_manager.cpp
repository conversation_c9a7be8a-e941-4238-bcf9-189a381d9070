// 自动驾驶开发加速系统 - 仿真器管理器
#include "adapters/simulator_manager.h"
#include "adapters/carla_adapter.h"
#include "adapters/airsim_adapter.h"
#include "core/logger.h"

namespace simulation_integration {

class SimulatorManager::Impl {
public:
    Impl(std::shared_ptr<Config> config) : config_(config) {}
    
    bool initialize() {
        try {
            // 注册仿真器工厂
            register_simulator_factories();
            
            // 检查可用的仿真器
            check_available_simulators();
            
            LOG_INFO("仿真器管理器初始化成功");
            LOG_INFO("可用仿真器: {}", get_available_simulators_string());
            
            return true;
            
        } catch (const std::exception& e) {
            LOG_ERROR("仿真器管理器初始化失败: {}", e.what());
            return false;
        }
    }
    
    Status create_simulator(SimulatorType type, const std::string& instance_id) {
        if (simulators_.find(instance_id) != simulators_.end()) {
            return Status::error("仿真器实例已存在: " + instance_id);
        }
        
        auto factory_it = factories_.find(type);
        if (factory_it == factories_.end()) {
            return Status::error("不支持的仿真器类型");
        }
        
        try {
            // 检查依赖
            if (!factory_it->second->check_dependencies()) {
                return Status::error("仿真器依赖检查失败");
            }
            
            // 创建仿真器实例
            auto simulator = factory_it->second->create_simulator();
            if (!simulator) {
                return Status::error("仿真器创建失败");
            }
            
            // 初始化仿真器
            std::string config_file = get_simulator_config_file(type);
            auto status = simulator->initialize(config_file);
            if (!status.is_ok()) {
                return status;
            }
            
            simulators_[instance_id] = std::move(simulator);
            
            LOG_INFO("仿真器实例创建成功: {} ({})", instance_id, 
                    factory_it->second->get_simulator_name());
            
            return Status::ok();
            
        } catch (const std::exception& e) {
            LOG_ERROR("仿真器创建失败: {}", e.what());
            return Status::error(e.what());
        }
    }
    
    Status destroy_simulator(const std::string& instance_id) {
        auto it = simulators_.find(instance_id);
        if (it == simulators_.end()) {
            return Status::error("仿真器实例不存在: " + instance_id);
        }
        
        try {
            // 停止仿真器
            it->second->stop();
            it->second->shutdown();
            
            // 移除实例
            simulators_.erase(it);
            
            LOG_INFO("仿真器实例销毁成功: {}", instance_id);
            return Status::ok();
            
        } catch (const std::exception& e) {
            LOG_ERROR("仿真器销毁失败: {}", e.what());
            return Status::error(e.what());
        }
    }
    
    ISimulator* get_simulator(const std::string& instance_id) {
        auto it = simulators_.find(instance_id);
        return (it != simulators_.end()) ? it->second.get() : nullptr;
    }
    
    std::vector<std::string> get_simulator_instances() const {
        std::vector<std::string> instances;
        for (const auto& [id, simulator] : simulators_) {
            instances.push_back(id);
        }
        return instances;
    }
    
    std::vector<SimulatorType> get_available_simulator_types() const {
        std::vector<SimulatorType> types;
        for (const auto& [type, factory] : factories_) {
            if (factory->check_dependencies()) {
                types.push_back(type);
            }
        }
        return types;
    }
    
    SystemStatus get_system_status() const {
        SystemStatus status;
        status.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::system_clock::now().time_since_epoch()).count();
        
        // 统计仿真器状态
        for (const auto& [id, simulator] : simulators_) {
            SimulatorInstanceStatus instance_status;
            instance_status.instance_id = id;
            instance_status.type = simulator->get_type();
            instance_status.status = simulator->get_status();
            instance_status.version = simulator->get_version();
            instance_status.is_connected = simulator->is_connected();
            instance_status.simulation_time = simulator->get_simulation_time();
            instance_status.frame_count = simulator->get_frame_count();
            
            status.simulators.push_back(instance_status);
        }
        
        // 系统资源状态
        status.cpu_usage = get_cpu_usage();
        status.memory_usage = get_memory_usage();
        status.gpu_usage = get_gpu_usage();
        
        return status;
    }
    
    void update() {
        // 更新所有仿真器实例
        for (auto& [id, simulator] : simulators_) {
            try {
                // 检查连接状态
                if (!simulator->is_connected()) {
                    LOG_WARN("仿真器实例连接丢失: {}", id);
                    continue;
                }
                
                // 如果是同步模式，执行tick
                if (simulator->is_synchronous_mode() && 
                    simulator->get_status() == SimulatorStatus::RUNNING) {
                    simulator->tick();
                }
                
            } catch (const std::exception& e) {
                LOG_ERROR("仿真器实例更新失败 {}: {}", id, e.what());
            }
        }
    }
    
    void shutdown() {
        LOG_INFO("正在关闭仿真器管理器...");
        
        // 关闭所有仿真器实例
        for (auto& [id, simulator] : simulators_) {
            try {
                simulator->stop();
                simulator->shutdown();
                LOG_INFO("仿真器实例已关闭: {}", id);
            } catch (const std::exception& e) {
                LOG_ERROR("关闭仿真器实例失败 {}: {}", id, e.what());
            }
        }
        
        simulators_.clear();
        LOG_INFO("仿真器管理器已关闭");
    }

private:
    void register_simulator_factories() {
        // 注册CARLA工厂
        auto carla_factory = std::make_unique<CarlaSimulatorFactory>();
        factories_[SimulatorType::CARLA] = std::move(carla_factory);
        
        // 注册AirSim工厂
        auto airsim_factory = std::make_unique<AirSimSimulatorFactory>();
        factories_[SimulatorType::AIRSIM] = std::move(airsim_factory);
        
        LOG_INFO("仿真器工厂注册完成");
    }
    
    void check_available_simulators() {
        available_simulators_.clear();
        
        for (const auto& [type, factory] : factories_) {
            if (factory->check_dependencies()) {
                available_simulators_.push_back(type);
                LOG_INFO("仿真器可用: {}", factory->get_simulator_name());
            } else {
                LOG_WARN("仿真器不可用: {} (依赖检查失败)", factory->get_simulator_name());
                
                // 输出缺失的依赖
                auto dependencies = factory->get_required_dependencies();
                for (const auto& dep : dependencies) {
                    LOG_WARN("  缺失依赖: {}", dep);
                }
            }
        }
    }
    
    std::string get_available_simulators_string() const {
        std::string result;
        for (size_t i = 0; i < available_simulators_.size(); ++i) {
            if (i > 0) result += ", ";
            
            auto factory_it = factories_.find(available_simulators_[i]);
            if (factory_it != factories_.end()) {
                result += factory_it->second->get_simulator_name();
            }
        }
        return result;
    }
    
    std::string get_simulator_config_file(SimulatorType type) const {
        switch (type) {
            case SimulatorType::CARLA:
                return config_->get_carla_config_file();
            case SimulatorType::AIRSIM:
                return config_->get_airsim_config_file();
            default:
                return "";
        }
    }
    
    float get_cpu_usage() const {
        // 实现CPU使用率获取
        // 这里返回模拟值
        return 45.6f;
    }
    
    float get_memory_usage() const {
        // 实现内存使用率获取
        // 这里返回模拟值
        return 62.3f;
    }
    
    float get_gpu_usage() const {
        // 实现GPU使用率获取
        // 这里返回模拟值
        return 78.9f;
    }

private:
    std::shared_ptr<Config> config_;
    std::unordered_map<SimulatorType, std::unique_ptr<ISimulatorFactory>> factories_;
    std::unordered_map<std::string, std::unique_ptr<ISimulator>> simulators_;
    std::vector<SimulatorType> available_simulators_;
};

// SimulatorManager实现
SimulatorManager::SimulatorManager(std::shared_ptr<Config> config) 
    : impl_(std::make_unique<Impl>(config)) {}

SimulatorManager::~SimulatorManager() = default;

bool SimulatorManager::initialize() {
    return impl_->initialize();
}

Status SimulatorManager::create_simulator(SimulatorType type, const std::string& instance_id) {
    return impl_->create_simulator(type, instance_id);
}

Status SimulatorManager::destroy_simulator(const std::string& instance_id) {
    return impl_->destroy_simulator(instance_id);
}

ISimulator* SimulatorManager::get_simulator(const std::string& instance_id) {
    return impl_->get_simulator(instance_id);
}

std::vector<std::string> SimulatorManager::get_simulator_instances() const {
    return impl_->get_simulator_instances();
}

std::vector<SimulatorType> SimulatorManager::get_available_simulator_types() const {
    return impl_->get_available_simulator_types();
}

SystemStatus SimulatorManager::get_system_status() const {
    return impl_->get_system_status();
}

void SimulatorManager::update() {
    impl_->update();
}

void SimulatorManager::shutdown() {
    impl_->shutdown();
}

// CARLA仿真器工厂实现
class CarlaSimulatorFactory : public ISimulatorFactory {
public:
    std::unique_ptr<ISimulator> create_simulator() override {
        return std::make_unique<CarlaAdapter>();
    }
    
    SimulatorType get_simulator_type() const override {
        return SimulatorType::CARLA;
    }
    
    std::string get_simulator_name() const override {
        return "CARLA";
    }
    
    std::vector<std::string> get_required_dependencies() const override {
        return {
            "CARLA Server",
            "Python 3.7+",
            "carla Python package"
        };
    }
    
    bool check_dependencies() const override {
        // 检查CARLA依赖
        // 这里简化实现，实际应该检查CARLA服务器是否可用
        return true; // 假设CARLA可用
    }
};

// AirSim仿真器工厂实现
class AirSimSimulatorFactory : public ISimulatorFactory {
public:
    std::unique_ptr<ISimulator> create_simulator() override {
        return std::make_unique<AirSimAdapter>();
    }
    
    SimulatorType get_simulator_type() const override {
        return SimulatorType::AIRSIM;
    }
    
    std::string get_simulator_name() const override {
        return "AirSim";
    }
    
    std::vector<std::string> get_required_dependencies() const override {
        return {
            "AirSim Binary",
            "Unreal Engine 4.27+",
            "AirSim C++ API"
        };
    }
    
    bool check_dependencies() const override {
        // 检查AirSim依赖
        // 这里简化实现，实际应该检查AirSim是否可用
        return true; // 假设AirSim可用
    }
};

} // namespace simulation_integration
