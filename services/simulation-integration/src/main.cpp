// 自动驾驶开发加速系统 - 仿真集成服务主入口
#include <iostream>
#include <memory>
#include <signal.h>
#include <thread>
#include <chrono>

#include "core/application.h"
#include "core/config.h"
#include "core/logger.h"
#include "adapters/simulator_manager.h"
#include "api/grpc_server.h"
#include "api/http_server.h"
#include "dds/dds_publisher.h"
#include "dds/dds_subscriber.h"

using namespace simulation_integration;

// 全局应用实例
std::unique_ptr<Application> g_application;

// 信号处理函数
void signal_handler(int signal) {
    LOG_INFO("收到信号 {}, 正在关闭服务...", signal);
    if (g_application) {
        g_application->shutdown();
    }
}

int main(int argc, char* argv[]) {
    try {
        // 设置信号处理
        signal(SIGINT, signal_handler);
        signal(SIGTERM, signal_handler);
        
        LOG_INFO("🚀 自动驾驶仿真集成服务启动中...");
        
        // 加载配置
        auto config = std::make_shared<Config>();
        if (!config->load_from_file("config/simulation_config.yaml")) {
            LOG_ERROR("配置文件加载失败");
            return -1;
        }
        
        // 创建应用实例
        g_application = std::make_unique<Application>(config);
        
        // 初始化应用
        if (!g_application->initialize()) {
            LOG_ERROR("应用初始化失败");
            return -1;
        }
        
        LOG_INFO("✅ 仿真集成服务启动成功");
        LOG_INFO("📊 服务状态:");
        LOG_INFO("  - gRPC服务端口: {}", config->get_grpc_port());
        LOG_INFO("  - HTTP服务端口: {}", config->get_http_port());
        LOG_INFO("  - DDS域ID: {}", config->get_dds_domain_id());
        LOG_INFO("  - 支持的仿真器: CARLA, AirSim");
        
        // 运行应用
        g_application->run();
        
        LOG_INFO("👋 仿真集成服务已关闭");
        return 0;
        
    } catch (const std::exception& e) {
        LOG_ERROR("应用运行异常: {}", e.what());
        return -1;
    } catch (...) {
        LOG_ERROR("未知异常");
        return -1;
    }
}

// 应用类实现
namespace simulation_integration {

class Application::Impl {
public:
    Impl(std::shared_ptr<Config> config) 
        : config_(config)
        , running_(false) {}
    
    bool initialize() {
        try {
            // 初始化日志系统
            Logger::initialize(config_->get_log_level(), config_->get_log_file());
            
            // 初始化仿真器管理器
            simulator_manager_ = std::make_unique<SimulatorManager>(config_);
            if (!simulator_manager_->initialize()) {
                LOG_ERROR("仿真器管理器初始化失败");
                return false;
            }
            
            // 初始化DDS发布者
            dds_publisher_ = std::make_unique<DDSPublisher>(config_->get_dds_domain_id());
            if (!dds_publisher_->initialize()) {
                LOG_ERROR("DDS发布者初始化失败");
                return false;
            }
            
            // 初始化DDS订阅者
            dds_subscriber_ = std::make_unique<DDSSubscriber>(config_->get_dds_domain_id());
            if (!dds_subscriber_->initialize()) {
                LOG_ERROR("DDS订阅者初始化失败");
                return false;
            }
            
            // 初始化gRPC服务器
            grpc_server_ = std::make_unique<GRPCServer>(
                config_->get_grpc_port(), 
                simulator_manager_.get(),
                dds_publisher_.get()
            );
            if (!grpc_server_->initialize()) {
                LOG_ERROR("gRPC服务器初始化失败");
                return false;
            }
            
            // 初始化HTTP服务器
            http_server_ = std::make_unique<HTTPServer>(
                config_->get_http_port(),
                simulator_manager_.get()
            );
            if (!http_server_->initialize()) {
                LOG_ERROR("HTTP服务器初始化失败");
                return false;
            }
            
            LOG_INFO("✅ 所有组件初始化完成");
            return true;
            
        } catch (const std::exception& e) {
            LOG_ERROR("初始化异常: {}", e.what());
            return false;
        }
    }
    
    void run() {
        running_ = true;
        
        // 启动gRPC服务器
        std::thread grpc_thread([this]() {
            grpc_server_->start();
        });
        
        // 启动HTTP服务器
        std::thread http_thread([this]() {
            http_server_->start();
        });
        
        // 启动DDS订阅者
        std::thread dds_thread([this]() {
            dds_subscriber_->start();
        });
        
        // 主循环
        while (running_) {
            // 更新仿真器状态
            simulator_manager_->update();
            
            // 发布系统状态
            publish_system_status();
            
            // 休眠一段时间
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
        }
        
        // 等待线程结束
        if (grpc_thread.joinable()) {
            grpc_thread.join();
        }
        if (http_thread.joinable()) {
            http_thread.join();
        }
        if (dds_thread.joinable()) {
            dds_thread.join();
        }
    }
    
    void shutdown() {
        LOG_INFO("正在关闭应用...");
        running_ = false;
        
        // 关闭各个组件
        if (http_server_) {
            http_server_->stop();
        }
        if (grpc_server_) {
            grpc_server_->stop();
        }
        if (dds_subscriber_) {
            dds_subscriber_->stop();
        }
        if (dds_publisher_) {
            dds_publisher_->shutdown();
        }
        if (simulator_manager_) {
            simulator_manager_->shutdown();
        }
        
        LOG_INFO("✅ 应用关闭完成");
    }

private:
    void publish_system_status() {
        // 发布系统状态到DDS
        if (dds_publisher_ && simulator_manager_) {
            auto status = simulator_manager_->get_system_status();
            dds_publisher_->publish_system_status(status);
        }
    }

private:
    std::shared_ptr<Config> config_;
    std::unique_ptr<SimulatorManager> simulator_manager_;
    std::unique_ptr<GRPCServer> grpc_server_;
    std::unique_ptr<HTTPServer> http_server_;
    std::unique_ptr<DDSPublisher> dds_publisher_;
    std::unique_ptr<DDSSubscriber> dds_subscriber_;
    std::atomic<bool> running_;
};

// Application类实现
Application::Application(std::shared_ptr<Config> config) 
    : impl_(std::make_unique<Impl>(config)) {}

Application::~Application() = default;

bool Application::initialize() {
    return impl_->initialize();
}

void Application::run() {
    impl_->run();
}

void Application::shutdown() {
    impl_->shutdown();
}

} // namespace simulation_integration
