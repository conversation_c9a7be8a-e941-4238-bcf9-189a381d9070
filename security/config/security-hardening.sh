#!/bin/bash
# 自动驾驶开发加速系统 - 安全加固脚本

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_error "此脚本不应以root用户运行"
        exit 1
    fi
}

# 检查系统环境
check_environment() {
    log_info "检查系统环境..."
    
    # 检查操作系统
    if [[ ! -f /etc/os-release ]]; then
        log_error "无法确定操作系统版本"
        exit 1
    fi
    
    source /etc/os-release
    log_info "操作系统: $PRETTY_NAME"
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装"
        exit 1
    fi
    
    log_success "系统环境检查完成"
}

# 配置防火墙
configure_firewall() {
    log_info "配置防火墙规则..."
    
    # 检查ufw是否安装
    if command -v ufw &> /dev/null; then
        # 重置防火墙规则
        sudo ufw --force reset
        
        # 设置默认策略
        sudo ufw default deny incoming
        sudo ufw default allow outgoing
        
        # 允许SSH
        sudo ufw allow ssh
        
        # 允许HTTP和HTTPS
        sudo ufw allow 80/tcp
        sudo ufw allow 443/tcp
        
        # 允许应用端口
        sudo ufw allow 3000/tcp  # 前端
        sudo ufw allow 8080/tcp  # API网关
        
        # 允许内部服务端口（仅本地）
        sudo ufw allow from 127.0.0.1 to any port 5432  # PostgreSQL
        sudo ufw allow from 127.0.0.1 to any port 6379  # Redis
        sudo ufw allow from 127.0.0.1 to any port 9200  # Elasticsearch
        
        # 启用防火墙
        sudo ufw --force enable
        
        log_success "防火墙配置完成"
    else
        log_warning "ufw未安装，跳过防火墙配置"
    fi
}

# 配置SSL/TLS
configure_ssl() {
    log_info "配置SSL/TLS证书..."
    
    SSL_DIR="./ssl"
    mkdir -p "$SSL_DIR"
    
    # 生成自签名证书（生产环境应使用正式证书）
    if [[ ! -f "$SSL_DIR/server.crt" ]]; then
        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout "$SSL_DIR/server.key" \
            -out "$SSL_DIR/server.crt" \
            -subj "/C=CN/ST=Beijing/L=Beijing/O=AutonomousDriving/CN=localhost"
        
        # 设置证书权限
        chmod 600 "$SSL_DIR/server.key"
        chmod 644 "$SSL_DIR/server.crt"
        
        log_success "SSL证书生成完成"
    else
        log_info "SSL证书已存在"
    fi
}

# 配置Nginx安全
configure_nginx_security() {
    log_info "配置Nginx安全设置..."
    
    NGINX_CONF="./nginx/nginx.conf"
    
    # 创建安全的Nginx配置
    cat > "$NGINX_CONF" << 'EOF'
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # 安全头配置
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self';" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # 隐藏Nginx版本
    server_tokens off;
    
    # 限制请求大小
    client_max_body_size 10M;
    client_body_buffer_size 128k;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
    
    # 超时设置
    client_body_timeout 12;
    client_header_timeout 12;
    keepalive_timeout 15;
    send_timeout 10;
    
    # 限流配置
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;
    
    # 日志格式
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';
    
    access_log /var/log/nginx/access.log main;
    
    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # 上游服务器配置
    upstream frontend {
        server frontend:3000;
    }
    
    upstream api_gateway {
        server api-gateway:8080;
    }
    
    # HTTPS服务器配置
    server {
        listen 443 ssl http2;
        server_name localhost;
        
        ssl_certificate /etc/nginx/ssl/server.crt;
        ssl_certificate_key /etc/nginx/ssl/server.key;
        
        # SSL配置
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;
        
        # 前端路由
        location / {
            proxy_pass http://frontend;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # API路由
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            
            proxy_pass http://api_gateway;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
        
        # 登录接口特殊限流
        location /api/v1/auth/login {
            limit_req zone=login burst=5 nodelay;
            
            proxy_pass http://api_gateway;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
    
    # HTTP重定向到HTTPS
    server {
        listen 80;
        server_name localhost;
        return 301 https://$server_name$request_uri;
    }
}
EOF
    
    log_success "Nginx安全配置完成"
}

# 配置数据库安全
configure_database_security() {
    log_info "配置数据库安全设置..."
    
    # PostgreSQL安全配置
    POSTGRES_CONF="./postgresql/postgresql.conf"
    mkdir -p "$(dirname "$POSTGRES_CONF")"
    
    cat > "$POSTGRES_CONF" << 'EOF'
# PostgreSQL安全配置

# 连接设置
listen_addresses = 'localhost'
port = 5432
max_connections = 100

# SSL设置
ssl = on
ssl_cert_file = '/var/lib/postgresql/server.crt'
ssl_key_file = '/var/lib/postgresql/server.key'

# 认证设置
password_encryption = scram-sha-256

# 日志设置
log_connections = on
log_disconnections = on
log_statement = 'all'
log_line_prefix = '%t [%p]: [%l-1] user=%u,db=%d,app=%a,client=%h '

# 安全设置
shared_preload_libraries = 'pg_stat_statements'
track_activities = on
track_counts = on
track_functions = all
EOF
    
    # Redis安全配置
    REDIS_CONF="./redis/redis.conf"
    mkdir -p "$(dirname "$REDIS_CONF")"
    
    cat > "$REDIS_CONF" << 'EOF'
# Redis安全配置

# 网络设置
bind 127.0.0.1
port 6379
protected-mode yes

# 认证设置
requirepass your_redis_password_here

# 安全设置
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command KEYS ""
rename-command CONFIG "CONFIG_b835c3b4e5f2a3d7c9e1f8a6b2d4e7f9"

# 日志设置
loglevel notice
logfile /var/log/redis/redis-server.log

# 持久化设置
save 900 1
save 300 10
save 60 10000
EOF
    
    log_success "数据库安全配置完成"
}

# 配置应用安全
configure_application_security() {
    log_info "配置应用安全设置..."
    
    # 创建环境变量文件
    ENV_FILE=".env.security"
    
    cat > "$ENV_FILE" << 'EOF'
# 安全环境变量配置

# JWT设置
JWT_SECRET=your_jwt_secret_key_here_should_be_very_long_and_random
JWT_EXPIRES_IN=1h
JWT_REFRESH_EXPIRES_IN=7d

# 数据库密码
POSTGRES_PASSWORD=your_postgres_password_here
REDIS_PASSWORD=your_redis_password_here

# 加密密钥
ENCRYPTION_KEY=your_encryption_key_here_32_chars

# API密钥
API_SECRET_KEY=your_api_secret_key_here

# 会话密钥
SESSION_SECRET=your_session_secret_here

# 邮件配置
SMTP_PASSWORD=your_smtp_password_here

# 第三方服务密钥
GITHUB_CLIENT_SECRET=your_github_client_secret
DINGTALK_SECRET=your_dingtalk_secret

# 监控密钥
PROMETHEUS_PASSWORD=your_prometheus_password
GRAFANA_PASSWORD=your_grafana_password
ELASTICSEARCH_PASSWORD=your_elasticsearch_password
EOF
    
    # 设置文件权限
    chmod 600 "$ENV_FILE"
    
    log_success "应用安全配置完成"
}

# 配置Docker安全
configure_docker_security() {
    log_info "配置Docker安全设置..."
    
    # 创建Docker安全配置
    DOCKER_DAEMON_JSON="/etc/docker/daemon.json"
    
    if [[ -f "$DOCKER_DAEMON_JSON" ]]; then
        sudo cp "$DOCKER_DAEMON_JSON" "$DOCKER_DAEMON_JSON.backup"
    fi
    
    sudo tee "$DOCKER_DAEMON_JSON" > /dev/null << 'EOF'
{
    "icc": false,
    "userland-proxy": false,
    "no-new-privileges": true,
    "seccomp-profile": "/etc/docker/seccomp.json",
    "log-driver": "json-file",
    "log-opts": {
        "max-size": "10m",
        "max-file": "3"
    },
    "live-restore": true,
    "default-ulimits": {
        "nofile": {
            "Name": "nofile",
            "Hard": 64000,
            "Soft": 64000
        }
    }
}
EOF
    
    # 重启Docker服务
    sudo systemctl restart docker
    
    log_success "Docker安全配置完成"
}

# 配置系统安全
configure_system_security() {
    log_info "配置系统安全设置..."
    
    # 配置fail2ban
    if command -v fail2ban-client &> /dev/null; then
        sudo systemctl enable fail2ban
        sudo systemctl start fail2ban
        log_success "fail2ban已启用"
    else
        log_warning "fail2ban未安装，建议安装以防止暴力破解"
    fi
    
    # 配置自动更新
    if command -v unattended-upgrades &> /dev/null; then
        sudo systemctl enable unattended-upgrades
        log_success "自动安全更新已启用"
    else
        log_warning "unattended-upgrades未安装，建议安装以自动应用安全更新"
    fi
    
    # 设置文件权限
    find . -name "*.sh" -exec chmod +x {} \;
    find . -name "*.key" -exec chmod 600 {} \;
    find . -name "*.env*" -exec chmod 600 {} \;
    
    log_success "系统安全配置完成"
}

# 生成安全检查报告
generate_security_report() {
    log_info "生成安全检查报告..."
    
    REPORT_FILE="security-check-report-$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "自动驾驶开发加速系统 - 安全检查报告"
        echo "生成时间: $(date)"
        echo "========================================"
        echo
        
        echo "1. 系统信息"
        echo "操作系统: $(lsb_release -d | cut -f2)"
        echo "内核版本: $(uname -r)"
        echo "Docker版本: $(docker --version)"
        echo
        
        echo "2. 网络安全"
        echo "防火墙状态:"
        if command -v ufw &> /dev/null; then
            sudo ufw status
        else
            echo "ufw未安装"
        fi
        echo
        
        echo "3. 服务状态"
        echo "Docker服务: $(systemctl is-active docker)"
        echo "Nginx服务: $(systemctl is-active nginx 2>/dev/null || echo "未运行")"
        echo
        
        echo "4. 文件权限检查"
        echo "敏感文件权限:"
        find . -name "*.env*" -o -name "*.key" -o -name "*.pem" | xargs ls -la 2>/dev/null || echo "无敏感文件"
        echo
        
        echo "5. 端口监听"
        echo "开放端口:"
        ss -tlnp | grep LISTEN
        echo
        
        echo "6. 安全建议"
        echo "- 定期更新系统和应用程序"
        echo "- 使用强密码和多因素认证"
        echo "- 定期备份重要数据"
        echo "- 监控系统日志和异常活动"
        echo "- 定期进行安全审计"
        
    } > "$REPORT_FILE"
    
    log_success "安全检查报告已生成: $REPORT_FILE"
}

# 主函数
main() {
    log_info "开始自动驾驶开发加速系统安全加固..."
    
    check_root
    check_environment
    configure_firewall
    configure_ssl
    configure_nginx_security
    configure_database_security
    configure_application_security
    configure_docker_security
    configure_system_security
    generate_security_report
    
    log_success "安全加固完成！"
    log_info "请检查生成的配置文件并根据实际环境进行调整"
    log_warning "请务必修改默认密码和密钥！"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
