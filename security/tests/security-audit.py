#!/usr/bin/env python3
# 自动驾驶开发加速系统 - 安全审计脚本

import requests
import json
import time
import subprocess
import sys
import argparse
from urllib.parse import urljoin
from typing import Dict, List, Any
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(f'security-audit-{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SecurityAuditor:
    """安全审计器"""
    
    def __init__(self, base_url: str, auth_token: str = None):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'SecurityAuditor/1.0',
            'Content-Type': 'application/json'
        })
        
        if auth_token:
            self.session.headers.update({
                'Authorization': f'Bearer {auth_token}'
            })
        
        self.vulnerabilities = []
        self.test_results = {
            'sql_injection': [],
            'xss': [],
            'authentication': [],
            'authorization': [],
            'information_disclosure': [],
            'csrf': [],
            'security_headers': [],
            'ssl_tls': []
        }

    def log_vulnerability(self, category: str, severity: str, description: str, details: Dict = None):
        """记录发现的漏洞"""
        vulnerability = {
            'timestamp': datetime.now().isoformat(),
            'category': category,
            'severity': severity,
            'description': description,
            'details': details or {}
        }
        
        self.vulnerabilities.append(vulnerability)
        self.test_results[category].append(vulnerability)
        
        logger.warning(f"[{severity}] {category}: {description}")

    def test_sql_injection(self):
        """SQL注入测试"""
        logger.info("开始SQL注入测试...")
        
        # SQL注入测试载荷
        sql_payloads = [
            "' OR '1'='1",
            "' OR '1'='1' --",
            "' OR '1'='1' /*",
            "'; DROP TABLE users; --",
            "' UNION SELECT NULL, NULL, NULL --",
            "1' AND (SELECT COUNT(*) FROM information_schema.tables) > 0 --",
            "1' AND (SELECT SUBSTRING(@@version,1,1)) = '5' --",
            "' OR 1=1#",
            "' OR 'a'='a",
            "admin'--",
            "admin' /*",
            "' OR 1=1 LIMIT 1 --"
        ]
        
        # 测试端点
        test_endpoints = [
            '/api/v1/auth/login',
            '/api/v1/users',
            '/api/v1/development-tools/projects',
            '/api/v1/simulation/sessions',
            '/api/v1/map-editor/maps'
        ]
        
        for endpoint in test_endpoints:
            for payload in sql_payloads:
                try:
                    # GET参数注入测试
                    response = self.session.get(
                        urljoin(self.base_url, endpoint),
                        params={'id': payload, 'search': payload},
                        timeout=10
                    )
                    
                    if self._check_sql_injection_response(response, payload):
                        self.log_vulnerability(
                            'sql_injection',
                            'HIGH',
                            f'SQL注入漏洞在GET参数中: {endpoint}',
                            {'payload': payload, 'response_code': response.status_code}
                        )
                    
                    # POST数据注入测试
                    if endpoint == '/api/v1/auth/login':
                        login_data = {
                            'username': payload,
                            'password': payload
                        }
                        response = self.session.post(
                            urljoin(self.base_url, endpoint),
                            json=login_data,
                            timeout=10
                        )
                        
                        if self._check_sql_injection_response(response, payload):
                            self.log_vulnerability(
                                'sql_injection',
                                'HIGH',
                                f'SQL注入漏洞在POST数据中: {endpoint}',
                                {'payload': payload, 'response_code': response.status_code}
                            )
                    
                except requests.RequestException as e:
                    logger.debug(f"请求失败: {endpoint} - {e}")
                
                time.sleep(0.1)  # 避免过于频繁的请求

    def _check_sql_injection_response(self, response: requests.Response, payload: str) -> bool:
        """检查响应是否表明存在SQL注入漏洞"""
        # 检查SQL错误信息
        sql_errors = [
            'mysql_fetch_array',
            'ORA-01756',
            'Microsoft OLE DB Provider for ODBC Drivers',
            'PostgreSQL query failed',
            'Warning: mysql_',
            'MySQLSyntaxErrorException',
            'valid MySQL result',
            'PostgreSQL',
            'Warning: pg_',
            'valid PostgreSQL result',
            'Npgsql.',
            'Warning: sqlite_',
            'SQLite/JDBCDriver',
            'SQLiteException',
            'sqlite error',
            'syntax error',
            'MariaDB server version',
            'Column count doesn\'t match'
        ]
        
        response_text = response.text.lower()
        for error in sql_errors:
            if error.lower() in response_text:
                return True
        
        # 检查异常的响应时间（时间盲注）
        if response.elapsed.total_seconds() > 5:
            return True
        
        return False

    def test_xss_vulnerabilities(self):
        """XSS漏洞测试"""
        logger.info("开始XSS漏洞测试...")
        
        xss_payloads = [
            '<script>alert("XSS")</script>',
            '<img src=x onerror=alert("XSS")>',
            '<svg onload=alert("XSS")>',
            'javascript:alert("XSS")',
            '<iframe src="javascript:alert(\'XSS\')"></iframe>',
            '<body onload=alert("XSS")>',
            '<input onfocus=alert("XSS") autofocus>',
            '<select onfocus=alert("XSS") autofocus>',
            '<textarea onfocus=alert("XSS") autofocus>',
            '<keygen onfocus=alert("XSS") autofocus>',
            '<video><source onerror="alert(\'XSS\')">',
            '<audio src=x onerror=alert("XSS")>'
        ]
        
        test_endpoints = [
            '/api/v1/development-tools/projects',
            '/api/v1/simulation/scenarios',
            '/api/v1/map-editor/maps'
        ]
        
        for endpoint in test_endpoints:
            for payload in xss_payloads:
                try:
                    # 测试GET参数
                    response = self.session.get(
                        urljoin(self.base_url, endpoint),
                        params={'search': payload, 'name': payload},
                        timeout=10
                    )
                    
                    if payload in response.text and 'text/html' in response.headers.get('content-type', ''):
                        self.log_vulnerability(
                            'xss',
                            'MEDIUM',
                            f'反射型XSS漏洞: {endpoint}',
                            {'payload': payload, 'response_code': response.status_code}
                        )
                    
                    # 测试POST数据
                    if 'projects' in endpoint:
                        project_data = {
                            'name': payload,
                            'description': payload
                        }
                        response = self.session.post(
                            urljoin(self.base_url, endpoint),
                            json=project_data,
                            timeout=10
                        )
                        
                        if response.status_code == 201:
                            # 检查存储型XSS
                            get_response = self.session.get(urljoin(self.base_url, endpoint))
                            if payload in get_response.text:
                                self.log_vulnerability(
                                    'xss',
                                    'HIGH',
                                    f'存储型XSS漏洞: {endpoint}',
                                    {'payload': payload}
                                )
                
                except requests.RequestException as e:
                    logger.debug(f"XSS测试请求失败: {endpoint} - {e}")
                
                time.sleep(0.1)

    def test_authentication_security(self):
        """身份认证安全测试"""
        logger.info("开始身份认证安全测试...")
        
        # 测试弱密码
        weak_passwords = [
            'password', '123456', 'admin', 'root', 'test',
            'password123', 'admin123', '12345678', 'qwerty',
            'abc123', 'password1', 'welcome', 'login'
        ]
        
        common_usernames = [
            'admin', 'administrator', 'root', 'test', 'guest',
            'user', 'demo', 'sa', 'operator', 'manager'
        ]
        
        login_endpoint = '/api/v1/auth/login'
        
        # 暴力破解测试
        for username in common_usernames:
            for password in weak_passwords:
                try:
                    response = self.session.post(
                        urljoin(self.base_url, login_endpoint),
                        json={'username': username, 'password': password},
                        timeout=10
                    )
                    
                    if response.status_code == 200:
                        self.log_vulnerability(
                            'authentication',
                            'HIGH',
                            f'弱密码账户: {username}/{password}',
                            {'username': username, 'password': password}
                        )
                    
                    # 检查是否有账户锁定机制
                    if response.status_code != 429:  # 429 Too Many Requests
                        continue
                    else:
                        logger.info(f"检测到账户锁定机制: {username}")
                        break
                
                except requests.RequestException as e:
                    logger.debug(f"认证测试请求失败: {e}")
                
                time.sleep(0.5)  # 避免触发限流
        
        # 测试JWT令牌安全
        self._test_jwt_security()

    def _test_jwt_security(self):
        """JWT令牌安全测试"""
        # 测试无效令牌
        invalid_tokens = [
            'invalid_token',
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.invalid.signature',
            '',
            'Bearer invalid'
        ]
        
        for token in invalid_tokens:
            headers = {'Authorization': f'Bearer {token}'}
            try:
                response = self.session.get(
                    urljoin(self.base_url, '/api/v1/users/profile'),
                    headers=headers,
                    timeout=10
                )
                
                if response.status_code == 200:
                    self.log_vulnerability(
                        'authentication',
                        'HIGH',
                        '无效JWT令牌被接受',
                        {'token': token}
                    )
            
            except requests.RequestException as e:
                logger.debug(f"JWT测试请求失败: {e}")

    def test_authorization_bypass(self):
        """权限绕过测试"""
        logger.info("开始权限绕过测试...")
        
        # 测试未授权访问
        protected_endpoints = [
            '/api/v1/users',
            '/api/v1/users/1',
            '/api/v1/development-tools/projects',
            '/api/v1/simulation/sessions',
            '/api/v1/map-editor/maps'
        ]
        
        # 移除认证头进行测试
        original_auth = self.session.headers.get('Authorization')
        if original_auth:
            del self.session.headers['Authorization']
        
        for endpoint in protected_endpoints:
            try:
                response = self.session.get(urljoin(self.base_url, endpoint), timeout=10)
                
                if response.status_code == 200:
                    self.log_vulnerability(
                        'authorization',
                        'HIGH',
                        f'未授权访问: {endpoint}',
                        {'response_code': response.status_code}
                    )
                elif response.status_code != 401 and response.status_code != 403:
                    self.log_vulnerability(
                        'authorization',
                        'MEDIUM',
                        f'异常响应码: {endpoint} - {response.status_code}',
                        {'response_code': response.status_code}
                    )
            
            except requests.RequestException as e:
                logger.debug(f"权限测试请求失败: {endpoint} - {e}")
        
        # 恢复认证头
        if original_auth:
            self.session.headers['Authorization'] = original_auth

    def test_information_disclosure(self):
        """信息泄露测试"""
        logger.info("开始信息泄露测试...")
        
        # 测试敏感文件访问
        sensitive_files = [
            '/.env',
            '/config.json',
            '/package.json',
            '/requirements.txt',
            '/Dockerfile',
            '/docker-compose.yml',
            '/.git/config',
            '/backup.sql',
            '/database.sql',
            '/admin',
            '/debug',
            '/test',
            '/swagger.json',
            '/api/docs',
            '/health',
            '/metrics',
            '/status'
        ]
        
        for file_path in sensitive_files:
            try:
                response = self.session.get(urljoin(self.base_url, file_path), timeout=10)
                
                if response.status_code == 200:
                    self.log_vulnerability(
                        'information_disclosure',
                        'MEDIUM',
                        f'敏感文件可访问: {file_path}',
                        {'response_code': response.status_code, 'content_length': len(response.text)}
                    )
            
            except requests.RequestException as e:
                logger.debug(f"信息泄露测试请求失败: {file_path} - {e}")

    def test_security_headers(self):
        """安全头测试"""
        logger.info("开始安全头测试...")
        
        try:
            response = self.session.get(urljoin(self.base_url, '/'), timeout=10)
            headers = response.headers
            
            # 检查重要的安全头
            security_headers = {
                'X-Content-Type-Options': 'nosniff',
                'X-Frame-Options': ['DENY', 'SAMEORIGIN'],
                'X-XSS-Protection': '1; mode=block',
                'Strict-Transport-Security': None,
                'Content-Security-Policy': None,
                'Referrer-Policy': None
            }
            
            for header, expected_value in security_headers.items():
                if header not in headers:
                    self.log_vulnerability(
                        'security_headers',
                        'MEDIUM',
                        f'缺少安全头: {header}',
                        {'missing_header': header}
                    )
                elif expected_value and isinstance(expected_value, list):
                    if headers[header] not in expected_value:
                        self.log_vulnerability(
                            'security_headers',
                            'LOW',
                            f'安全头配置不当: {header}',
                            {'header': header, 'value': headers[header]}
                        )
        
        except requests.RequestException as e:
            logger.error(f"安全头测试失败: {e}")

    def test_ssl_tls_security(self):
        """SSL/TLS安全测试"""
        logger.info("开始SSL/TLS安全测试...")
        
        if not self.base_url.startswith('https://'):
            self.log_vulnerability(
                'ssl_tls',
                'HIGH',
                '未使用HTTPS加密',
                {'url': self.base_url}
            )
            return
        
        try:
            # 使用testssl.sh进行详细的SSL/TLS测试
            hostname = self.base_url.replace('https://', '').split('/')[0]
            
            # 检查SSL证书
            result = subprocess.run(
                ['openssl', 's_client', '-connect', f'{hostname}:443', '-servername', hostname],
                input='',
                text=True,
                capture_output=True,
                timeout=30
            )
            
            if 'Verify return code: 0 (ok)' not in result.stdout:
                self.log_vulnerability(
                    'ssl_tls',
                    'MEDIUM',
                    'SSL证书验证失败',
                    {'hostname': hostname}
                )
        
        except subprocess.TimeoutExpired:
            logger.warning("SSL/TLS测试超时")
        except FileNotFoundError:
            logger.warning("OpenSSL未安装，跳过SSL/TLS测试")
        except Exception as e:
            logger.error(f"SSL/TLS测试失败: {e}")

    def generate_report(self) -> Dict[str, Any]:
        """生成安全审计报告"""
        total_vulnerabilities = len(self.vulnerabilities)
        severity_counts = {
            'HIGH': len([v for v in self.vulnerabilities if v['severity'] == 'HIGH']),
            'MEDIUM': len([v for v in self.vulnerabilities if v['severity'] == 'MEDIUM']),
            'LOW': len([v for v in self.vulnerabilities if v['severity'] == 'LOW'])
        }
        
        report = {
            'scan_info': {
                'target': self.base_url,
                'timestamp': datetime.now().isoformat(),
                'total_vulnerabilities': total_vulnerabilities,
                'severity_distribution': severity_counts
            },
            'vulnerabilities': self.vulnerabilities,
            'test_results': self.test_results,
            'recommendations': self._generate_recommendations()
        }
        
        return report

    def _generate_recommendations(self) -> List[str]:
        """生成安全建议"""
        recommendations = []
        
        if any(v['category'] == 'sql_injection' for v in self.vulnerabilities):
            recommendations.append("使用参数化查询或ORM防止SQL注入")
        
        if any(v['category'] == 'xss' for v in self.vulnerabilities):
            recommendations.append("对用户输入进行适当的编码和验证")
        
        if any(v['category'] == 'authentication' for v in self.vulnerabilities):
            recommendations.append("实施强密码策略和多因素认证")
        
        if any(v['category'] == 'authorization' for v in self.vulnerabilities):
            recommendations.append("实施适当的访问控制和权限验证")
        
        if any(v['category'] == 'security_headers' for v in self.vulnerabilities):
            recommendations.append("配置适当的HTTP安全头")
        
        if any(v['category'] == 'ssl_tls' for v in self.vulnerabilities):
            recommendations.append("使用HTTPS并配置强SSL/TLS设置")
        
        return recommendations

    def run_full_audit(self):
        """运行完整的安全审计"""
        logger.info("开始完整安全审计...")
        
        try:
            self.test_sql_injection()
            self.test_xss_vulnerabilities()
            self.test_authentication_security()
            self.test_authorization_bypass()
            self.test_information_disclosure()
            self.test_security_headers()
            self.test_ssl_tls_security()
            
            logger.info("安全审计完成")
            
        except Exception as e:
            logger.error(f"安全审计过程中发生错误: {e}")

def main():
    parser = argparse.ArgumentParser(description='自动驾驶开发加速系统安全审计工具')
    parser.add_argument('--url', required=True, help='目标系统URL')
    parser.add_argument('--token', help='认证令牌')
    parser.add_argument('--output', default='security-report.json', help='报告输出文件')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 创建安全审计器
    auditor = SecurityAuditor(args.url, args.token)
    
    # 运行安全审计
    auditor.run_full_audit()
    
    # 生成报告
    report = auditor.generate_report()
    
    # 保存报告
    with open(args.output, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    # 输出摘要
    print(f"\n安全审计完成！")
    print(f"发现漏洞总数: {report['scan_info']['total_vulnerabilities']}")
    print(f"高危漏洞: {report['scan_info']['severity_distribution']['HIGH']}")
    print(f"中危漏洞: {report['scan_info']['severity_distribution']['MEDIUM']}")
    print(f"低危漏洞: {report['scan_info']['severity_distribution']['LOW']}")
    print(f"详细报告已保存到: {args.output}")

if __name__ == '__main__':
    main()
