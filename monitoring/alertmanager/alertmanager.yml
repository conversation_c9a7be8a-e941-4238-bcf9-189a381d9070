# 自动驾驶开发加速系统 - AlertManager配置
global:
  # SMTP配置
  smtp_smarthost: 'smtp.example.com:587'
  smtp_from: '<EMAIL>'
  smtp_auth_username: '<EMAIL>'
  smtp_auth_password: 'your-email-password'
  smtp_require_tls: true

  # 钉钉机器人配置
  dingtalk_api_url: 'https://oapi.dingtalk.com/robot/send'

# 模板配置
templates:
  - '/etc/alertmanager/templates/*.tmpl'

# 路由配置
route:
  group_by: ['alertname', 'cluster', 'service']
  group_wait: 10s
  group_interval: 10s
  repeat_interval: 1h
  receiver: 'default-receiver'
  routes:
    # 严重告警路由
    - match:
        severity: critical
      receiver: 'critical-alerts'
      group_wait: 5s
      group_interval: 5s
      repeat_interval: 30m
      routes:
        # 系统资源严重告警
        - match:
            service: system
        receiver: 'system-critical-alerts'
        # 数据库严重告警
        - match:
            service: postgresql
        receiver: 'database-critical-alerts'
        - match:
            service: redis
        receiver: 'database-critical-alerts'

    # 警告级别告警路由
    - match:
        severity: warning
      receiver: 'warning-alerts'
      group_wait: 30s
      group_interval: 5m
      repeat_interval: 2h

    # 业务告警路由
    - match_re:
        service: 'development-tools|simulation-integration|map-editor|deployment-ops'
      receiver: 'business-alerts'
      group_wait: 15s
      group_interval: 2m
      repeat_interval: 1h

    # 静默测试告警
    - match:
        alertname: 'TestAlert'
      receiver: 'null'

# 抑制规则
inhibit_rules:
  # 如果有严重告警，抑制相同服务的警告告警
  - source_match:
      severity: 'critical'
    target_match:
      severity: 'warning'
    equal: ['alertname', 'service', 'instance']

  # 如果服务下线，抑制该服务的其他告警
  - source_match:
      alertname: 'ServiceDown'
    target_match_re:
      alertname: '.*'
    equal: ['service', 'instance']

# 接收器配置
receivers:
  # 默认接收器
  - name: 'default-receiver'
    email_configs:
      - to: '<EMAIL>'
        subject: '[自动驾驶平台] {{ .GroupLabels.alertname }} 告警'
        body: |
          {{ range .Alerts }}
          告警名称: {{ .Annotations.summary }}
          告警详情: {{ .Annotations.description }}
          告警级别: {{ .Labels.severity }}
          告警服务: {{ .Labels.service }}
          告警实例: {{ .Labels.instance }}
          告警时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ end }}

  # 严重告警接收器
  - name: 'critical-alerts'
    email_configs:
      - to: '<EMAIL>,<EMAIL>'
        subject: '[紧急] {{ .GroupLabels.alertname }} 严重告警'
        body: |
          🚨 严重告警通知 🚨
          
          {{ range .Alerts }}
          告警名称: {{ .Annotations.summary }}
          告警详情: {{ .Annotations.description }}
          告警级别: {{ .Labels.severity }}
          告警服务: {{ .Labels.service }}
          告警实例: {{ .Labels.instance }}
          告警时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          
          请立即处理此告警！
          {{ end }}
        headers:
          Priority: 'high'
    
    webhook_configs:
      - url: 'https://oapi.dingtalk.com/robot/send?access_token=your-dingtalk-token'
        send_resolved: true
        http_config:
          proxy_url: ''
        title: '[紧急] 自动驾驶平台严重告警'
        text: |
          ## 🚨 严重告警通知
          
          {{ range .Alerts }}
          **告警名称**: {{ .Annotations.summary }}
          
          **告警详情**: {{ .Annotations.description }}
          
          **告警级别**: {{ .Labels.severity }}
          
          **告警服务**: {{ .Labels.service }}
          
          **告警实例**: {{ .Labels.instance }}
          
          **告警时间**: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          
          ---
          {{ end }}
          
          请立即处理此告警！

  # 系统严重告警接收器
  - name: 'system-critical-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '[系统紧急] {{ .GroupLabels.alertname }} 系统资源告警'
        body: |
          🔥 系统资源严重告警 🔥
          
          {{ range .Alerts }}
          告警名称: {{ .Annotations.summary }}
          告警详情: {{ .Annotations.description }}
          告警实例: {{ .Labels.instance }}
          告警时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ end }}
          
          系统资源可能即将耗尽，请立即检查！

    webhook_configs:
      - url: 'http://alertmanager-webhook:8080/system-alerts'
        send_resolved: true

  # 数据库严重告警接收器
  - name: 'database-critical-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '[数据库紧急] {{ .GroupLabels.alertname }} 数据库告警'
        body: |
          💾 数据库严重告警 💾
          
          {{ range .Alerts }}
          告警名称: {{ .Annotations.summary }}
          告警详情: {{ .Annotations.description }}
          数据库服务: {{ .Labels.service }}
          告警实例: {{ .Labels.instance }}
          告警时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ end }}
          
          数据库可能出现严重问题，请立即检查！

  # 警告级别告警接收器
  - name: 'warning-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '[警告] {{ .GroupLabels.alertname }} 告警'
        body: |
          ⚠️ 警告级别告警 ⚠️
          
          {{ range .Alerts }}
          告警名称: {{ .Annotations.summary }}
          告警详情: {{ .Annotations.description }}
          告警级别: {{ .Labels.severity }}
          告警服务: {{ .Labels.service }}
          告警实例: {{ .Labels.instance }}
          告警时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ end }}
          
          请关注此告警并及时处理。

  # 业务告警接收器
  - name: 'business-alerts'
    email_configs:
      - to: '<EMAIL>'
        subject: '[业务告警] {{ .GroupLabels.alertname }}'
        body: |
          📊 业务指标告警 📊
          
          {{ range .Alerts }}
          告警名称: {{ .Annotations.summary }}
          告警详情: {{ .Annotations.description }}
          业务服务: {{ .Labels.service }}
          告警时间: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          {{ end }}
          
          业务指标出现异常，请检查相关服务。

    webhook_configs:
      - url: 'https://oapi.dingtalk.com/robot/send?access_token=your-business-dingtalk-token'
        send_resolved: true
        title: '业务告警通知'
        text: |
          ## 📊 业务指标告警
          
          {{ range .Alerts }}
          **告警名称**: {{ .Annotations.summary }}
          
          **告警详情**: {{ .Annotations.description }}
          
          **业务服务**: {{ .Labels.service }}
          
          **告警时间**: {{ .StartsAt.Format "2006-01-02 15:04:05" }}
          
          ---
          {{ end }}

  # 空接收器（用于静默告警）
  - name: 'null'
