{"dashboard": {"id": null, "title": "自动驾驶开发加速系统 - 系统概览", "tags": ["autonomous-driving", "system-overview"], "style": "dark", "timezone": "browser", "refresh": "30s", "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "templating": {"list": [{"name": "instance", "type": "query", "query": "label_values(up, instance)", "refresh": 1, "includeAll": true, "allValue": ".*", "multi": true}, {"name": "service", "type": "query", "query": "label_values(up, job)", "refresh": 1, "includeAll": true, "allValue": ".*", "multi": true}]}, "panels": [{"id": 1, "title": "系统状态概览", "type": "stat", "gridPos": {"h": 8, "w": 24, "x": 0, "y": 0}, "targets": [{"expr": "count(up == 1)", "legendFormat": "在线服务数", "refId": "A"}, {"expr": "count(up == 0)", "legendFormat": "离线服务数", "refId": "B"}, {"expr": "sum(rate(http_requests_total[5m]))", "legendFormat": "总请求率 (req/s)", "refId": "C"}, {"expr": "avg(100 - (avg by(instance) (irate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100))", "legendFormat": "平均CPU使用率 (%)", "refId": "D"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"displayMode": "list", "orientation": "horizontal"}, "mappings": [], "thresholds": {"steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}}}, {"id": 2, "title": "服务可用性", "type": "table", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "targets": [{"expr": "up", "format": "table", "instant": true, "refId": "A"}], "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true, "__name__": true}, "indexByName": {}, "renameByName": {"job": "服务名称", "instance": "实例", "Value": "状态"}}}], "fieldConfig": {"defaults": {"custom": {"displayMode": "color-background"}, "mappings": [{"options": {"0": {"color": "red", "text": "离线"}, "1": {"color": "green", "text": "在线"}}, "type": "value"}]}}}, {"id": 3, "title": "CPU使用率", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "targets": [{"expr": "100 - (avg by(instance) (irate(node_cpu_seconds_total{mode=\"idle\",instance=~\"$instance\"}[5m])) * 100)", "legendFormat": "{{instance}}", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "barAlignment": 0, "lineWidth": 1, "fillOpacity": 10, "gradientMode": "none", "spanNulls": false, "insertNulls": false, "showPoints": "never", "pointSize": 5, "stacking": {"mode": "none", "group": "A"}, "axisPlacement": "auto", "axisLabel": "", "scaleDistribution": {"type": "linear"}, "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent"}}}, {"id": 4, "title": "内存使用率", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "targets": [{"expr": "(1 - (node_memory_MemAvailable_bytes{instance=~\"$instance\"} / node_memory_MemTotal_bytes{instance=~\"$instance\"})) * 100", "legendFormat": "{{instance}}", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "barAlignment": 0, "lineWidth": 1, "fillOpacity": 10, "gradientMode": "none", "spanNulls": false, "insertNulls": false, "showPoints": "never", "pointSize": 5, "stacking": {"mode": "none", "group": "A"}, "axisPlacement": "auto", "axisLabel": "", "scaleDistribution": {"type": "linear"}, "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent"}}}, {"id": 5, "title": "磁盘使用率", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "targets": [{"expr": "(1 - (node_filesystem_avail_bytes{instance=~\"$instance\",fstype!=\"tmpfs\"} / node_filesystem_size_bytes{instance=~\"$instance\",fstype!=\"tmpfs\"})) * 100", "legendFormat": "{{instance}} - {{mountpoint}}", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "barAlignment": 0, "lineWidth": 1, "fillOpacity": 10, "gradientMode": "none", "spanNulls": false, "insertNulls": false, "showPoints": "never", "pointSize": 5, "stacking": {"mode": "none", "group": "A"}, "axisPlacement": "auto", "axisLabel": "", "scaleDistribution": {"type": "linear"}, "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent"}}}, {"id": 6, "title": "网络流量", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}, "targets": [{"expr": "rate(node_network_receive_bytes_total{instance=~\"$instance\",device!=\"lo\"}[5m]) * 8", "legendFormat": "{{instance}} - {{device}} 接收", "refId": "A"}, {"expr": "rate(node_network_transmit_bytes_total{instance=~\"$instance\",device!=\"lo\"}[5m]) * 8", "legendFormat": "{{instance}} - {{device}} 发送", "refId": "B"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "barAlignment": 0, "lineWidth": 1, "fillOpacity": 10, "gradientMode": "none", "spanNulls": false, "insertNulls": false, "showPoints": "never", "pointSize": 5, "stacking": {"mode": "none", "group": "A"}, "axisPlacement": "auto", "axisLabel": "", "scaleDistribution": {"type": "linear"}, "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bps"}}}, {"id": 7, "title": "HTTP请求率", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}, "targets": [{"expr": "sum by(job) (rate(http_requests_total{job=~\"$service\"}[5m]))", "legendFormat": "{{job}}", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "barAlignment": 0, "lineWidth": 1, "fillOpacity": 10, "gradientMode": "none", "spanNulls": false, "insertNulls": false, "showPoints": "never", "pointSize": 5, "stacking": {"mode": "none", "group": "A"}, "axisPlacement": "auto", "axisLabel": "", "scaleDistribution": {"type": "linear"}, "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "reqps"}}}, {"id": 8, "title": "HTTP错误率", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 0, "y": 32}, "targets": [{"expr": "sum by(job) (rate(http_requests_total{job=~\"$service\",status=~\"5..\"}[5m])) / sum by(job) (rate(http_requests_total{job=~\"$service\"}[5m])) * 100", "legendFormat": "{{job}} 5xx错误率", "refId": "A"}, {"expr": "sum by(job) (rate(http_requests_total{job=~\"$service\",status=~\"4..\"}[5m])) / sum by(job) (rate(http_requests_total{job=~\"$service\"}[5m])) * 100", "legendFormat": "{{job}} 4xx错误率", "refId": "B"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "barAlignment": 0, "lineWidth": 1, "fillOpacity": 10, "gradientMode": "none", "spanNulls": false, "insertNulls": false, "showPoints": "never", "pointSize": 5, "stacking": {"mode": "none", "group": "A"}, "axisPlacement": "auto", "axisLabel": "", "scaleDistribution": {"type": "linear"}, "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 5}, {"color": "red", "value": 20}]}, "unit": "percent"}}}, {"id": 9, "title": "响应时间分布", "type": "timeseries", "gridPos": {"h": 8, "w": 12, "x": 12, "y": 32}, "targets": [{"expr": "histogram_quantile(0.50, sum by(job, le) (rate(http_request_duration_seconds_bucket{job=~\"$service\"}[5m])))", "legendFormat": "{{job}} P50", "refId": "A"}, {"expr": "histogram_quantile(0.95, sum by(job, le) (rate(http_request_duration_seconds_bucket{job=~\"$service\"}[5m])))", "legendFormat": "{{job}} P95", "refId": "B"}, {"expr": "histogram_quantile(0.99, sum by(job, le) (rate(http_request_duration_seconds_bucket{job=~\"$service\"}[5m])))", "legendFormat": "{{job}} P99", "refId": "C"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "barAlignment": 0, "lineWidth": 1, "fillOpacity": 10, "gradientMode": "none", "spanNulls": false, "insertNulls": false, "showPoints": "never", "pointSize": 5, "stacking": {"mode": "none", "group": "A"}, "axisPlacement": "auto", "axisLabel": "", "scaleDistribution": {"type": "linear"}, "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "yellow", "value": 1}, {"color": "red", "value": 5}]}, "unit": "s"}}}]}}