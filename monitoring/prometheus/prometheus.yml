# 自动驾驶开发加速系统 - Prometheus监控配置
global:
  scrape_interval: 15s # 全局抓取间隔
  evaluation_interval: 15s # 规则评估间隔
  external_labels:
    cluster: 'autonomous-driving-platform'
    environment: 'production'

# 告警管理器配置
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# 告警规则文件
rule_files:
  - "rules/*.yml"

# 抓取配置
scrape_configs:
  # Prometheus自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 5s
    metrics_path: /metrics

  # 系统管理服务监控
  - job_name: 'system-management'
    static_configs:
      - targets: 
        - 'system-management:8080'
    scrape_interval: 10s
    metrics_path: /metrics
    scrape_timeout: 5s
    honor_labels: true
    params:
      format: ['prometheus']
    relabel_configs:
      - source_labels: [__address__]
        target_label: __param_target
      - source_labels: [__param_target]
        target_label: instance
      - target_label: __address__
        replacement: system-management:8080

  # 开发工具服务监控
  - job_name: 'development-tools'
    static_configs:
      - targets:
        - 'development-tools:8081'
    scrape_interval: 15s
    metrics_path: /metrics
    honor_labels: true
    metric_relabel_configs:
      - source_labels: [__name__]
        regex: 'development_tools_(.*)'
        target_label: service
        replacement: 'development-tools'

  # 仿真集成服务监控
  - job_name: 'simulation-integration'
    static_configs:
      - targets:
        - 'simulation-integration:8082'
    scrape_interval: 10s
    metrics_path: /metrics
    honor_labels: true
    metric_relabel_configs:
      - source_labels: [__name__]
        regex: 'simulation_(.*)'
        target_label: service
        replacement: 'simulation-integration'

  # 地图编辑服务监控
  - job_name: 'map-editor'
    static_configs:
      - targets:
        - 'map-editor:8083'
    scrape_interval: 15s
    metrics_path: /metrics
    honor_labels: true
    metric_relabel_configs:
      - source_labels: [__name__]
        regex: 'map_editor_(.*)'
        target_label: service
        replacement: 'map-editor'

  # 部署运维服务监控
  - job_name: 'deployment-ops'
    static_configs:
      - targets:
        - 'deployment-ops:8084'
    scrape_interval: 15s
    metrics_path: /metrics
    honor_labels: true
    metric_relabel_configs:
      - source_labels: [__name__]
        regex: 'deployment_ops_(.*)'
        target_label: service
        replacement: 'deployment-ops'

  # 前端应用监控（通过nginx-prometheus-exporter）
  - job_name: 'frontend-nginx'
    static_configs:
      - targets:
        - 'nginx-exporter:9113'
    scrape_interval: 30s
    metrics_path: /metrics

  # 数据库监控
  - job_name: 'postgresql'
    static_configs:
      - targets:
        - 'postgres-exporter:9187'
    scrape_interval: 30s
    metrics_path: /metrics
    relabel_configs:
      - source_labels: [__address__]
        target_label: instance
        regex: '(.*):(.*)'
        replacement: '${1}'

  # Redis监控
  - job_name: 'redis'
    static_configs:
      - targets:
        - 'redis-exporter:9121'
    scrape_interval: 30s
    metrics_path: /metrics

  # Node Exporter监控（系统指标）
  - job_name: 'node-exporter'
    static_configs:
      - targets:
        - 'node-exporter:9100'
    scrape_interval: 30s
    metrics_path: /metrics
    relabel_configs:
      - source_labels: [__address__]
        regex: '([^:]+):(.*)'
        target_label: instance
        replacement: '${1}'

  # cAdvisor监控（容器指标）
  - job_name: 'cadvisor'
    static_configs:
      - targets:
        - 'cadvisor:8080'
    scrape_interval: 30s
    metrics_path: /metrics
    honor_labels: true

  # Kubernetes集群监控
  - job_name: 'kubernetes-apiservers'
    kubernetes_sd_configs:
      - role: endpoints
    scheme: https
    tls_config:
      ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
    bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
    relabel_configs:
      - source_labels: [__meta_kubernetes_namespace, __meta_kubernetes_service_name, __meta_kubernetes_endpoint_port_name]
        action: keep
        regex: default;kubernetes;https

  # Kubernetes节点监控
  - job_name: 'kubernetes-nodes'
    kubernetes_sd_configs:
      - role: node
    scheme: https
    tls_config:
      ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
    bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
    relabel_configs:
      - action: labelmap
        regex: __meta_kubernetes_node_label_(.+)
      - target_label: __address__
        replacement: kubernetes.default.svc:443
      - source_labels: [__meta_kubernetes_node_name]
        regex: (.+)
        target_label: __metrics_path__
        replacement: /api/v1/nodes/${1}/proxy/metrics

  # Kubernetes Pod监控
  - job_name: 'kubernetes-pods'
    kubernetes_sd_configs:
      - role: pod
    relabel_configs:
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
        action: keep
        regex: true
      - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
        action: replace
        target_label: __metrics_path__
        regex: (.+)
      - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
        action: replace
        regex: ([^:]+)(?::\d+)?;(\d+)
        replacement: $1:$2
        target_label: __address__
      - action: labelmap
        regex: __meta_kubernetes_pod_label_(.+)
      - source_labels: [__meta_kubernetes_namespace]
        action: replace
        target_label: kubernetes_namespace
      - source_labels: [__meta_kubernetes_pod_name]
        action: replace
        target_label: kubernetes_pod_name

  # Harbor镜像仓库监控
  - job_name: 'harbor'
    static_configs:
      - targets:
        - 'harbor-core:8080'
    scrape_interval: 60s
    metrics_path: /api/v2.0/metrics
    basic_auth:
      username: 'admin'
      password: 'Harbor12345'

  # GitLab监控
  - job_name: 'gitlab'
    static_configs:
      - targets:
        - 'gitlab:9090'
    scrape_interval: 60s
    metrics_path: /-/metrics
    bearer_token: 'gitlab-prometheus-token'

  # Vault监控
  - job_name: 'vault'
    static_configs:
      - targets:
        - 'vault:8200'
    scrape_interval: 30s
    metrics_path: /v1/sys/metrics
    params:
      format: ['prometheus']
    bearer_token: 'vault-prometheus-token'

  # 自定义业务指标监控
  - job_name: 'business-metrics'
    static_configs:
      - targets:
        - 'business-metrics-exporter:9090'
    scrape_interval: 30s
    metrics_path: /metrics
    honor_labels: true

# 远程写入配置（可选，用于长期存储）
remote_write:
  - url: "http://thanos-receive:19291/api/v1/receive"
    queue_config:
      max_samples_per_send: 1000
      max_shards: 200
      capacity: 2500

# 远程读取配置（可选，用于查询历史数据）
remote_read:
  - url: "http://thanos-query:9090/api/v1/query"
    read_recent: true
