# 自动驾驶开发加速系统 - 系统告警规则
groups:
  # 系统资源告警
  - name: system-resources
    rules:
      # CPU使用率告警
      - alert: HighCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 80
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "服务器CPU使用率过高"
          description: "实例 {{ $labels.instance }} 的CPU使用率已超过80%，当前值为 {{ $value }}%"

      - alert: CriticalCPUUsage
        expr: 100 - (avg by(instance) (irate(node_cpu_seconds_total{mode="idle"}[5m])) * 100) > 95
        for: 2m
        labels:
          severity: critical
          service: system
        annotations:
          summary: "服务器CPU使用率严重过高"
          description: "实例 {{ $labels.instance }} 的CPU使用率已超过95%，当前值为 {{ $value }}%，系统可能出现性能问题"

      # 内存使用率告警
      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 80
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "服务器内存使用率过高"
          description: "实例 {{ $labels.instance }} 的内存使用率已超过80%，当前值为 {{ $value }}%"

      - alert: CriticalMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) * 100 > 95
        for: 2m
        labels:
          severity: critical
          service: system
        annotations:
          summary: "服务器内存使用率严重过高"
          description: "实例 {{ $labels.instance }} 的内存使用率已超过95%，当前值为 {{ $value }}%，可能导致系统不稳定"

      # 磁盘使用率告警
      - alert: HighDiskUsage
        expr: (1 - (node_filesystem_avail_bytes{fstype!="tmpfs"} / node_filesystem_size_bytes{fstype!="tmpfs"})) * 100 > 80
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "磁盘使用率过高"
          description: "实例 {{ $labels.instance }} 的磁盘 {{ $labels.mountpoint }} 使用率已超过80%，当前值为 {{ $value }}%"

      - alert: CriticalDiskUsage
        expr: (1 - (node_filesystem_avail_bytes{fstype!="tmpfs"} / node_filesystem_size_bytes{fstype!="tmpfs"})) * 100 > 95
        for: 2m
        labels:
          severity: critical
          service: system
        annotations:
          summary: "磁盘使用率严重过高"
          description: "实例 {{ $labels.instance }} 的磁盘 {{ $labels.mountpoint }} 使用率已超过95%，当前值为 {{ $value }}%，请立即清理磁盘空间"

      # 磁盘IO告警
      - alert: HighDiskIOWait
        expr: irate(node_cpu_seconds_total{mode="iowait"}[5m]) * 100 > 20
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "磁盘IO等待时间过高"
          description: "实例 {{ $labels.instance }} 的磁盘IO等待时间已超过20%，当前值为 {{ $value }}%"

      # 网络连接数告警
      - alert: HighNetworkConnections
        expr: node_netstat_Tcp_CurrEstab > 10000
        for: 5m
        labels:
          severity: warning
          service: system
        annotations:
          summary: "TCP连接数过高"
          description: "实例 {{ $labels.instance }} 的TCP连接数已超过10000，当前值为 {{ $value }}"

  # 服务可用性告警
  - name: service-availability
    rules:
      # 服务下线告警
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
          service: "{{ $labels.job }}"
        annotations:
          summary: "服务不可用"
          description: "服务 {{ $labels.job }} 在实例 {{ $labels.instance }} 上已下线超过1分钟"

      # HTTP错误率告警
      - alert: HighHTTPErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) * 100 > 5
        for: 5m
        labels:
          severity: warning
          service: "{{ $labels.service }}"
        annotations:
          summary: "HTTP错误率过高"
          description: "服务 {{ $labels.service }} 的HTTP 5xx错误率已超过5%，当前值为 {{ $value }}%"

      - alert: CriticalHTTPErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m]) * 100 > 20
        for: 2m
        labels:
          severity: critical
          service: "{{ $labels.service }}"
        annotations:
          summary: "HTTP错误率严重过高"
          description: "服务 {{ $labels.service }} 的HTTP 5xx错误率已超过20%，当前值为 {{ $value }}%"

      # 响应时间告警
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
          service: "{{ $labels.service }}"
        annotations:
          summary: "响应时间过长"
          description: "服务 {{ $labels.service }} 的95%响应时间已超过2秒，当前值为 {{ $value }}秒"

      - alert: CriticalResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 5
        for: 2m
        labels:
          severity: critical
          service: "{{ $labels.service }}"
        annotations:
          summary: "响应时间严重过长"
          description: "服务 {{ $labels.service }} 的95%响应时间已超过5秒，当前值为 {{ $value }}秒"

  # 数据库告警
  - name: database-alerts
    rules:
      # PostgreSQL连接数告警
      - alert: PostgreSQLHighConnections
        expr: pg_stat_database_numbackends / pg_settings_max_connections * 100 > 80
        for: 5m
        labels:
          severity: warning
          service: postgresql
        annotations:
          summary: "PostgreSQL连接数过高"
          description: "PostgreSQL数据库连接数使用率已超过80%，当前值为 {{ $value }}%"

      # PostgreSQL慢查询告警
      - alert: PostgreSQLSlowQueries
        expr: rate(pg_stat_database_tup_returned[5m]) / rate(pg_stat_database_tup_fetched[5m]) < 0.1
        for: 10m
        labels:
          severity: warning
          service: postgresql
        annotations:
          summary: "PostgreSQL存在慢查询"
          description: "PostgreSQL数据库 {{ $labels.datname }} 的查询效率较低，可能存在慢查询"

      # Redis内存使用告警
      - alert: RedisHighMemoryUsage
        expr: redis_memory_used_bytes / redis_memory_max_bytes * 100 > 80
        for: 5m
        labels:
          severity: warning
          service: redis
        annotations:
          summary: "Redis内存使用率过高"
          description: "Redis实例 {{ $labels.instance }} 的内存使用率已超过80%，当前值为 {{ $value }}%"

      # Redis连接数告警
      - alert: RedisHighConnections
        expr: redis_connected_clients > 1000
        for: 5m
        labels:
          severity: warning
          service: redis
        annotations:
          summary: "Redis连接数过高"
          description: "Redis实例 {{ $labels.instance }} 的连接数已超过1000，当前值为 {{ $value }}"

  # 容器告警
  - name: container-alerts
    rules:
      # 容器重启告警
      - alert: ContainerRestarting
        expr: rate(kube_pod_container_status_restarts_total[15m]) > 0
        for: 5m
        labels:
          severity: warning
          service: kubernetes
        annotations:
          summary: "容器频繁重启"
          description: "Pod {{ $labels.pod }} 中的容器 {{ $labels.container }} 在过去15分钟内重启了 {{ $value }} 次"

      # 容器内存使用告警
      - alert: ContainerHighMemoryUsage
        expr: container_memory_usage_bytes / container_spec_memory_limit_bytes * 100 > 80
        for: 5m
        labels:
          severity: warning
          service: kubernetes
        annotations:
          summary: "容器内存使用率过高"
          description: "容器 {{ $labels.name }} 的内存使用率已超过80%，当前值为 {{ $value }}%"

      # 容器CPU使用告警
      - alert: ContainerHighCPUUsage
        expr: rate(container_cpu_usage_seconds_total[5m]) * 100 > 80
        for: 5m
        labels:
          severity: warning
          service: kubernetes
        annotations:
          summary: "容器CPU使用率过高"
          description: "容器 {{ $labels.name }} 的CPU使用率已超过80%，当前值为 {{ $value }}%"

  # 业务指标告警
  - name: business-alerts
    rules:
      # 项目构建失败率告警
      - alert: HighBuildFailureRate
        expr: rate(development_tools_build_failures_total[10m]) / rate(development_tools_builds_total[10m]) * 100 > 20
        for: 5m
        labels:
          severity: warning
          service: development-tools
        annotations:
          summary: "项目构建失败率过高"
          description: "开发工具服务的项目构建失败率已超过20%，当前值为 {{ $value }}%"

      # 仿真会话异常告警
      - alert: SimulationSessionErrors
        expr: rate(simulation_session_errors_total[5m]) > 0.1
        for: 5m
        labels:
          severity: warning
          service: simulation-integration
        annotations:
          summary: "仿真会话出现异常"
          description: "仿真集成服务的会话错误率为 {{ $value }} 错误/秒"

      # 地图编辑冲突告警
      - alert: MapEditingConflicts
        expr: rate(map_editor_conflicts_total[10m]) > 0.05
        for: 5m
        labels:
          severity: warning
          service: map-editor
        annotations:
          summary: "地图编辑冲突频繁"
          description: "地图编辑服务的冲突率为 {{ $value }} 冲突/秒，可能影响协作编辑"

      # 用户登录失败告警
      - alert: HighLoginFailureRate
        expr: rate(auth_login_failures_total[5m]) / rate(auth_login_attempts_total[5m]) * 100 > 30
        for: 5m
        labels:
          severity: warning
          service: system-management
        annotations:
          summary: "用户登录失败率过高"
          description: "系统管理服务的用户登录失败率已超过30%，当前值为 {{ $value }}%，可能存在安全风险"
