#!/bin/bash

# 自动驾驶开发加速系统构建脚本
# 
# 本脚本用于构建整个系统的所有组件
# 包括前端应用、后端服务和Docker镜像

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[信息]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[成功]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[警告]${NC} $1"
}

log_error() {
    echo -e "${RED}[错误]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "命令 $1 未找到，请先安装"
        exit 1
    fi
}

# 检查必要的工具
check_dependencies() {
    log_info "检查构建依赖..."
    
    check_command "node"
    check_command "npm"
    check_command "go"
    check_command "python3"
    check_command "cargo"
    check_command "docker"
    
    log_success "所有依赖检查通过"
}

# 构建前端应用
build_frontend() {
    log_info "开始构建前端应用..."
    
    cd frontend
    
    # 安装依赖
    log_info "安装前端依赖..."
    npm ci
    
    # 类型检查
    log_info "执行TypeScript类型检查..."
    npm run type-check
    
    # 代码检查
    log_info "执行ESLint代码检查..."
    npm run lint
    
    # 运行测试
    if [ "$SKIP_TESTS" != "true" ]; then
        log_info "运行前端测试..."
        npm run test
    fi
    
    # 构建生产版本
    log_info "构建前端生产版本..."
    npm run build
    
    cd ..
    log_success "前端应用构建完成"
}

# 构建系统管理服务 (Go)
build_system_management() {
    log_info "开始构建系统管理服务..."
    
    cd services/system-management
    
    # 下载依赖
    log_info "下载Go模块依赖..."
    go mod download
    
    # 代码检查
    log_info "执行Go代码检查..."
    go vet ./...
    go fmt ./...
    
    # 运行测试
    if [ "$SKIP_TESTS" != "true" ]; then
        log_info "运行Go测试..."
        go test -v ./...
    fi
    
    # 构建二进制文件
    log_info "构建Go二进制文件..."
    CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o bin/system-management .
    
    cd ../..
    log_success "系统管理服务构建完成"
}

# 构建开发工具服务 (Python)
build_development_tools() {
    log_info "开始构建开发工具服务..."
    
    cd services/development-tools
    
    # 创建虚拟环境
    if [ ! -d "venv" ]; then
        log_info "创建Python虚拟环境..."
        python3 -m venv venv
    fi
    
    # 激活虚拟环境
    source venv/bin/activate
    
    # 安装依赖
    log_info "安装Python依赖..."
    pip install -r requirements.txt
    
    # 代码检查
    log_info "执行Python代码检查..."
    flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
    black --check .
    
    # 运行测试
    if [ "$SKIP_TESTS" != "true" ]; then
        log_info "运行Python测试..."
        pytest
    fi
    
    # 停用虚拟环境
    deactivate
    
    cd ../..
    log_success "开发工具服务构建完成"
}

# 构建仿真集成服务 (C++)
build_simulation_integration() {
    log_info "开始构建仿真集成服务..."
    
    cd services/simulation-integration
    
    # 创建构建目录
    mkdir -p build
    cd build
    
    # CMake配置
    log_info "配置CMake..."
    cmake -DCMAKE_BUILD_TYPE=Release ..
    
    # 编译
    log_info "编译C++代码..."
    make -j$(nproc)
    
    # 运行测试
    if [ "$SKIP_TESTS" != "true" ]; then
        log_info "运行C++测试..."
        make test
    fi
    
    cd ../../..
    log_success "仿真集成服务构建完成"
}

# 构建地图编辑服务 (Rust)
build_map_editor() {
    log_info "开始构建地图编辑服务..."
    
    cd services/map-editor
    
    # 构建Rust项目
    log_info "构建Rust项目..."
    cargo build --release
    
    # 代码检查
    log_info "执行Rust代码检查..."
    cargo clippy -- -D warnings
    cargo fmt --check
    
    # 运行测试
    if [ "$SKIP_TESTS" != "true" ]; then
        log_info "运行Rust测试..."
        cargo test
    fi
    
    cd ../..
    log_success "地图编辑服务构建完成"
}

# 构建部署运维服务 (Node.js)
build_devops() {
    log_info "开始构建部署运维服务..."
    
    cd services/devops
    
    # 安装依赖
    log_info "安装Node.js依赖..."
    npm ci
    
    # 代码检查
    log_info "执行Node.js代码检查..."
    npm run lint
    
    # 运行测试
    if [ "$SKIP_TESTS" != "true" ]; then
        log_info "运行Node.js测试..."
        npm test
    fi
    
    # 构建
    log_info "构建Node.js应用..."
    npm run build
    
    cd ../..
    log_success "部署运维服务构建完成"
}

# 构建Docker镜像
build_docker_images() {
    log_info "开始构建Docker镜像..."
    
    # 构建前端镜像
    log_info "构建前端Docker镜像..."
    docker build -t autodriving/frontend:latest -f deployment/docker/Dockerfile.frontend .
    
    # 构建系统管理服务镜像
    log_info "构建系统管理服务Docker镜像..."
    docker build -t autodriving/system-management:latest -f deployment/docker/Dockerfile.system-management .
    
    # 构建开发工具服务镜像
    log_info "构建开发工具服务Docker镜像..."
    docker build -t autodriving/development-tools:latest -f deployment/docker/Dockerfile.development-tools .
    
    # 构建仿真集成服务镜像
    log_info "构建仿真集成服务Docker镜像..."
    docker build -t autodriving/simulation-integration:latest -f deployment/docker/Dockerfile.simulation-integration .
    
    # 构建地图编辑服务镜像
    log_info "构建地图编辑服务Docker镜像..."
    docker build -t autodriving/map-editor:latest -f deployment/docker/Dockerfile.map-editor .
    
    # 构建部署运维服务镜像
    log_info "构建部署运维服务Docker镜像..."
    docker build -t autodriving/devops:latest -f deployment/docker/Dockerfile.devops .
    
    log_success "Docker镜像构建完成"
}

# 清理构建产物
clean() {
    log_info "清理构建产物..."
    
    # 清理前端
    rm -rf frontend/dist
    rm -rf frontend/node_modules
    
    # 清理Go服务
    rm -rf services/system-management/bin
    
    # 清理Python服务
    rm -rf services/development-tools/venv
    rm -rf services/development-tools/__pycache__
    
    # 清理C++服务
    rm -rf services/simulation-integration/build
    
    # 清理Rust服务
    cd services/map-editor && cargo clean && cd ../..
    
    # 清理Node.js服务
    rm -rf services/devops/dist
    rm -rf services/devops/node_modules
    
    log_success "清理完成"
}

# 显示帮助信息
show_help() {
    echo "自动驾驶开发加速系统构建脚本"
    echo ""
    echo "用法: $0 [选项] [目标]"
    echo ""
    echo "目标:"
    echo "  all                构建所有组件（默认）"
    echo "  frontend           只构建前端应用"
    echo "  backend            只构建所有后端服务"
    echo "  system-management  只构建系统管理服务"
    echo "  development-tools  只构建开发工具服务"
    echo "  simulation         只构建仿真集成服务"
    echo "  map-editor         只构建地图编辑服务"
    echo "  devops             只构建部署运维服务"
    echo "  docker             只构建Docker镜像"
    echo "  clean              清理构建产物"
    echo ""
    echo "选项:"
    echo "  --skip-tests       跳过测试"
    echo "  --help, -h         显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                 构建所有组件"
    echo "  $0 frontend        只构建前端"
    echo "  $0 --skip-tests    构建所有组件但跳过测试"
}

# 主函数
main() {
    local target="all"
    
    # 解析命令行参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-tests)
                export SKIP_TESTS="true"
                shift
                ;;
            --help|-h)
                show_help
                exit 0
                ;;
            clean|frontend|backend|system-management|development-tools|simulation|map-editor|devops|docker|all)
                target="$1"
                shift
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    log_info "开始构建自动驾驶开发加速系统..."
    log_info "构建目标: $target"
    
    if [ "$SKIP_TESTS" = "true" ]; then
        log_warning "跳过测试"
    fi
    
    # 检查依赖
    if [ "$target" != "clean" ]; then
        check_dependencies
    fi
    
    # 执行构建
    case $target in
        clean)
            clean
            ;;
        frontend)
            build_frontend
            ;;
        backend)
            build_system_management
            build_development_tools
            build_simulation_integration
            build_map_editor
            build_devops
            ;;
        system-management)
            build_system_management
            ;;
        development-tools)
            build_development_tools
            ;;
        simulation)
            build_simulation_integration
            ;;
        map-editor)
            build_map_editor
            ;;
        devops)
            build_devops
            ;;
        docker)
            build_docker_images
            ;;
        all)
            build_frontend
            build_system_management
            build_development_tools
            build_simulation_integration
            build_map_editor
            build_devops
            if command -v docker &> /dev/null; then
                build_docker_images
            else
                log_warning "Docker未安装，跳过镜像构建"
            fi
            ;;
    esac
    
    log_success "构建完成！"
}

# 执行主函数
main "$@"
