# 自动驾驶开发加速系统 - GitLab CI/CD 配置
# 支持多语言项目的持续集成和部署流水线

# 定义流水线阶段
stages:
  - validate      # 代码验证阶段
  - build        # 构建阶段
  - test         # 测试阶段
  - security     # 安全检查阶段
  - package      # 打包阶段
  - deploy       # 部署阶段

# 全局变量定义
variables:
  # Docker相关配置
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: "/certs"
  
  # 项目版本信息
  PROJECT_VERSION: "1.0.0"
  
  # 构建配置
  GO_VERSION: "1.21"
  NODE_VERSION: "18"
  PYTHON_VERSION: "3.11"
  RUST_VERSION: "1.75"

# 代码质量检查 - ESLint
lint:frontend:
  stage: validate
  image: node:${NODE_VERSION}-alpine
  before_script:
    - cd frontend
    - npm ci
  script:
    - npm run lint
    - npm run type-check
  artifacts:
    reports:
      junit: frontend/test-results/lint-results.xml
  only:
    changes:
      - frontend/**/*
      - "*.json"
      - "*.js"
      - "*.ts"
      - "*.tsx"

# Go代码质量检查
lint:go:
  stage: validate
  image: golang:${GO_VERSION}-alpine
  before_script:
    - apk add --no-cache git
    - go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
  script:
    - golangci-lint run ./services/system-management/...
  artifacts:
    reports:
      junit: go-lint-report.xml
  only:
    changes:
      - services/system-management/**/*
      - "go.mod"
      - "go.sum"

# Python代码质量检查
lint:python:
  stage: validate
  image: python:${PYTHON_VERSION}-slim
  before_script:
    - pip install flake8 black isort mypy
  script:
    - flake8 services/development-tools/
    - black --check services/development-tools/
    - isort --check-only services/development-tools/
    - mypy services/development-tools/
  only:
    changes:
      - services/development-tools/**/*
      - requirements.txt

# Rust代码质量检查
lint:rust:
  stage: validate
  image: rust:${RUST_VERSION}-slim
  before_script:
    - rustup component add clippy rustfmt
  script:
    - cd services/map-editor
    - cargo fmt -- --check
    - cargo clippy -- -D warnings
  only:
    changes:
      - services/map-editor/**/*
      - Cargo.toml

# SonarQube代码质量分析
sonarqube-check:
  stage: validate
  image: sonarsource/sonar-scanner-cli:latest
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"
    GIT_DEPTH: "0"
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache
  script:
    - sonar-scanner
  allow_failure: true
  only:
    - main
    - develop

# 前端构建
build:frontend:
  stage: build
  image: node:${NODE_VERSION}-alpine
  before_script:
    - cd frontend
    - npm ci
  script:
    - npm run build
  artifacts:
    paths:
      - frontend/dist/
    expire_in: 1 hour
  only:
    changes:
      - frontend/**/*

# Go服务构建
build:go:
  stage: build
  image: golang:${GO_VERSION}-alpine
  before_script:
    - apk add --no-cache git
  script:
    - cd services/system-management
    - go mod download
    - CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o bin/system-management ./cmd/main.go
  artifacts:
    paths:
      - services/system-management/bin/
    expire_in: 1 hour
  only:
    changes:
      - services/system-management/**/*

# Python服务构建
build:python:
  stage: build
  image: python:${PYTHON_VERSION}-slim
  before_script:
    - cd services/development-tools
    - pip install -r requirements.txt
  script:
    - python -m py_compile src/**/*.py
    - pip install build
    - python -m build
  artifacts:
    paths:
      - services/development-tools/dist/
    expire_in: 1 hour
  only:
    changes:
      - services/development-tools/**/*

# Rust服务构建
build:rust:
  stage: build
  image: rust:${RUST_VERSION}-slim
  script:
    - cd services/map-editor
    - cargo build --release
  artifacts:
    paths:
      - services/map-editor/target/release/
    expire_in: 1 hour
  only:
    changes:
      - services/map-editor/**/*

# 前端单元测试
test:frontend:
  stage: test
  image: node:${NODE_VERSION}-alpine
  before_script:
    - cd frontend
    - npm ci
  script:
    - npm run test:coverage
  coverage: '/All files[^|]*\|[^|]*\s+([\d\.]+)/'
  artifacts:
    reports:
      junit: frontend/test-results/junit.xml
      coverage_report:
        coverage_format: cobertura
        path: frontend/coverage/cobertura-coverage.xml
  only:
    changes:
      - frontend/**/*

# Go单元测试
test:go:
  stage: test
  image: golang:${GO_VERSION}-alpine
  before_script:
    - apk add --no-cache git
  script:
    - cd services/system-management
    - go test -v -race -coverprofile=coverage.out ./...
    - go tool cover -html=coverage.out -o coverage.html
  coverage: '/coverage: \d+\.\d+% of statements/'
  artifacts:
    reports:
      junit: services/system-management/report.xml
      coverage_report:
        coverage_format: cobertura
        path: services/system-management/coverage.xml
  only:
    changes:
      - services/system-management/**/*

# Python单元测试
test:python:
  stage: test
  image: python:${PYTHON_VERSION}-slim
  before_script:
    - cd services/development-tools
    - pip install -r requirements.txt
    - pip install pytest pytest-cov pytest-xdist
  script:
    - pytest --cov=src --cov-report=xml --cov-report=html --junitxml=report.xml
  coverage: '/(?i)total.*? (100(?:\.0+)?\%|[1-9]?\d(?:\.\d+)?\%)$/'
  artifacts:
    reports:
      junit: services/development-tools/report.xml
      coverage_report:
        coverage_format: cobertura
        path: services/development-tools/coverage.xml
  only:
    changes:
      - services/development-tools/**/*

# 安全扫描
security:dependency-scan:
  stage: security
  image: owasp/dependency-check:latest
  script:
    - /usr/share/dependency-check/bin/dependency-check.sh --scan . --format XML --out dependency-check-report.xml
  artifacts:
    reports:
      dependency_scanning: dependency-check-report.xml
  allow_failure: true

# Docker镜像构建
package:docker:
  stage: package
  image: docker:latest
  services:
    - docker:dind
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
  script:
    - docker build -t $CI_REGISTRY_IMAGE/frontend:$CI_COMMIT_SHA -f frontend/Dockerfile frontend/
    - docker build -t $CI_REGISTRY_IMAGE/system-management:$CI_COMMIT_SHA -f services/system-management/Dockerfile services/system-management/
    - docker build -t $CI_REGISTRY_IMAGE/development-tools:$CI_COMMIT_SHA -f services/development-tools/Dockerfile services/development-tools/
    - docker push $CI_REGISTRY_IMAGE/frontend:$CI_COMMIT_SHA
    - docker push $CI_REGISTRY_IMAGE/system-management:$CI_COMMIT_SHA
    - docker push $CI_REGISTRY_IMAGE/development-tools:$CI_COMMIT_SHA
  only:
    - main
    - develop

# 开发环境部署
deploy:development:
  stage: deploy
  image: bitnami/kubectl:latest
  before_script:
    - kubectl config use-context $KUBE_CONTEXT_DEV
  script:
    - kubectl set image deployment/frontend frontend=$CI_REGISTRY_IMAGE/frontend:$CI_COMMIT_SHA -n development
    - kubectl set image deployment/system-management system-management=$CI_REGISTRY_IMAGE/system-management:$CI_COMMIT_SHA -n development
    - kubectl rollout status deployment/frontend -n development
    - kubectl rollout status deployment/system-management -n development
  environment:
    name: development
    url: https://dev.autonomous-driving-platform.com
  only:
    - develop

# 生产环境部署
deploy:production:
  stage: deploy
  image: bitnami/kubectl:latest
  before_script:
    - kubectl config use-context $KUBE_CONTEXT_PROD
  script:
    - kubectl set image deployment/frontend frontend=$CI_REGISTRY_IMAGE/frontend:$CI_COMMIT_SHA -n production
    - kubectl set image deployment/system-management system-management=$CI_REGISTRY_IMAGE/system-management:$CI_COMMIT_SHA -n production
    - kubectl rollout status deployment/frontend -n production
    - kubectl rollout status deployment/system-management -n production
  environment:
    name: production
    url: https://autonomous-driving-platform.com
  when: manual
  only:
    - main

# 集成测试
test:integration:
  stage: test
  image: docker:latest
  services:
    - docker:dind
  before_script:
    - docker-compose -f docker-compose.test.yml up -d
    - sleep 30  # 等待服务启动
  script:
    - docker-compose -f docker-compose.test.yml exec -T test-runner npm run test:integration
  after_script:
    - docker-compose -f docker-compose.test.yml down
  artifacts:
    reports:
      junit: integration-tests/reports/integration-results.xml
  only:
    - main
    - develop
    - merge_requests

# 性能测试
test:performance:
  stage: test
  image: loadimpact/k6:latest
  script:
    - k6 run --out junit=performance-results.xml performance-tests/load-test.js
  artifacts:
    reports:
      junit: performance-results.xml
  only:
    - main
    - develop
  when: manual

# 容器安全扫描
security:container-scan:
  stage: security
  image: aquasec/trivy:latest
  script:
    - trivy image --format template --template "@contrib/gitlab.tpl" -o gl-container-scanning-report.json $CI_REGISTRY_IMAGE/frontend:$CI_COMMIT_SHA
  artifacts:
    reports:
      container_scanning: gl-container-scanning-report.json
  dependencies:
    - package:docker
  only:
    - main
    - develop

# 部署通知
notify:deployment:
  stage: deploy
  image: alpine:latest
  before_script:
    - apk add --no-cache curl
  script:
    - |
      curl -X POST -H 'Content-type: application/json' \
      --data "{\"text\":\"🚀 部署完成: ${CI_PROJECT_NAME} - ${CI_COMMIT_REF_NAME} (${CI_COMMIT_SHORT_SHA}) 到 ${CI_ENVIRONMENT_NAME}\"}" \
      $SLACK_WEBHOOK_URL
  only:
    - main
    - develop
  when: on_success

# 清理任务
cleanup:
  stage: .post
  image: docker:latest
  services:
    - docker:dind
  script:
    - docker system prune -f
  when: always
