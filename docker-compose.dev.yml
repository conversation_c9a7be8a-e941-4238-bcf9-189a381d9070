# 自动驾驶开发加速系统 - 开发环境 Docker Compose 配置
# 
# 本文件定义了开发环境所需的所有基础服务
# 包括数据库、缓存、消息队列、监控等组件

version: '3.8'

services:
  # ============================================================================
  # 数据库服务
  # ============================================================================
  
  # PostgreSQL 主数据库
  postgres:
    image: postgres:14-alpine
    container_name: autodriving-postgres
    environment:
      POSTGRES_DB: autodriving
      POSTGRES_USER: autodriving
      POSTGRES_PASSWORD: autodriving123
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - autodriving-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U autodriving -d autodriving"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis 缓存服务
  redis:
    image: redis:7-alpine
    container_name: autodriving-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./deployment/docker/redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - autodriving-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MongoDB 文档数据库（用于存储地图数据等）
  mongodb:
    image: mongo:6.0
    container_name: autodriving-mongodb
    environment:
      MONGO_INITDB_ROOT_USERNAME: autodriving
      MONGO_INITDB_ROOT_PASSWORD: autodriving123
      MONGO_INITDB_DATABASE: autodriving
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./scripts/init-mongo.js:/docker-entrypoint-initdb.d/init-mongo.js
    networks:
      - autodriving-network
    restart: unless-stopped
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongosh localhost:27017/autodriving --quiet
      interval: 30s
      timeout: 10s
      retries: 3

  # ============================================================================
  # 消息队列和通信服务
  # ============================================================================

  # Apache Kafka 消息队列
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: autodriving-zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - autodriving-network
    restart: unless-stopped

  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: autodriving-kafka
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: true
    networks:
      - autodriving-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "kafka-broker-api-versions", "--bootstrap-server", "localhost:9092"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ============================================================================
  # 监控和日志服务
  # ============================================================================

  # Prometheus 监控服务
  prometheus:
    image: prom/prometheus:v2.45.0
    container_name: autodriving-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./deployment/docker/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - autodriving-network
    restart: unless-stopped

  # Grafana 可视化服务
  grafana:
    image: grafana/grafana:10.0.0
    container_name: autodriving-grafana
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD: autodriving123
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./deployment/docker/grafana/provisioning:/etc/grafana/provisioning
      - ./deployment/docker/grafana/dashboards:/var/lib/grafana/dashboards
    networks:
      - autodriving-network
    restart: unless-stopped
    depends_on:
      - prometheus

  # Elasticsearch 日志存储
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:8.8.0
    container_name: autodriving-elasticsearch
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    ports:
      - "9200:9200"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - autodriving-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Kibana 日志可视化
  kibana:
    image: docker.elastic.co/kibana/kibana:8.8.0
    container_name: autodriving-kibana
    ports:
      - "5601:5601"
    environment:
      ELASTICSEARCH_HOSTS: http://elasticsearch:9200
    networks:
      - autodriving-network
    restart: unless-stopped
    depends_on:
      - elasticsearch

  # ============================================================================
  # 开发工具服务
  # ============================================================================

  # MinIO 对象存储（用于文件存储）
  minio:
    image: minio/minio:RELEASE.2023-07-07T07-13-57Z
    container_name: autodriving-minio
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      MINIO_ROOT_USER: autodriving
      MINIO_ROOT_PASSWORD: autodriving123
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    networks:
      - autodriving-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Jaeger 分布式链路追踪
  jaeger:
    image: jaegertracing/all-in-one:1.46
    container_name: autodriving-jaeger
    ports:
      - "16686:16686"
      - "14268:14268"
    environment:
      COLLECTOR_OTLP_ENABLED: true
    networks:
      - autodriving-network
    restart: unless-stopped

  # ============================================================================
  # API网关
  # ============================================================================

  # Nginx API网关
  nginx:
    image: nginx:1.25-alpine
    container_name: autodriving-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./deployment/docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./deployment/docker/nginx/conf.d:/etc/nginx/conf.d
      - ./deployment/docker/nginx/ssl:/etc/nginx/ssl
    networks:
      - autodriving-network
    restart: unless-stopped
    depends_on:
      - postgres
      - redis
      - mongodb

  # ============================================================================
  # 开发辅助工具
  # ============================================================================

  # pgAdmin PostgreSQL管理工具
  pgadmin:
    image: dpage/pgadmin4:7.4
    container_name: autodriving-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: autodriving123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - autodriving-network
    restart: unless-stopped
    depends_on:
      - postgres

  # Redis Commander Redis管理工具
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: autodriving-redis-commander
    environment:
      REDIS_HOSTS: local:redis:6379
    ports:
      - "8081:8081"
    networks:
      - autodriving-network
    restart: unless-stopped
    depends_on:
      - redis

  # Mongo Express MongoDB管理工具
  mongo-express:
    image: mongo-express:1.0.0-alpha
    container_name: autodriving-mongo-express
    environment:
      ME_CONFIG_MONGODB_ADMINUSERNAME: autodriving
      ME_CONFIG_MONGODB_ADMINPASSWORD: autodriving123
      ME_CONFIG_MONGODB_URL: **************************************************/
      ME_CONFIG_BASICAUTH_USERNAME: admin
      ME_CONFIG_BASICAUTH_PASSWORD: autodriving123
    ports:
      - "8082:8081"
    networks:
      - autodriving-network
    restart: unless-stopped
    depends_on:
      - mongodb

# ============================================================================
# 网络配置
# ============================================================================
networks:
  autodriving-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# ============================================================================
# 数据卷配置
# ============================================================================
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  mongodb_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  elasticsearch_data:
    driver: local
  minio_data:
    driver: local
  pgadmin_data:
    driver: local
