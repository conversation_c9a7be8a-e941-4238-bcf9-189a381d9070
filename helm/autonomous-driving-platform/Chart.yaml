# 自动驾驶开发加速系统 - Helm Chart 配置
apiVersion: v2
name: autonomous-driving-platform
description: 自动驾驶开发加速系统 - 完整的开发、仿真和部署平台
type: application
version: 1.0.0
appVersion: "1.0.0"
home: https://github.com/company/autonomous-driving-platform
sources:
  - https://github.com/company/autonomous-driving-platform
maintainers:
  - name: Autonomous Driving Team
    email: <EMAIL>
keywords:
  - autonomous-driving
  - simulation
  - development-tools
  - map-editor
  - ci-cd
dependencies:
  - name: postgresql
    version: 12.12.10
    repository: https://charts.bitnami.com/bitnami
    condition: postgresql.enabled
  - name: redis
    version: 18.1.5
    repository: https://charts.bitnami.com/bitnami
    condition: redis.enabled
  - name: prometheus
    version: 25.1.0
    repository: https://prometheus-community.github.io/helm-charts
    condition: monitoring.prometheus.enabled
  - name: grafana
    version: 7.0.3
    repository: https://grafana.github.io/helm-charts
    condition: monitoring.grafana.enabled
  - name: elasticsearch
    version: 8.5.1
    repository: https://helm.elastic.co
    condition: logging.elasticsearch.enabled
  - name: kibana
    version: 8.5.1
    repository: https://helm.elastic.co
    condition: logging.kibana.enabled
  - name: jaeger
    version: 0.71.14
    repository: https://jaegertracing.github.io/helm-charts
    condition: tracing.jaeger.enabled
annotations:
  category: Development Platform
  licenses: MIT
