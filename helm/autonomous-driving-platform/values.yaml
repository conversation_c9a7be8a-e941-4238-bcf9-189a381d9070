# 自动驾驶开发加速系统 - Helm Values 配置
# 全局配置
global:
  imageRegistry: harbor.company.com
  imagePullSecrets:
    - name: harbor-secret
  storageClass: fast-ssd

# 镜像配置
image:
  registry: harbor.company.com
  namespace: autonomous-driving
  tag: latest
  pullPolicy: IfNotPresent

# 前端应用配置
frontend:
  enabled: true
  replicaCount: 2
  image:
    repository: frontend
    tag: ""
  service:
    type: ClusterIP
    port: 80
    targetPort: 3000
  ingress:
    enabled: true
    className: nginx
    annotations:
      nginx.ingress.kubernetes.io/rewrite-target: /
      nginx.ingress.kubernetes.io/ssl-redirect: "true"
    hosts:
      - host: autonomous-driving.company.com
        paths:
          - path: /
            pathType: Prefix
    tls:
      - secretName: autonomous-driving-tls
        hosts:
          - autonomous-driving.company.com
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 250m
      memory: 256Mi
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 10
    targetCPUUtilizationPercentage: 80

# 系统管理服务配置
systemManagement:
  enabled: true
  replicaCount: 2
  image:
    repository: system-management
    tag: ""
  service:
    type: ClusterIP
    port: 8080
    targetPort: 8080
  resources:
    limits:
      cpu: 1000m
      memory: 1Gi
    requests:
      cpu: 500m
      memory: 512Mi
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 5
    targetCPUUtilizationPercentage: 80
  env:
    LOG_LEVEL: info
    GIN_MODE: release

# 开发工具服务配置
developmentTools:
  enabled: true
  replicaCount: 2
  image:
    repository: development-tools
    tag: ""
  service:
    type: ClusterIP
    port: 8080
    targetPort: 8080
  resources:
    limits:
      cpu: 1000m
      memory: 1Gi
    requests:
      cpu: 500m
      memory: 512Mi
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 5
    targetCPUUtilizationPercentage: 80

# 仿真集成服务配置
simulationIntegration:
  enabled: true
  replicaCount: 1
  image:
    repository: simulation-integration
    tag: ""
  service:
    type: ClusterIP
    port: 8080
    targetPort: 8080
  resources:
    limits:
      cpu: 2000m
      memory: 4Gi
    requests:
      cpu: 1000m
      memory: 2Gi
  env:
    DDS_DOMAIN_ID: 42
    CARLA_HOST: carla-simulator
    CARLA_PORT: 2000

# 地图编辑服务配置
mapEditor:
  enabled: true
  replicaCount: 2
  image:
    repository: map-editor
    tag: ""
  service:
    type: ClusterIP
    port: 8080
    targetPort: 8080
  resources:
    limits:
      cpu: 1000m
      memory: 2Gi
    requests:
      cpu: 500m
      memory: 1Gi
  autoscaling:
    enabled: true
    minReplicas: 2
    maxReplicas: 5
    targetCPUUtilizationPercentage: 80

# API网关配置
apiGateway:
  enabled: true
  replicaCount: 2
  image:
    repository: openresty/openresty
    tag: alpine
  service:
    type: LoadBalancer
    port: 80
    targetPort: 80
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 250m
      memory: 256Mi

# PostgreSQL数据库配置
postgresql:
  enabled: true
  auth:
    postgresPassword: "autonomous-driving-password"
    username: "ad_user"
    password: "ad_password"
    database: "autonomous_driving"
  primary:
    persistence:
      enabled: true
      size: 20Gi
      storageClass: fast-ssd
    resources:
      limits:
        cpu: 1000m
        memory: 2Gi
      requests:
        cpu: 500m
        memory: 1Gi
  metrics:
    enabled: true

# Redis缓存配置
redis:
  enabled: true
  auth:
    enabled: true
    password: "redis-password"
  master:
    persistence:
      enabled: true
      size: 8Gi
      storageClass: fast-ssd
    resources:
      limits:
        cpu: 500m
        memory: 1Gi
      requests:
        cpu: 250m
        memory: 512Mi
  metrics:
    enabled: true

# 监控配置
monitoring:
  prometheus:
    enabled: true
    server:
      persistentVolume:
        enabled: true
        size: 20Gi
        storageClass: fast-ssd
      resources:
        limits:
          cpu: 1000m
          memory: 2Gi
        requests:
          cpu: 500m
          memory: 1Gi
  grafana:
    enabled: true
    persistence:
      enabled: true
      size: 10Gi
      storageClass: fast-ssd
    adminPassword: "grafana-admin-password"
    resources:
      limits:
        cpu: 500m
        memory: 1Gi
      requests:
        cpu: 250m
        memory: 512Mi

# 日志配置
logging:
  elasticsearch:
    enabled: true
    master:
      persistence:
        enabled: true
        size: 30Gi
        storageClass: fast-ssd
      resources:
        limits:
          cpu: 1000m
          memory: 2Gi
        requests:
          cpu: 500m
          memory: 1Gi
  kibana:
    enabled: true
    resources:
      limits:
        cpu: 500m
        memory: 1Gi
      requests:
        cpu: 250m
        memory: 512Mi

# 链路追踪配置
tracing:
  jaeger:
    enabled: true
    storage:
      type: elasticsearch
    resources:
      limits:
        cpu: 500m
        memory: 1Gi
      requests:
        cpu: 250m
        memory: 512Mi

# 存储配置
storage:
  minio:
    enabled: true
    mode: standalone
    persistence:
      enabled: true
      size: 100Gi
      storageClass: standard
    resources:
      limits:
        cpu: 500m
        memory: 1Gi
      requests:
        cpu: 250m
        memory: 512Mi

# 安全配置
security:
  networkPolicies:
    enabled: true
  podSecurityPolicy:
    enabled: true
  rbac:
    enabled: true

# 环境特定配置
environment: development

# 节点选择器
nodeSelector: {}

# 容忍度
tolerations: []

# 亲和性
affinity: {}

# 服务账户
serviceAccount:
  create: true
  annotations: {}
  name: ""
